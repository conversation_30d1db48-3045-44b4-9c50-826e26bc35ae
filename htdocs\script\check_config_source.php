<?php
/**
 * 配置來源檢查工具
 * 確認系統讀取的是哪個配置文件
 */

// 定義常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置來源檢查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .config-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 配置來源檢查</h1>
        
        <div class="info">
            <strong>檢查目的：</strong>確認系統是否正確讀取外部敏感配置文件
        </div>

        <?php
        echo "<h2>1. 檢查配置文件存在性</h2>";
        
        $configFiles = [
            'htdocs/config/config.production.php' => __DIR__ . '/config/config.production.php',
            '../config/secrets.production.php' => dirname(__DIR__) . '/config/secrets.production.php',
            'htdocs/config/environment.php' => __DIR__ . '/config/environment.php'
        ];
        
        echo "<table>";
        echo "<tr><th>配置文件</th><th>路徑</th><th>狀態</th><th>大小</th></tr>";
        
        foreach ($configFiles as $name => $path) {
            $exists = file_exists($path);
            $size = $exists ? filesize($path) : 0;
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td>" . htmlspecialchars($path) . "</td>";
            echo "<td class='" . ($exists ? 'success' : 'error') . "'>" . ($exists ? '✅ 存在' : '❌ 不存在') . "</td>";
            echo "<td>" . ($exists ? number_format($size) . ' bytes' : 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>2. 載入環境配置並檢查來源</h2>";
        
        try {
            // 載入環境配置
            require_once __DIR__ . '/config/environment.php';
            
            echo "<div class='config-box'>";
            echo "<h3>環境配置載入成功</h3>";
            echo "<p><strong>當前環境：</strong> " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "</p>";
            echo "<p><strong>除錯模式：</strong> " . (defined('DEBUG_MODE') && DEBUG_MODE ? '開啟' : '關閉') . "</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>環境配置載入失敗：" . $e->getMessage() . "</div>";
        }
        
        echo "<h2>3. 檢查資料庫配置來源</h2>";
        
        echo "<table>";
        echo "<tr><th>配置項目</th><th>當前值</th><th>來源分析</th></tr>";
        
        $dbConfigs = [
            'DB_HOST' => defined('DB_HOST') ? DB_HOST : '未定義',
            'DB_NAME' => defined('DB_NAME') ? DB_NAME : '未定義',
            'DB_USER' => defined('DB_USER') ? DB_USER : '未定義',
            'DB_PASS' => defined('DB_PASS') ? '***已設定***' : '未定義'
        ];
        
        foreach ($dbConfigs as $key => $value) {
            echo "<tr>";
            echo "<td><strong>$key</strong></td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            
            // 分析來源
            $source = '未知';
            if ($key === 'DB_PASS' && defined('DB_PASS')) {
                if (defined('DB_PASSWORD_PRODUCTION') && DB_PASS === DB_PASSWORD_PRODUCTION) {
                    $source = '<span class="success">✅ 來自外部 secrets.production.php</span>';
                } else {
                    $source = '<span class="warning">⚠️ 來自 htdocs/config 內部配置</span>';
                }
            } elseif (defined($key)) {
                $source = '<span class="info">ℹ️ 已定義</span>';
            }
            
            echo "<td>$source</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>4. 檢查外部敏感配置載入狀況</h2>";
        
        $secretsFile = dirname(__DIR__) . '/config/secrets.production.php';
        if (file_exists($secretsFile)) {
            echo "<div class='success'>✅ 外部敏感配置文件存在</div>";
            
            // 檢查是否已載入
            $secretConstants = [
                'DB_PASSWORD_PRODUCTION',
                'JWT_SECRET_KEY',
                'ENCRYPTION_KEY',
                'API_SECRET_KEY'
            ];
            
            echo "<table>";
            echo "<tr><th>敏感配置常數</th><th>載入狀態</th><th>值狀態</th></tr>";
            
            foreach ($secretConstants as $constant) {
                $defined = defined($constant);
                $value = $defined ? constant($constant) : '未定義';
                $isDefault = strpos($value, 'your_') === 0;
                
                echo "<tr>";
                echo "<td><strong>$constant</strong></td>";
                echo "<td class='" . ($defined ? 'success' : 'error') . "'>" . ($defined ? '✅ 已載入' : '❌ 未載入') . "</td>";
                
                if ($defined) {
                    if ($isDefault) {
                        echo "<td class='warning'>⚠️ 使用預設值</td>";
                    } else {
                        echo "<td class='success'>✅ 已設定實際值</td>";
                    }
                } else {
                    echo "<td class='error'>❌ 未定義</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<div class='error'>❌ 外部敏感配置文件不存在</div>";
        }
        
        echo "<h2>5. 資料庫連接測試</h2>";
        
        try {
            if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "<div class='success'>✅ 資料庫連接成功</div>";
                echo "<div class='config-box'>";
                echo "<p><strong>連接資訊：</strong></p>";
                echo "<p>主機：" . DB_HOST . "</p>";
                echo "<p>資料庫：" . DB_NAME . "</p>";
                echo "<p>用戶：" . DB_USER . "</p>";
                echo "<p>密碼：" . (defined('DB_PASSWORD_PRODUCTION') && DB_PASS === DB_PASSWORD_PRODUCTION ? 
                    '<span class="success">使用外部敏感配置</span>' : 
                    '<span class="warning">使用內部配置</span>') . "</p>";
                echo "</div>";
                
            } else {
                echo "<div class='error'>❌ 資料庫配置不完整</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='error'>❌ 資料庫連接失敗：" . $e->getMessage() . "</div>";
        }
        
        echo "<h2>6. 建議操作</h2>";
        
        if (defined('DB_PASSWORD_PRODUCTION') && defined('DB_PASS') && DB_PASS === DB_PASSWORD_PRODUCTION) {
            echo "<div class='success'>";
            echo "<h3>✅ 配置正確</h3>";
            echo "<p>系統正在使用外部敏感配置文件中的資料庫密碼。</p>";
            echo "<p><strong>可以安全地保留 htdocs/config/config.php</strong> 作為開發環境的配置。</p>";
            echo "</div>";
        } else {
            echo "<div class='warning'>";
            echo "<h3>⚠️ 需要檢查</h3>";
            echo "<p>系統可能沒有正確載入外部敏感配置。</p>";
            echo "<p>請檢查 config.production.php 中的載入邏輯。</p>";
            echo "</div>";
        }
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="test_basic.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">基本測試</a>
            <a href="deployment_check.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">完整檢查</a>
        </div>
    </div>
</body>
</html>
