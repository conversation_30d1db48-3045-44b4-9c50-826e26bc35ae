<?php
/**
 * 調試登入處理過程
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

echo "=== 登入處理過程調試 ===\n";

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "1. 環境配置載入完成\n";
echo "   當前環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "   LOG_ERRORS: " . (defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義') . "\n";
echo "   LOG_PATH: " . (defined('LOG_PATH') ? LOG_PATH : '未定義') . "\n";

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';
echo "2. 共用函數載入完成\n";

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';
echo "3. 核心類別載入完成\n";

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();
echo "4. 核心物件初始化完成\n";

// 檢查 Auth 類別的方法
echo "5. Auth 類別方法檢查:\n";
$reflection = new ReflectionClass($auth);
$methods = $reflection->getMethods();
foreach ($methods as $method) {
    if ($method->getName() === 'login' || $method->getName() === 'logActivity') {
        echo "   - " . $method->getName() . " 方法存在\n";
    }
}

// 模擬 pages/login.php 的登入處理
echo "\n=== 模擬 pages/login.php 登入處理 ===\n";

// 模擬 POST 請求
$_POST['email'] = '<EMAIL>';
$_POST['password'] = 'wrong_password_from_pages';

$email = $_POST['email'] ?? '';
$password = $_POST['password'] ?? '';
$rememberMe = isset($_POST['remember_me']);

echo "6. 模擬 POST 資料:\n";
echo "   Email: " . $email . "\n";
echo "   Password: [隱藏]\n";
echo "   Remember Me: " . ($rememberMe ? 'true' : 'false') . "\n";

if (empty($email) || empty($password)) {
    echo "7. 驗證失敗：空的 email 或密碼\n";
} else {
    echo "7. 開始呼叫 auth->login()\n";
    
    // 記錄呼叫前的日誌狀態
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "   呼叫前日誌文件大小: " . $beforeSize . " bytes\n";
    
    $result = $auth->login($email, $password, $rememberMe);
    
    echo "8. auth->login() 呼叫完成\n";
    echo "   結果: " . ($result['success'] ? '成功' : '失敗') . "\n";
    echo "   訊息: " . $result['message'] . "\n";
    
    // 記錄呼叫後的日誌狀態
    $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "   呼叫後日誌文件大小: " . $afterSize . " bytes\n";
    echo "   日誌文件是否增長: " . ($afterSize > $beforeSize ? '是' : '否') . "\n";
    
    if ($result['success']) {
        echo "9. 登入成功處理\n";
    } else {
        echo "9. 登入失敗處理\n";
        $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
        echo "   錯誤訊息: " . $error . "\n";
    }
}

// 檢查最新的日誌記錄
echo "\n=== 檢查最新日誌記錄 ===\n";
if (file_exists($authLogFile)) {
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    $lastLine = end($lines);
    
    if (!empty($lastLine)) {
        $data = json_decode($lastLine, true);
        if ($data && $data['user_email'] === $email) {
            echo "✅ 找到對應的日誌記錄\n";
            echo "   時間: " . $data['timestamp'] . "\n";
            echo "   動作: " . $data['action'] . "\n";
            echo "   描述: " . $data['description'] . "\n";
        } else {
            echo "❌ 沒有找到對應的日誌記錄\n";
            echo "   最後一行: " . $lastLine . "\n";
            if ($data) {
                echo "   最後記錄的 email: " . ($data['user_email'] ?? '無') . "\n";
            }
        }
    } else {
        echo "❌ 日誌文件為空\n";
    }
} else {
    echo "❌ 日誌文件不存在\n";
}

echo "\n調試完成！\n";
?>
