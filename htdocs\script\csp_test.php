<?php
/**
 * CSP測試頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP測試頁面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-item { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">CSP和CDN測試頁面</h1>
        
        <div id="results">
            <div class="test-item" id="tailwind-test">
                <h3>Tailwind CSS測試</h3>
                <p>如果這個文字有樣式，表示Tailwind CSS加載成功</p>
            </div>
            
            <div class="test-item" id="qrcode-test">
                <h3>QRCode庫測試</h3>
                <p>QRCode庫狀態: <span id="qrcode-status">檢測中...</span></p>
            </div>
            
            <div class="test-item" id="html5qrcode-test">
                <h3>Html5Qrcode庫測試</h3>
                <p>Html5Qrcode庫狀態: <span id="html5qrcode-status">檢測中...</span></p>
            </div>
        </div>
        
        <div class="mt-6">
            <h2 class="text-2xl font-bold mb-4">測試結果</h2>
            <div id="test-summary"></div>
        </div>
    </div>

    <!-- 測試外部CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"></script>
    
    <script>
        // 測試Tailwind CSS
        if (typeof tailwind !== 'undefined') {
            document.getElementById('tailwind-test').className += ' success';
        } else {
            document.getElementById('tailwind-test').className += ' error';
        }
        
        // 測試QRCode庫
        setTimeout(function() {
            const qrcodeStatus = document.getElementById('qrcode-status');
            const qrcodeTest = document.getElementById('qrcode-test');
            
            if (typeof QRCode !== 'undefined') {
                qrcodeStatus.textContent = '✅ 加載成功';
                qrcodeTest.className += ' success';
            } else {
                qrcodeStatus.textContent = '❌ 加載失敗';
                qrcodeTest.className += ' error';
            }
            
            // 測試Html5Qrcode庫
            const html5qrcodeStatus = document.getElementById('html5qrcode-status');
            const html5qrcodeTest = document.getElementById('html5qrcode-test');
            
            if (typeof Html5Qrcode !== 'undefined') {
                html5qrcodeStatus.textContent = '✅ 加載成功';
                html5qrcodeTest.className += ' success';
            } else {
                html5qrcodeStatus.textContent = '❌ 加載失敗';
                html5qrcodeTest.className += ' error';
            }
            
            // 生成測試總結
            const summary = document.getElementById('test-summary');
            const successCount = document.querySelectorAll('.success').length;
            const totalTests = 3;
            
            if (successCount === totalTests) {
                summary.innerHTML = '<div class="test-item success"><strong>✅ 所有測試通過</strong><br>CSP配置正確，所有外部資源都能正常加載。</div>';
            } else {
                summary.innerHTML = '<div class="test-item error"><strong>❌ 部分測試失敗</strong><br>通過: ' + successCount + '/' + totalTests + '<br>請檢查CSP配置或網路連接。</div>';
            }
        }, 1000);
    </script>
</body>
</html>