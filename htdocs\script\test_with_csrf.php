<?php
/**
 * 包含 CSRF token 的 AJAX 登入測試
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

// 初始化 Auth 物件
$auth = new Auth();

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>包含 CSRF Token 的 AJAX 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .csrf-info {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 包含 CSRF Token 的 AJAX 測試</h1>
        
        <div class="csrf-info">
            <strong>CSRF Token 信息：</strong><br>
            Session ID: <?= session_id() ?><br>
            CSRF Token: <?= $_SESSION[CSRF_TOKEN_NAME] ?? '未生成' ?><br>
            Token 長度: <?= isset($_SESSION[CSRF_TOKEN_NAME]) ? strlen($_SESSION[CSRF_TOKEN_NAME]) : 0 ?> 字符
        </div>
        
        <div class="result info">
測試說明：
1. 這個頁面包含有效的 CSRF token
2. 會向 pages/login.php 發送完整的 AJAX 請求
3. 檢查響應中的 debug_info 來確認日誌寫入情況
4. 同時可以開啟 monitor_real_login.php 來實時監控日誌變化
        </div>

        <form id="testForm">
            <?php generate_csrf_token_field(); ?>
            
            <div class="form-group">
                <label for="email">電子郵件：</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">密碼：</label>
                <input type="password" id="password" name="password" value="test123" required>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="remember_me" name="remember_me"> 記住我
                </label>
            </div>

            <button type="submit" class="btn" id="submitBtn">🚀 發送 AJAX 請求（含 CSRF）</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        // 自動生成測試郵箱
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('email');
            const timestamp = Math.floor(Date.now() / 1000);
            emailInput.value = 'csrf_test_' + timestamp + '@example.com';
        });

        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用按鈕
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 發送中...';
            
            // 清除之前的結果
            resultDiv.innerHTML = '';
            
            // 準備表單數據
            const formData = new FormData();
            formData.append('ajax_login', '1');
            formData.append('email', document.getElementById('email').value);
            formData.append('password', document.getElementById('password').value);
            
            if (document.getElementById('remember_me').checked) {
                formData.append('remember_me', '1');
            }
            
            // 添加 CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]');
            if (csrfToken) {
                formData.append('csrf_token', csrfToken.value);
                console.log('CSRF Token 已添加:', csrfToken.value.substring(0, 10) + '...');
            } else {
                console.warn('未找到 CSRF Token');
            }
            
            const startTime = new Date();
            
            console.log('發送 AJAX 請求到 pages/login.php');
            console.log('POST 數據:', {
                ajax_login: '1',
                email: document.getElementById('email').value,
                password: '***',
                csrf_token: csrfToken ? csrfToken.value.substring(0, 10) + '...' : 'N/A'
            });
            
            // 發送 AJAX 請求
            fetch('pages/login.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                const endTime = new Date();
                const duration = endTime - startTime;
                
                console.log('響應狀態:', response.status);
                console.log('響應標頭:', response.headers.get('content-type'));
                console.log('響應時間:', duration + 'ms');
                
                return response.text().then(text => {
                    return {
                        status: response.status,
                        contentType: response.headers.get('content-type'),
                        text: text,
                        duration: duration
                    };
                });
            })
            .then(data => {
                console.log('原始響應:', data.text);
                
                let resultText = '';
                resultText += '=== AJAX 請求結果 ===\n';
                resultText += '時間: ' + new Date().toISOString() + '\n';
                resultText += '狀態碼: ' + data.status + '\n';
                resultText += '內容類型: ' + data.contentType + '\n';
                resultText += '響應時間: ' + data.duration + 'ms\n\n';
                
                // 嘗試解析 JSON 響應
                try {
                    const jsonData = JSON.parse(data.text);
                    
                    resultText += '=== JSON 解析成功 ===\n';
                    resultText += '成功: ' + (jsonData.success ? '是' : '否') + '\n';
                    resultText += '訊息: ' + (jsonData.message || 'N/A') + '\n';
                    
                    if (jsonData.debug_info) {
                        const debugInfo = jsonData.debug_info;
                        const sizeChange = debugInfo.after_size - debugInfo.before_size;
                        
                        resultText += '\n=== 日誌寫入檢查 ===\n';
                        resultText += '認證日誌檔案: ' + debugInfo.auth_log_file + '\n';
                        resultText += '登入前大小: ' + debugInfo.before_size + ' bytes\n';
                        resultText += '登入後大小: ' + debugInfo.after_size + ' bytes\n';
                        resultText += '大小變化: ' + sizeChange + ' bytes\n';
                        resultText += '日誌路徑: ' + debugInfo.log_path + '\n';
                        
                        if (sizeChange > 0) {
                            resultText += '\n✅ 日誌寫入成功！\n';
                            resultDiv.className = 'result success';
                        } else {
                            resultText += '\n❌ 日誌寫入失敗！\n';
                            resultDiv.className = 'result error';
                        }
                    }
                    
                    resultText += '\n=== 完整響應數據 ===\n';
                    resultText += JSON.stringify(jsonData, null, 2);
                    
                } catch (e) {
                    resultText += '=== JSON 解析失敗 ===\n';
                    resultText += '錯誤: ' + e.message + '\n\n';
                    resultText += '=== 原始響應 ===\n';
                    resultText += data.text;
                    
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = resultText;
                if (!resultDiv.className) {
                    resultDiv.className = 'result info';
                }
            })
            .catch(error => {
                console.error('AJAX 錯誤:', error);
                
                let resultText = '';
                resultText += '=== AJAX 請求失敗 ===\n';
                resultText += '錯誤: ' + error.message + '\n';
                resultText += '時間: ' + new Date().toISOString() + '\n';
                
                resultDiv.textContent = resultText;
                resultDiv.className = 'result error';
            })
            .finally(() => {
                // 重新啟用按鈕
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 發送 AJAX 請求（含 CSRF）';
            });
        });
    </script>
</body>
</html>
