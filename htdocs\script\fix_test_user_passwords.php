<?php
/**
 * 修復測試用戶密碼
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 修復測試用戶密碼 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 連接資料庫
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ 資料庫連接成功\n\n";
    
    // 定義測試用戶和密碼
    $testUsers = [
        ['email' => '<EMAIL>', 'password' => 'demo123', 'description' => '演示帳號'],
        ['email' => '<EMAIL>', 'password' => 'test123', 'description' => '測試帳號'],
        ['email' => '<EMAIL>', 'password' => 'guest123', 'description' => '訪客帳號'],
        ['email' => '<EMAIL>', 'password' => 'admin123', 'description' => '管理員帳號']
    ];
    
    echo "開始更新測試用戶密碼...\n\n";
    
    foreach ($testUsers as $user) {
        echo "處理用戶: " . $user['email'] . " (" . $user['description'] . ")\n";
        
        // 檢查用戶是否存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$user['email']]);
        $existingUser = $stmt->fetch();
        
        if ($existingUser) {
            // 更新密碼
            $passwordHash = password_hash($user['password'], PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                UPDATE users 
                SET password_hash = ?, updated_at = NOW() 
                WHERE email = ?
            ");
            
            $result = $stmt->execute([$passwordHash, $user['email']]);
            
            if ($result) {
                echo "✅ 密碼更新成功 - 密碼: " . $user['password'] . "\n";
            } else {
                echo "❌ 密碼更新失敗\n";
            }
        } else {
            echo "❌ 用戶不存在，跳過\n";
        }
        
        echo "\n";
    }
    
    echo "=== 測試更新後的密碼 ===\n\n";
    
    // 載入 Auth 類別進行測試
    require_once UTILS_PATH . '/Auth.php';
    $auth = new Auth();
    
    foreach ($testUsers as $user) {
        echo "測試登入: " . $user['email'] . "\n";
        
        // 清除 Session
        session_unset();
        
        $loginResult = $auth->login($user['email'], $user['password']);
        
        if ($loginResult['success']) {
            echo "✅ 登入成功\n";
            
            // 檢查登入狀態
            $isLoggedIn = $auth->isLoggedIn();
            echo "   登入狀態: " . ($isLoggedIn ? '✅ 正常' : '❌ 異常') . "\n";
            
        } else {
            echo "❌ 登入失敗: " . $loginResult['message'] . "\n";
        }
        
        echo "\n";
    }
    
    echo "=== 修復完成 ===\n";
    echo "現在可以使用以下帳號進行測試:\n\n";
    
    foreach ($testUsers as $user) {
        echo $user['description'] . ":\n";
        echo "  郵箱: " . $user['email'] . "\n";
        echo "  密碼: " . $user['password'] . "\n\n";
    }
    
    echo "測試方式:\n";
    echo "1. 訪問登入頁面: pages/login.php\n";
    echo "2. 使用上述任一帳號登入\n";
    echo "3. 或使用忘記密碼功能獲取臨時密碼\n";
    
} catch (Exception $e) {
    echo "❌ 處理過程發生錯誤: " . $e->getMessage() . "\n";
}
?>
