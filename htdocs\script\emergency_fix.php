<?php
/**
 * SignAttend 緊急修復腳本
 * 用於診斷和修復重定向循環問題
 */

// 不載入任何配置，避免重定向
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignAttend 緊急修復</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #fff3cd; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; border: 2px solid #ffc107; }
        .alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffc107; color: #856404; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 SignAttend 緊急修復工具</h1>
        
        <div class="alert alert-warning">
            <strong>重定向循環問題檢測</strong><br>
            如果您看到這個頁面，表示緊急修復腳本可以正常運行。
        </div>

        <div class="alert alert-info">
            <h3>當前環境資訊</h3>
            <p><strong>時間:</strong> <?= date('Y-m-d H:i:s') ?></p>
            <p><strong>PHP 版本:</strong> <?= phpversion() ?></p>
            <p><strong>伺服器:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></p>
            <p><strong>域名:</strong> <?= $_SERVER['HTTP_HOST'] ?? 'Unknown' ?></p>
            <p><strong>請求 URI:</strong> <?= $_SERVER['REQUEST_URI'] ?? 'Unknown' ?></p>
            <p><strong>HTTPS 檢測:</strong>
                <?php
                $httpsChecks = [];
                $httpsChecks['$_SERVER[HTTPS]'] = $_SERVER['HTTPS'] ?? 'not set';
                $httpsChecks['$_SERVER[HTTP_X_FORWARDED_PROTO]'] = $_SERVER['HTTP_X_FORWARDED_PROTO'] ?? 'not set';
                $httpsChecks['$_SERVER[SERVER_PORT]'] = $_SERVER['SERVER_PORT'] ?? 'not set';
                
                foreach ($httpsChecks as $key => $value) {
                    echo "<br>• $key: <code>$value</code>";
                }
                ?>
            </p>
        </div>

        <div class="alert alert-success">
            <h3>修復步驟</h3>
            
            <h4>方法 1: 臨時禁用 HTTPS 重定向</h4>
            <p>編輯 <code>htdocs/.htaccess</code> 文件，註釋掉 HTTPS 重定向規則：</p>
            <pre># 臨時註釋這些行
# RewriteCond %{HTTPS} off
# RewriteCond %{HTTP_X_FORWARDED_PROTO} !https
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]</pre>

            <h4>方法 2: 修改配置文件</h4>
            <p>編輯 <code>htdocs/config/config.production.php</code>，臨時註釋掉 HTTPS 檢查：</p>
            <pre>// 臨時註釋這個區塊
/*
if (!isHttps() && !headers_sent()) {
    header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'], true, 301);
    exit();
}
*/</pre>

            <h4>方法 3: 使用開發環境配置</h4>
            <p>臨時刪除 <code>htdocs/config/.env.production</code> 文件，讓系統使用開發環境配置。</p>
        </div>

        <div class="alert alert-info">
            <h3>測試連結</h3>
            <p>修復後請測試以下連結：</p>
            <a href="test_simple.php" class="btn btn-primary">簡單測試頁面</a>
            <a href="test_basic.php" class="btn btn-success">基本功能測試</a>
            <a href="index.php" class="btn btn-warning">主頁面</a>
        </div>

        <div class="alert alert-warning">
            <h3>重要提醒</h3>
            <ul>
                <li>修復完成後，記得重新啟用 HTTPS 重定向</li>
                <li>確保正式環境的安全設定正確</li>
                <li>定期檢查錯誤日誌</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <small>SignAttend 緊急修復工具 - 僅供診斷使用</small>
        </div>
    </div>
</body>
</html>
