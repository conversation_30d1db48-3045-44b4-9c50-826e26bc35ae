# SignAttend 正式環境 Apache 配置
# 安全性和效能優化設定

# 啟用 URL 重寫
RewriteEngine On

# 強制 HTTPS 重定向 (考慮反向代理)
#RewriteCond %{HTTPS} off
#RewriteCond %{HTTP_X_FORWARDED_PROTO} !https
#RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 隱藏 Apache 版本資訊
ServerTokens Prod
ServerSignature Off

# 安全標頭設定
<IfModule mod_headers.c>
    # 防止點擊劫持
    Header always set X-Frame-Options "DENY"
    
    # 防止 MIME 類型嗅探
    Header always set X-Content-Type-Options "nosniff"
    
    # XSS 保護
    Header always set X-XSS-Protection "1; mode=block"
    
    # 嚴格傳輸安全 (HSTS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # 內容安全政策
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' data: https://cdn.jsdelivr.net; connect-src 'self';"
    
    # 引用者政策
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 權限政策
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# 禁止訪問敏感文件和目錄
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# 禁止訪問配置目錄中的敏感文件
<Files "config.php">
    Require all denied
</Files>

<Files "config.production.php">
    Require all denied
</Files>

<Files "database.production.php">
    Require all denied
</Files>

<Files ".env.production">
    Require all denied
</Files>

# 禁止訪問日誌目錄
<Directory "logs">
    Require all denied
</Directory>

# 禁止目錄瀏覽
Options -Indexes

# 禁用伺服器端包含和 CGI 執行
Options -Includes -ExecCGI

# 限制 PHP 文件執行（僅允許在特定目錄）
<Directory "uploads">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
</Directory>

# 檔案上傳限制
<Directory "uploads">
    # 限制檔案類型
    <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
        Require all denied
    </FilesMatch>
    
    # 允許的檔案類型
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx|csv)$">
        Require all granted
    </FilesMatch>
</Directory>

# 快取控制
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 圖片快取 1 個月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    
    # CSS 和 JS 快取 1 週
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType text/javascript "access plus 1 week"
    
    # HTML 快取 1 小時
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Gzip 壓縮
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 防止熱鏈接
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https://(www\.)?attendance\.app\.tn [NC]
    RewriteRule \.(jpg|jpeg|png|gif)$ - [F]
</IfModule>

# 限制請求大小 (5MB)
LimitRequestBody 5242880

# 自定義錯誤頁面
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html

# PHP 安全設定
<IfModule mod_php.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 5M
    php_value upload_max_filesize 5M
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>
