<?php
/**
 * 調試 pages/login.php 的實際執行流程
 */

// 模擬真實的 Web 環境
$_SERVER['HTTP_HOST'] = 'attendance.app.tn';
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
$_SERVER['REMOTE_ADDR'] = '*************';

// 模擬 POST 資料
$_POST['email'] = '<EMAIL>';
$_POST['password'] = 'wrong_password_pages';

echo "=== 調試 pages/login.php 執行流程 ===\n";
echo "模擬環境: " . $_SERVER['HTTP_HOST'] . "\n";
echo "請求方法: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "POST Email: " . $_POST['email'] . "\n";

// 步驟 1: 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);
echo "\n1. ✅ 定義 SIGNATTEND_INIT\n";

// 步驟 2: 啟動 Session
session_start();
echo "2. ✅ 啟動 Session\n";

// 步驟 3: 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';
echo "3. ✅ 載入環境配置\n";
echo "   當前環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "   LOG_ERRORS: " . (defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義') . "\n";

// 步驟 4: 載入共用函數
require_once INCLUDES_PATH . '/functions.php';
echo "4. ✅ 載入共用函數\n";

// 步驟 5: 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';
echo "5. ✅ 載入核心類別\n";

// 步驟 6: 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();
echo "6. ✅ 初始化核心物件\n";

// 步驟 7: 檢查是否已經登入
echo "7. 檢查登入狀態\n";
if ($auth->isLoggedIn()) {
    echo "   ❌ 用戶已登入，會重定向到儀表板（這會阻止登入處理）\n";
    echo "   Session 用戶: " . json_encode($_SESSION['user'] ?? null) . "\n";
    // 不執行 exit，繼續調試
} else {
    echo "   ✅ 用戶未登入，繼續登入流程\n";
}

// 步驟 8: 處理登入表單提交
echo "8. 處理登入表單提交\n";
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "   ✅ 檢測到 POST 請求\n";
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    echo "   Email: " . $email . "\n";
    echo "   Password: [隱藏]\n";
    echo "   Remember Me: " . ($rememberMe ? 'true' : 'false') . "\n";

    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
        echo "   ❌ 驗證失敗: " . $error . "\n";
    } else {
        echo "   ✅ 基本驗證通過，開始登入\n";
        
        // 記錄登入前的日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        echo "   登入前日誌大小: " . $beforeSize . " bytes\n";
        
        echo "   呼叫 auth->login()...\n";
        $result = $auth->login($email, $password, $rememberMe);
        echo "   auth->login() 完成\n";
        
        // 記錄登入後的日誌狀態
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        echo "   登入後日誌大小: " . $afterSize . " bytes\n";
        echo "   日誌大小變化: " . ($afterSize - $beforeSize) . " bytes\n";

        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            echo "   ✅ 登入成功: " . $success . "\n";
            
            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);
            echo "   重定向 URL: " . $redirectUrl . "\n";
            
            // 這裡會有 JavaScript 跳轉，但我們不執行
            echo "   （會執行 JavaScript 跳轉到: " . page_url($redirectUrl) . "）\n";
        } else {
            $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
            echo "   ❌ 登入失敗: " . $error . "\n";
        }
        
        // 檢查最新的日誌記錄
        echo "   檢查最新日誌記錄...\n";
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $lines = explode("\n", trim($content));
            $lastLine = end($lines);
            
            if (!empty($lastLine)) {
                $data = json_decode($lastLine, true);
                if ($data && $data['user_email'] === $email) {
                    echo "   ✅ 找到對應的日誌記錄\n";
                    echo "      時間: " . $data['timestamp'] . "\n";
                    echo "      動作: " . $data['action'] . "\n";
                    echo "      描述: " . $data['description'] . "\n";
                } else {
                    echo "   ❌ 沒有找到對應的日誌記錄\n";
                    if ($data) {
                        echo "      最後記錄的 email: " . ($data['user_email'] ?? '無') . "\n";
                    }
                }
            } else {
                echo "   ❌ 日誌文件為空\n";
            }
        } else {
            echo "   ❌ 日誌文件不存在\n";
        }
    }
} else {
    echo "   ❌ 不是 POST 請求\n";
}

echo "\n=== 調試完成 ===\n";
echo "總結:\n";
echo "- 環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未知') . "\n";
echo "- 登入結果: " . (isset($result) ? ($result['success'] ? '成功' : '失敗') : '未執行') . "\n";
echo "- 錯誤訊息: " . ($error ?: '無') . "\n";
echo "- 成功訊息: " . ($success ?: '無') . "\n";
?>
