<?php
define('SIGNATTEND_INIT', true);
require_once 'config/config.php';

echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 測試寫入
$testFile = LOG_PATH . '/quick_test_' . date('Y-m-d_H-i-s') . '.log';
$content = date('Y-m-d H:i:s') . " - 快速測試\n";

if (file_put_contents($testFile, $content)) {
    echo "✅ 成功寫入外層 logs 目錄: " . $testFile . "\n";
} else {
    echo "❌ 寫入失敗\n";
}

// 檢查外層 logs 目錄
$files = glob(LOG_PATH . '/*.log');
echo "外層 logs 目錄包含 " . count($files) . " 個日誌文件\n";
?>
