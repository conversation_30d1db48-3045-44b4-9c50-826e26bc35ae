{"timestamp":"2025-06-26 05:28:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 05:28:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 05:28:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 05:28:17","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 05:28:18","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 05:28:18","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:12:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:12:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:12:29","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:13:04","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:13:04","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:13:04","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:26:00","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:26:00","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 10:26:01","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:13:51","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"0.0.0.0","user_id":null}
{"timestamp":"2025-06-26 19:19:09","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"0.0.0.0","user_id":null}
{"timestamp":"2025-06-26 19:35:43","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"127.0.0.1","user_id":null}
{"timestamp":"2025-06-26 19:40:24","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:40:24","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:40:25","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:49:19","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:49:19","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 19:49:19","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 20:46:12","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"127.0.0.1","user_id":null}
{"timestamp":"2025-06-26 20:56:25","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 20:56:25","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 20:56:25","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:a598:465f:a0de:e43f","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-26 21:31:57","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"127.0.0.1","user_id":null}
{"timestamp":"2025-06-26 21:32:07","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"127.0.0.1","user_id":null}
{"timestamp":"2025-06-26 21:32:18","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"127.0.0.1","user_id":"a5aaa1ea0eb789550f1f5f27ac4e47e0a0bd"}
