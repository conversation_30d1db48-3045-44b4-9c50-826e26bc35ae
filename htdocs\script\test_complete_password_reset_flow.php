<?php
/**
 * 測試完整的密碼重設流程
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';
require_once UTILS_PATH . '/PasswordReset.php';

echo "=== 測試完整的密碼重設流程 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $auth = new Auth();
    $passwordReset = new PasswordReset();
    
    // 測試用戶
    $testEmail = '<EMAIL>';
    
    echo "步驟 1: 重設密碼\n";
    echo "郵箱: $testEmail\n";
    
    $resetResult = $passwordReset->handleForgotPassword($testEmail);
    
    if (!$resetResult['success']) {
        echo "❌ 密碼重設失敗: " . $resetResult['message'] . "\n";
        exit;
    }
    
    $tempPassword = $resetResult['temp_password'];
    echo "✅ 密碼重設成功\n";
    echo "臨時密碼: $tempPassword\n\n";
    
    echo "步驟 2: 使用臨時密碼登入\n";
    
    // 清除現有 Session
    session_unset();
    
    $loginResult = $auth->login($testEmail, $tempPassword);
    
    if (!$loginResult['success']) {
        echo "❌ 登入失敗: " . $loginResult['message'] . "\n";
        exit;
    }
    
    echo "✅ 登入成功\n\n";
    
    echo "步驟 3: 驗證登入狀態\n";
    
    $isLoggedIn = $auth->isLoggedIn();
    echo "登入狀態: " . ($isLoggedIn ? '✅ 已登入' : '❌ 未登入') . "\n";
    
    if (!$isLoggedIn) {
        echo "❌ 登入狀態檢查失敗\n";
        exit;
    }
    
    echo "✅ 登入狀態正常\n\n";
    
    echo "步驟 4: 檢查用戶信息\n";
    
    $currentUser = $auth->getCurrentUser();
    $currentProfile = $auth->getCurrentProfile();
    
    if (!$currentUser || !$currentProfile) {
        echo "❌ 無法獲取用戶信息\n";
        exit;
    }
    
    echo "✅ 用戶信息正常\n";
    echo "用戶ID: " . $currentUser['id'] . "\n";
    echo "郵箱: " . $currentUser['email'] . "\n";
    echo "姓名: " . $currentProfile['name'] . "\n\n";
    
    echo "步驟 5: 測試頁面重定向邏輯\n";
    
    // 模擬訪問需要登入的頁面
    if ($auth->isLoggedIn()) {
        echo "✅ 可以訪問需要登入的頁面\n";
    } else {
        echo "❌ 無法訪問需要登入的頁面\n";
    }
    
    echo "\n步驟 6: 登出測試\n";
    
    $auth->logout();
    
    $isLoggedInAfterLogout = $auth->isLoggedIn();
    echo "登出後狀態: " . ($isLoggedInAfterLogout ? '❌ 仍然登入' : '✅ 已登出') . "\n\n";
    
    echo "=== 🎉 完整流程測試成功 ===\n";
    echo "✅ 密碼重設功能正常\n";
    echo "✅ 臨時密碼登入正常\n";
    echo "✅ 登入狀態檢查正常\n";
    echo "✅ 用戶信息獲取正常\n";
    echo "✅ 頁面訪問控制正常\n";
    echo "✅ 登出功能正常\n\n";
    
    echo "現在用戶可以：\n";
    echo "1. 在忘記密碼頁面輸入郵箱\n";
    echo "2. 獲得臨時密碼\n";
    echo "3. 使用臨時密碼成功登入\n";
    echo "4. 正常使用系統功能\n";
    
} catch (Exception $e) {
    echo "❌ 測試過程發生錯誤: " . $e->getMessage() . "\n";
    echo "錯誤詳情: " . $e->getTraceAsString() . "\n";
}
?>
