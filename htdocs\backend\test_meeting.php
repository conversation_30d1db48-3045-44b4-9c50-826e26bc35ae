<?php
// 測試會議管理功能
$user_id = '9fda4357-f5bf-4ac1-a502-3dddad8dc8d3'; // 從登入響應中獲取

// 1. 創建會議
echo "=== 測試創建會議 ===\n";
$meetingData = [
    'name' => '測試會議',
    'description' => '這是一個測試會議',
    'start_date' => '2025-06-20 10:00:00',
    'end_date' => '2025-06-20 12:00:00',
    'location' => '會議室A',
    'created_by' => $user_id
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/SignAttend/backend/api/meetings');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($meetingData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";

$responseData = json_decode($response, true);
$meeting_id = $responseData['data']['id'] ?? null;

if ($meeting_id) {
    echo "\n=== 測試獲取會議列表 ===\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/SignAttend/backend/api/meetings');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
    
    echo "\n=== 測試獲取單一會議 ===\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/SignAttend/backend/api/meetings/$meeting_id");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
}
?>
