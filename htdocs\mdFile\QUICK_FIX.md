# SignAttend 快速修復指南

## 問題 1: 內容安全政策 (CSP) 字體錯誤
**狀態**: ✅ 已修復
- 已更新 CSP 設定允許 data: URI 字體
- 修改了 `config.production.php` 和 `.htaccess` 文件

## 問題 2: 資料庫連接錯誤
**狀態**: ⚠️ 需要手動修復

### 立即修復步驟：

1. **修改資料庫密碼**
   編輯 `htdocs/config/config.production.php` 第 68 行：
   ```php
   define('DB_PASS', 'your_actual_database_password'); // 替換為實際密碼
   ```

2. **創建資料庫和用戶**
   ```sql
   -- 連接到 MySQL
   mysql -u root -p
   
   -- 執行以下命令
   CREATE DATABASE IF NOT EXISTS signattend_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'signattend_user'@'localhost' IDENTIFIED BY 'your_actual_database_password';
   GRANT SELECT, INSERT, UPDATE, DELETE ON signattend_prod.* TO 'signattend_user'@'localhost';
   FLUSH PRIVILEGES;
   
   -- 初始化資料表
   USE signattend_prod;
   SOURCE htdocs/config/database_init.sql;
   ```

3. **測試修復**
   訪問 `https://attendance.app.tn/deployment_check.php` 檢查狀態

## 問題 3: 如果仍然看到通用錯誤訊息

這表示 PHP 發生了異常，但因為正式環境關閉了詳細錯誤顯示。

### 臨時啟用除錯模式查看詳細錯誤：

1. **臨時修改配置**
   編輯 `htdocs/config/config.production.php` 第 60 行：
   ```php
   define('DEBUG_MODE', true); // 臨時改為 true
   ```

2. **查看詳細錯誤後記得改回**
   ```php
   define('DEBUG_MODE', false); // 修復後改回 false
   ```

## 快速檢查清單

- [ ] 資料庫密碼已修改
- [ ] 資料庫和用戶已創建
- [ ] 資料表已初始化
- [ ] CSP 設定已更新
- [ ] 網站可以正常訪問
- [ ] 除錯模式已關閉

## 聯絡支援
如果問題持續存在，請提供：
1. 錯誤訊息截圖
2. 瀏覽器開發者工具的 Console 錯誤
3. 伺服器錯誤日誌 (`logs/` 目錄，位於 htdocs 外層)

---
更新時間: 2025-06-25
