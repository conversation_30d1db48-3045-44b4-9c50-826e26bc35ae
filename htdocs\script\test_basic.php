<?php
/**
 * SignAttend 基本功能測試頁面
 * 用於快速驗證系統是否正常運作
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignAttend 基本測試</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SignAttend 基本功能測試</h1>
        <p><strong>測試時間:</strong> <?= date('Y-m-d H:i:s') ?></p>
        
        <div class="section">
            <h2>1. 環境檢測</h2>
            <table>
                <tr>
                    <td>當前環境</td>
                    <td class="<?= IS_PRODUCTION ? 'status-ok' : 'status-warning' ?>">
                        <?= strtoupper(APP_ENVIRONMENT) ?>
                    </td>
                </tr>
                <tr>
                    <td>除錯模式</td>
                    <td class="<?= DEBUG_MODE ? 'status-warning' : 'status-ok' ?>">
                        <?= DEBUG_MODE ? '開啟 (建議關閉)' : '關閉' ?>
                    </td>
                </tr>
                <tr>
                    <td>PHP 版本</td>
                    <td class="status-ok"><?= phpversion() ?></td>
                </tr>
                <tr>
                    <td>域名</td>
                    <td><?= $_SERVER['HTTP_HOST'] ?? 'N/A' ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>2. 配置檢測</h2>
            <table>
                <tr>
                    <td>應用程式名稱</td>
                    <td><?= APP_NAME ?></td>
                </tr>
                <tr>
                    <td>版本</td>
                    <td><?= APP_VERSION ?></td>
                </tr>
                <tr>
                    <td>基礎 URL</td>
                    <td><?= BASE_URL ?></td>
                </tr>
                <tr>
                    <td>API URL</td>
                    <td><?= API_BASE_URL ?></td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>3. 資料庫連接測試</h2>
            <?php
            try {
                $pdo = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                
                echo '<p class="status-ok">✓ 資料庫連接成功</p>';
                echo '<table>';
                echo '<tr><td>主機</td><td>' . DB_HOST . '</td></tr>';
                echo '<tr><td>資料庫</td><td>' . DB_NAME . '</td></tr>';
                echo '<tr><td>用戶</td><td>' . DB_USER . '</td></tr>';
                echo '</table>';
                
                // 測試查詢
                $stmt = $pdo->query("SELECT VERSION() as version");
                $result = $stmt->fetch();
                echo '<p><strong>MySQL 版本:</strong> ' . $result['version'] . '</p>';
                
            } catch (PDOException $e) {
                echo '<p class="status-error">✗ 資料庫連接失敗</p>';
                echo '<p><strong>錯誤訊息:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<p><strong>建議:</strong> 請檢查資料庫配置和密碼設定</p>';
            }
            ?>
        </div>

        <div class="section">
            <h2>4. 文件系統檢測</h2>
            <table>
                <?php
                $directories = [
                    'uploads' => '上傳目錄',
                    'logs' => '日誌目錄',
                    'config' => '配置目錄'
                ];
                
                foreach ($directories as $dir => $description) {
                    $path = __DIR__ . '/' . $dir;
                    $exists = is_dir($path);
                    $writable = $exists && is_writable($path);
                    
                    echo '<tr>';
                    echo '<td>' . $description . '</td>';
                    echo '<td class="' . ($writable ? 'status-ok' : 'status-warning') . '">';
                    echo $writable ? '✓ 可寫入' : ($exists ? '△ 唯讀' : '✗ 不存在');
                    echo '</td>';
                    echo '</tr>';
                }
                ?>
            </table>
        </div>

        <div class="section">
            <h2>5. 功能開關狀態</h2>
            <table>
                <?php
                $features = [
                    'FEATURE_QR_SCANNER' => 'QR Code 掃描',
                    'FEATURE_SIGNATURE_PAD' => '電子簽名',
                    'FEATURE_EMAIL_NOTIFICATIONS' => '郵件通知',
                    'FEATURE_PDF_EXPORT' => 'PDF 匯出',
                    'FEATURE_EXCEL_EXPORT' => 'Excel 匯出'
                ];
                
                foreach ($features as $constant => $description) {
                    $enabled = defined($constant) && constant($constant);
                    echo '<tr>';
                    echo '<td>' . $description . '</td>';
                    echo '<td class="' . ($enabled ? 'status-ok' : 'status-warning') . '">';
                    echo $enabled ? '✓ 啟用' : '△ 停用';
                    echo '</td>';
                    echo '</tr>';
                }
                ?>
            </table>
        </div>

        <div class="section">
            <h2>6. 快速操作</h2>
            <p>
                <a href="index.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">前往首頁</a>
                <a href="deployment_check.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">完整檢查</a>
                <a href="pages/login.php" style="display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">登入頁面</a>
            </p>
        </div>

        <div class="section">
            <h2>7. 系統資訊</h2>
            <table>
                <tr>
                    <td>伺服器軟體</td>
                    <td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' ?></td>
                </tr>
                <tr>
                    <td>文檔根目錄</td>
                    <td><?= $_SERVER['DOCUMENT_ROOT'] ?? 'N/A' ?></td>
                </tr>
                <tr>
                    <td>當前腳本</td>
                    <td><?= $_SERVER['SCRIPT_FILENAME'] ?? 'N/A' ?></td>
                </tr>
                <tr>
                    <td>記憶體限制</td>
                    <td><?= ini_get('memory_limit') ?></td>
                </tr>
                <tr>
                    <td>最大執行時間</td>
                    <td><?= ini_get('max_execution_time') ?> 秒</td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
