<?php
/**
 * Auth 類別診斷工具
 */

echo "<h1>🔍 Auth 類別診斷</h1>";
echo "<div style='font-family: Arial; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 8px;'>";

// 步驟 1: 基本設定
echo "<h2>步驟 1: 基本設定</h2>";
try {
    define('SIGNATTEND_INIT', true);
    session_start();
    echo "✅ 基本設定完成<br><br>";
} catch (Exception $e) {
    echo "❌ 基本設定失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 2: 載入環境配置
echo "<h2>步驟 2: 載入環境配置</h2>";
try {
    require_once __DIR__ . '/config/environment.php';
    echo "✅ 環境配置載入成功<br>";
    echo "當前環境: " . APP_ENVIRONMENT . "<br>";
    echo "除錯模式: " . (DEBUG_MODE ? '開啟' : '關閉') . "<br><br>";
} catch (Exception $e) {
    echo "❌ 環境配置載入失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 3: 檢查 Auth 文件是否存在
echo "<h2>步驟 3: 檢查 Auth 文件</h2>";
$authFile = __DIR__ . '/utils/Auth.php';
if (file_exists($authFile)) {
    echo "✅ Auth.php 文件存在<br>";
    echo "文件大小: " . filesize($authFile) . " bytes<br>";
    echo "文件權限: " . substr(sprintf('%o', fileperms($authFile)), -4) . "<br><br>";
} else {
    echo "❌ Auth.php 文件不存在<br><br>";
}

// 步驟 4: 檢查文件內容
echo "<h2>步驟 4: 檢查 Auth 文件內容</h2>";
if (file_exists($authFile)) {
    $content = file_get_contents($authFile);
    $lines = explode("\n", $content);
    echo "文件行數: " . count($lines) . "<br>";
    
    // 檢查是否有 PHP 語法錯誤 (簡化版本，因為 shell_exec 被禁用)
    if (function_exists('shell_exec')) {
        $syntaxCheck = shell_exec("php -l " . escapeshellarg($authFile) . " 2>&1");
        if (strpos($syntaxCheck, 'No syntax errors') !== false) {
            echo "✅ PHP 語法檢查通過<br>";
        } else {
            echo "❌ PHP 語法錯誤:<br>";
            echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 3px;'>" . htmlspecialchars($syntaxCheck) . "</pre>";
        }
    } else {
        echo "⚠️ shell_exec 函數被禁用，跳過語法檢查<br>";
    }
    
    // 顯示文件開頭
    echo "<br><strong>文件開頭內容:</strong><br>";
    echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", array_slice($lines, 0, 20)));
    echo "</pre><br>";
} else {
    echo "無法檢查文件內容<br><br>";
}

// 步驟 5: 嘗試載入 Auth 類別
echo "<h2>步驟 5: 嘗試載入 Auth 類別</h2>";
try {
    require_once $authFile;
    echo "✅ Auth 文件載入成功<br>";
    
    // 檢查類別是否存在
    if (class_exists('Auth')) {
        echo "✅ Auth 類別存在<br>";
        
        // 嘗試實例化
        $auth = new Auth();
        echo "✅ Auth 類別實例化成功<br>";
        
        // 測試基本方法
        if (method_exists($auth, 'isLoggedIn')) {
            $isLoggedIn = $auth->isLoggedIn();
            echo "✅ isLoggedIn 方法可用，結果: " . ($isLoggedIn ? '已登入' : '未登入') . "<br>";
        } else {
            echo "❌ isLoggedIn 方法不存在<br>";
        }
        
    } else {
        echo "❌ Auth 類別不存在<br>";
    }
    
} catch (ParseError $e) {
    echo "❌ PHP 解析錯誤: " . $e->getMessage() . "<br>";
    echo "錯誤位置: 第 " . $e->getLine() . " 行<br>";
} catch (Error $e) {
    echo "❌ PHP 致命錯誤: " . $e->getMessage() . "<br>";
    echo "錯誤位置: 第 " . $e->getLine() . " 行<br>";
} catch (Exception $e) {
    echo "❌ 一般錯誤: " . $e->getMessage() . "<br>";
}

echo "<br>";

// 步驟 6: 檢查相關文件
echo "<h2>步驟 6: 檢查相關文件</h2>";
$relatedFiles = [
    'utils/ApiClient.php' => 'API 客戶端',
    'includes/functions.php' => '共用函數'
];

foreach ($relatedFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✅ $description 存在<br>";
        
        // 語法檢查 (如果 shell_exec 可用)
        if (function_exists('shell_exec')) {
            $syntaxCheck = shell_exec("php -l " . escapeshellarg($fullPath) . " 2>&1");
            if (strpos($syntaxCheck, 'No syntax errors') !== false) {
                echo "✅ $description 語法正確<br>";
            } else {
                echo "❌ $description 語法錯誤<br>";
            }
        } else {
            echo "⚠️ 跳過語法檢查<br>";
        }
    } else {
        echo "❌ $description 不存在<br>";
    }
}

echo "<br><h2>🎯 診斷完成</h2>";
echo "<p><a href='index.php' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>測試首頁</a></p>";
echo "<p><a href='debug_index.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>返回首頁診斷</a></p>";

echo "</div>";
?>
