<?php
/**
 * SignAttend Backend - 簡單參與者管理 API
 */

// 開啟錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定 CORS 標頭
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 載入配置
$config = require_once __DIR__ . '/config/database.php';

// 資料庫連接
try {
    $pdo = new PDO(
        "mysql:host=" . $config['host'] . ";dbname=" . $config['database'] . ";charset=utf8mb4",
        $config['user'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '資料庫連接失敗: ' . $e->getMessage()
    ]);
    exit;
}

// 獲取請求方法和參數
$method = $_SERVER['REQUEST_METHOD'];
$meetingId = $_GET['meeting_id'] ?? '';
$attendeeId = $_GET['attendee_id'] ?? '';

// 路由處理
switch ($method) {
    case 'GET':
        handleGetAttendees($pdo, $meetingId);
        break;
    
    case 'POST':
        handleCreateAttendee($pdo);
        break;
    
    case 'DELETE':
        if (!empty($attendeeId)) {
            handleDeleteAttendee($pdo, $attendeeId);
        } else {
            handleBatchDeleteAttendees($pdo);
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => '不支援的請求方法'
        ]);
        break;
}

/**
 * 獲取會議參與者列表
 */
function handleGetAttendees($pdo, $meetingId) {
    if (empty($meetingId)) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => '缺少會議 ID'
        ]);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, meeting_id, name, email, organization,
                   checked_in, check_in_time, invitation_sent, invitation_sent_at,
                   created_at
            FROM attendees
            WHERE meeting_id = ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$meetingId]);
        $attendees = $stmt->fetchAll();

        // 格式化日期欄位以符合前端期望
        foreach ($attendees as &$attendee) {
            $attendee['invitation_date'] = $attendee['invitation_sent_at'];
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => $attendees
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => '獲取參與者列表失敗'
        ]);
    }
}

/**
 * 創建新參與者
 */
function handleCreateAttendee($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['meeting_id']) || empty($input['name']) || empty($input['email'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => '缺少必要參數'
        ]);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO attendees (meeting_id, name, email, organization, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $input['meeting_id'],
            $input['name'],
            $input['email'],
            $input['organization'] ?? ''
        ]);
        
        $attendeeId = $pdo->lastInsertId();
        
        echo json_encode([
            'status' => 'success',
            'message' => '參與者創建成功',
            'data' => ['id' => $attendeeId]
        ]);
        
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            http_response_code(409);
            echo json_encode([
                'status' => 'error',
                'message' => '該電子郵件已存在於此會議中'
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => '創建參與者失敗'
            ]);
        }
    }
}

/**
 * 刪除單個參與者
 */
function handleDeleteAttendee($pdo, $attendeeId) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM attendees
            WHERE id = ?
        ");
        $result = $stmt->execute([$attendeeId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'status' => 'success',
                'message' => '參與者刪除成功'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'status' => 'error',
                'message' => '找不到指定的參與者'
            ]);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => '刪除參與者失敗'
        ]);
    }
}

/**
 * 批次刪除參與者
 */
function handleBatchDeleteAttendees($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);

    // 記錄接收到的資料
    error_log('Batch delete input: ' . print_r($input, true));

    if (!$input || empty($input['attendee_ids']) || !is_array($input['attendee_ids'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => '缺少參與者 ID 列表',
            'received_data' => $input
        ]);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 由於我們使用的是模擬資料，先檢查是否為 demo ID
        $demoIds = array_filter($input['attendee_ids'], function($id) {
            return strpos($id, 'demo-') === 0;
        });

        if (!empty($demoIds)) {
            // 對於模擬資料，直接返回成功
            $pdo->commit();
            echo json_encode([
                'status' => 'success',
                'message' => "成功刪除 " . count($demoIds) . " 位參與者（模擬操作）",
                'deleted_count' => count($demoIds),
                'demo_mode' => true
            ]);
            return;
        }

        // 對於真實資料，執行實際的刪除操作
        $placeholders = str_repeat('?,', count($input['attendee_ids']) - 1) . '?';
        $stmt = $pdo->prepare("
            DELETE FROM attendees
            WHERE id IN ($placeholders)
        ");
        $stmt->execute($input['attendee_ids']);

        $deletedCount = $stmt->rowCount();

        $pdo->commit();

        echo json_encode([
            'status' => 'success',
            'message' => "成功刪除 {$deletedCount} 位參與者",
            'deleted_count' => $deletedCount
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log('Batch delete error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => '批次刪除失敗: ' . $e->getMessage()
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log('Batch delete general error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => '批次刪除失敗: ' . $e->getMessage()
        ]);
    }
}
?>
