<?php
/**
 * 查看登入記錄
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';
require_once UTILS_PATH . '/DatabaseLogger.php';

$dbLogger = new DatabaseLogger();

// 處理參數
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
$email = isset($_GET['email']) ? trim($_GET['email']) : null;

// 獲取記錄和統計
$logs = $dbLogger->getRecentLogs($limit, $email);
$stats = $dbLogger->getLoginStats($email);

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入記錄查看器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .filter-form {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .filter-form input, .filter-form select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .log-table th, .log-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .log-table th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .log-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .failed {
            color: red;
            font-weight: bold;
        }
        .description {
            max-width: 300px;
            word-wrap: break-word;
        }
        .user-agent {
            max-width: 200px;
            word-wrap: break-word;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 登入記錄查看器</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h4>總嘗試次數</h4>
                <div class="stat-value"><?= $stats['total_attempts'] ?></div>
                <small>最近 7 天</small>
            </div>
            <div class="stat-card">
                <h4>成功登入</h4>
                <div class="stat-value success"><?= $stats['successful_logins'] ?></div>
                <small>成功次數</small>
            </div>
            <div class="stat-card">
                <h4>失敗嘗試</h4>
                <div class="stat-value failed"><?= $stats['failed_attempts'] ?></div>
                <small>失敗次數</small>
            </div>
            <div class="stat-card">
                <h4>成功率</h4>
                <div class="stat-value" style="color: blue;"><?= $stats['success_rate'] ?>%</div>
                <small>成功百分比</small>
            </div>
        </div>

        <div class="filter-form">
            <form method="GET">
                <label>
                    郵箱篩選: 
                    <input type="email" name="email" value="<?= htmlspecialchars($email ?? '') ?>" placeholder="輸入郵箱地址">
                </label>
                
                <label>
                    記錄數量: 
                    <select name="limit">
                        <option value="20" <?= $limit == 20 ? 'selected' : '' ?>>20</option>
                        <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                        <option value="200" <?= $limit == 200 ? 'selected' : '' ?>>200</option>
                    </select>
                </label>
                
                <button type="submit" class="btn">🔍 篩選</button>
                <a href="?" class="btn">🔄 重置</a>
            </form>
        </div>

        <h2>📋 登入記錄 (顯示 <?= count($logs) ?> 筆)</h2>
        
        <?php if (empty($logs)): ?>
            <div style="text-align: center; padding: 40px; color: #666;">
                <h3>暫無登入記錄</h3>
                <p>請先進行一些登入測試</p>
                <a href="test_database_login_logs.php" class="btn">🧪 前往測試頁面</a>
            </div>
        <?php else: ?>
            <table class="log-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用戶ID</th>
                        <th>郵箱</th>
                        <th>動作</th>
                        <th>描述</th>
                        <th>結果</th>
                        <th>IP 地址</th>
                        <th>用戶代理</th>
                        <th>時間</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $log): ?>
                        <tr>
                            <td><?= $log['id'] ?></td>
                            <td><?= htmlspecialchars($log['user_id'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($log['user_email']) ?></td>
                            <td><?= htmlspecialchars($log['action']) ?></td>
                            <td class="description"><?= htmlspecialchars($log['description'] ?? '') ?></td>
                            <td class="<?= $log['success'] ? 'success' : 'failed' ?>">
                                <?= $log['success'] ? '✅ 成功' : '❌ 失敗' ?>
                            </td>
                            <td><?= htmlspecialchars($log['ip_address'] ?? '') ?></td>
                            <td class="user-agent"><?= htmlspecialchars(substr($log['user_agent'] ?? '', 0, 50)) ?><?= strlen($log['user_agent'] ?? '') > 50 ? '...' : '' ?></td>
                            <td><?= $log['created_at'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>🔧 管理工具</h3>
            <a href="test_database_login_logs.php" class="btn">🧪 測試登入功能</a>
            <a href="setup_login_logs_table.php" class="btn">⚙️ 重新設置資料表</a>
            <a href="?action=clean" class="btn" onclick="return confirm('確定要清理 30 天前的舊記錄嗎？')">🗑️ 清理舊記錄</a>
        </div>
    </div>

    <script>
        // 自動重新整理功能
        let autoRefresh = false;
        let refreshInterval;

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                refreshInterval = setInterval(() => {
                    window.location.reload();
                }, 10000); // 每 10 秒重新整理
                console.log('自動重新整理已啟用');
            } else {
                clearInterval(refreshInterval);
                console.log('自動重新整理已停用');
            }
        }

        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                toggleAutoRefresh();
            }
        });
    </script>
</body>
</html>

<?php
// 處理清理請求
if (isset($_GET['action']) && $_GET['action'] === 'clean') {
    try {
        $result = $dbLogger->cleanOldLogs(30);
        if ($result) {
            echo "<script>alert('舊記錄清理成功！'); window.location.href = '?';</script>";
        } else {
            echo "<script>alert('舊記錄清理失敗！'); window.location.href = '?';</script>";
        }
    } catch (Exception $e) {
        echo "<script>alert('清理過程中發生錯誤: " . addslashes($e->getMessage()) . "'); window.location.href = '?';</script>";
    }
    exit;
}
?>
