<?php
// 測試新的日誌路徑配置
define('SIGNATTEND_INIT', true);
require_once 'config/config.php';

echo "=== 新配置測試 ===\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "HTDOCS_PATH: " . (defined('HTDOCS_PATH') ? HTDOCS_PATH : '未定義') . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 檢查路徑是否正確指向外層
$expectedOuterPath = dirname(dirname(__FILE__)) . '/logs';
echo "預期的外層路徑: " . $expectedOuterPath . "\n";
echo "LOG_PATH 是否指向外層: " . (LOG_PATH === $expectedOuterPath ? '是' : '否') . "\n";

echo "\n=== 目錄檢查 ===\n";
echo "LOG_PATH 是否存在: " . (is_dir(LOG_PATH) ? '是' : '否') . "\n";
echo "LOG_PATH 是否可寫: " . (is_writable(LOG_PATH) ? '是' : '否') . "\n";

// 測試寫入
echo "\n=== 測試寫入到新路徑 ===\n";
$testMessage = "[" . date('Y-m-d H:i:s') . "] 測試新配置日誌寫入\n";
$testLogFile = LOG_PATH . '/config_test_' . date('Y-m-d') . '.log';

if (file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "✓ 成功寫入測試日誌到: " . $testLogFile . "\n";
    echo "文件是否存在: " . (file_exists($testLogFile) ? '是' : '否') . "\n";
} else {
    echo "✗ 無法寫入測試日誌到: " . $testLogFile . "\n";
}

// 檢查外層 logs 目錄內容
echo "\n=== 外層 logs 目錄內容 ===\n";
$outerLogsDir = dirname(dirname(__FILE__)) . '/logs';
if (is_dir($outerLogsDir)) {
    $files = scandir($outerLogsDir);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "外層 logs 目錄中的日誌文件數量: " . count($logFiles) . "\n";
    if (count($logFiles) > 0) {
        echo "文件列表:\n";
        foreach ($logFiles as $file) {
            echo "  - " . $file . "\n";
        }
    } else {
        echo "外層 logs 目錄為空\n";
    }
}
?>
