<?php
/**
 * 最終登入測試 - 即時監控
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入配置
require_once __DIR__ . '/config/environment.php';

// 處理 AJAX 請求
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    if ($_GET['action'] === 'get_latest_logs') {
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $response = [
            'exists' => file_exists($authLogFile),
            'size' => file_exists($authLogFile) ? filesize($authLogFile) : 0,
            'modified' => file_exists($authLogFile) ? filemtime($authLogFile) : 0,
            'logs' => [],
            'total_lines' => 0
        ];
        
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $lines = explode("\n", trim($content));
            $lines = array_filter($lines);
            $response['total_lines'] = count($lines);
            
            // 取最後 3 筆記錄
            $recentLines = array_slice($lines, -3);
            
            foreach ($recentLines as $index => $line) {
                $data = json_decode($line, true);
                if ($data) {
                    $response['logs'][] = [
                        'line_number' => count($lines) - count($recentLines) + $index + 1,
                        'timestamp' => $data['timestamp'],
                        'email' => $data['user_email'] ?? '未知',
                        'action' => $data['action'],
                        'description' => $data['description'],
                        'ip' => $data['ip_address'],
                        'user_agent' => substr($data['user_agent'], 0, 50) . '...',
                        'is_real_browser' => strpos($data['user_agent'], 'Mozilla') !== false && 
                                           strpos($data['ip_address'], '2402:') !== false || 
                                           strpos($data['ip_address'], '192.168.') !== false
                    ];
                }
            }
        }
        
        echo json_encode($response);
        exit;
    }
    
    if ($_GET['action'] === 'clear_session') {
        session_start();
        session_destroy();
        echo json_encode(['success' => true]);
        exit;
    }
}

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最終登入測試 - 即時監控</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
        .test-panel { background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 2px solid #007cba; }
        .monitor-panel { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .log-entry { background: white; margin: 10px 0; padding: 15px; border-radius: 4px; border-left: 4px solid #007cba; }
        .log-failed { border-left-color: #dc3545; }
        .log-real { background: #fff3cd; border-left-color: #ffc107; }
        .timestamp { font-weight: bold; color: #666; }
        .email { color: #007cba; font-weight: bold; }
        .description { margin: 5px 0; }
        .meta { font-size: 12px; color: #666; }
        .btn { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; font-size: 16px; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .status { display: flex; gap: 15px; margin-bottom: 15px; }
        .status-item { background: white; padding: 10px; border-radius: 4px; flex: 1; text-align: center; }
        .highlight { background: #ffeb3b !important; animation: highlight 2s ease-out; }
        @keyframes highlight { from { background: #ffeb3b; } to { background: white; } }
        .instructions { background: #d4edda; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #28a745; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <h1>🔍 最終登入測試 - 即時監控</h1>
    
    <div class="instructions">
        <h3>📋 測試說明</h3>
        <ol>
            <li><strong>保持此頁面開啟</strong>（每 2 秒自動更新）</li>
            <li><strong>開啟新分頁</strong>：<a href="/pages/login.php" target="_blank" class="btn">開啟真實登入頁面</a></li>
            <li><strong>輸入測試資料</strong>：
                <ul>
                    <li>Email: <code>real_test_<?= date('His') ?>@example.com</code></li>
                    <li>Password: <code>wrong_password_<?= date('His') ?></code></li>
                </ul>
            </li>
            <li><strong>點擊登入</strong>並立即回到此頁面觀察</li>
            <li><strong>觀察結果</strong>：新記錄會以黃色高亮顯示</li>
        </ol>
    </div>
    
    <div class="warning">
        <strong>⚠️ 重要提醒：</strong>請使用上面建議的 email 格式，這樣更容易識別您的測試記錄！
    </div>
    
    <div class="test-panel">
        <h3>🛠️ 測試工具</h3>
        <button class="btn btn-success" onclick="clearSession()">清除 Session</button>
        <button class="btn btn-danger" onclick="forceRefresh()">強制重新整理</button>
        <button class="btn" onclick="openLoginPage()">開啟登入頁面</button>
    </div>
    
    <div class="monitor-panel">
        <h3>📊 即時監控狀態</h3>
        <div class="status">
            <div class="status-item">
                <div>總記錄數</div>
                <div id="total-lines">檢查中...</div>
            </div>
            <div class="status-item">
                <div>文件大小</div>
                <div id="file-size">檢查中...</div>
            </div>
            <div class="status-item">
                <div>最後修改</div>
                <div id="last-modified">檢查中...</div>
            </div>
            <div class="status-item">
                <div>更新狀態</div>
                <div id="refresh-status">自動更新中</div>
            </div>
        </div>
        
        <p><strong>日誌路徑:</strong> <code><?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log</code></p>
        <p><strong>當前時間:</strong> <span id="current-time"><?= date('Y-m-d H:i:s') ?></span></p>
    </div>
    
    <div id="logs-container">
        <h3>📋 最新的認證記錄（最後 3 筆）</h3>
        <div id="logs-list">載入中...</div>
    </div>
    
    <script>
        let lastTotalLines = 0;
        let lastModified = 0;
        
        function updateLogs() {
            fetch('?action=get_latest_logs')
                .then(response => response.json())
                .then(data => {
                    // 更新狀態
                    document.getElementById('total-lines').textContent = data.total_lines;
                    document.getElementById('file-size').textContent = data.size + ' bytes';
                    document.getElementById('last-modified').textContent = data.modified ? new Date(data.modified * 1000).toLocaleString() : '無';
                    document.getElementById('current-time').textContent = new Date().toLocaleString();
                    
                    // 檢查是否有新記錄
                    let hasNewRecord = false;
                    if (data.total_lines > lastTotalLines || data.modified > lastModified) {
                        hasNewRecord = true;
                        document.getElementById('refresh-status').textContent = '🆕 檢測到新記錄！';
                        document.getElementById('refresh-status').style.color = '#28a745';
                        document.getElementById('refresh-status').style.fontWeight = 'bold';
                        
                        setTimeout(() => {
                            document.getElementById('refresh-status').textContent = '自動更新中';
                            document.getElementById('refresh-status').style.color = '#666';
                            document.getElementById('refresh-status').style.fontWeight = 'normal';
                        }, 3000);
                    }
                    
                    lastTotalLines = data.total_lines;
                    lastModified = data.modified;
                    
                    // 更新日誌列表
                    const logsList = document.getElementById('logs-list');
                    if (data.logs.length === 0) {
                        logsList.innerHTML = '<p>今日尚無認證記錄</p>';
                    } else {
                        let html = '';
                        data.logs.reverse().forEach((log, index) => {
                            let cssClass = 'log-entry';
                            if (log.action.includes('failed')) cssClass += ' log-failed';
                            if (log.is_real_browser) cssClass += ' log-real';
                            if (hasNewRecord && index === 0) cssClass += ' highlight';
                            
                            const browserIcon = log.is_real_browser ? '🌐' : '🤖';
                            const typeLabel = log.is_real_browser ? '真實瀏覽器' : '測試腳本';
                            
                            html += `
                                <div class="${cssClass}">
                                    <div class="timestamp">⏰ ${log.timestamp} (第 ${log.line_number} 行) ${browserIcon} ${typeLabel}</div>
                                    <div class="email">👤 ${log.email}</div>
                                    <div class="description">📝 ${log.description}</div>
                                    <div class="meta">🌐 IP: ${log.ip} | 🔧 動作: ${log.action} | 🖥️ 瀏覽器: ${log.user_agent}</div>
                                </div>
                            `;
                        });
                        logsList.innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('更新失敗:', error);
                    document.getElementById('logs-list').innerHTML = '<p style="color: red;">更新失敗: ' + error.message + '</p>';
                });
        }
        
        function clearSession() {
            fetch('?action=clear_session')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ Session 已清除');
                    }
                });
        }
        
        function forceRefresh() {
            updateLogs();
            alert('🔄 已強制重新整理');
        }
        
        function openLoginPage() {
            window.open('/pages/login.php', '_blank');
        }
        
        // 初始載入
        updateLogs();
        
        // 每 2 秒自動更新
        setInterval(updateLogs, 2000);
        
        // 頁面可見性變化時暫停/恢復自動更新
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                updateLogs();
            }
        });
    </script>
</body>
</html>
