<?php
/**
 * 檢查 login_logs 資料表狀態
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 檢查 login_logs 資料表 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n";
echo "資料庫: " . DB_NAME . "\n";
echo "主機: " . DB_HOST . "\n\n";

try {
    // 連接資料庫
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "✅ 資料庫連接成功\n\n";
    
    // 檢查所有資料表
    echo "=== 所有資料表列表 ===\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- " . $table . "\n";
    }
    
    // 檢查 login_logs 表是否存在
    echo "\n=== 檢查 login_logs 表 ===\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
    
    if ($stmt->rowCount() > 0) {
        echo "✅ login_logs 表存在\n\n";
        
        // 顯示表結構
        echo "=== login_logs 表結構 ===\n";
        $stmt = $pdo->query("DESCRIBE login_logs");
        $columns = $stmt->fetchAll();
        
        printf("%-15s %-20s %-6s %-4s %-10s %-15s\n", 
            "欄位名稱", "資料類型", "允許NULL", "鍵", "預設值", "額外");
        echo str_repeat("-", 80) . "\n";
        
        foreach ($columns as $column) {
            printf("%-15s %-20s %-6s %-4s %-10s %-15s\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key'],
                $column['Default'] ?? 'NULL',
                $column['Extra']
            );
        }
        
        // 檢查記錄數量
        echo "\n=== 記錄統計 ===\n";
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM login_logs");
        $total = $stmt->fetch()['total'];
        echo "總記錄數: " . $total . "\n";
        
        if ($total > 0) {
            // 顯示最近的記錄
            echo "\n=== 最近 5 筆記錄 ===\n";
            $stmt = $pdo->query("
                SELECT id, user_email, action, description, success, created_at 
                FROM login_logs 
                ORDER BY created_at DESC 
                LIMIT 5
            ");
            $recentLogs = $stmt->fetchAll();
            
            printf("%-4s %-30s %-15s %-30s %-6s %-20s\n", 
                "ID", "郵箱", "動作", "描述", "成功", "時間");
            echo str_repeat("-", 110) . "\n";
            
            foreach ($recentLogs as $log) {
                printf("%-4s %-30s %-15s %-30s %-6s %-20s\n",
                    $log['id'],
                    substr($log['user_email'], 0, 29),
                    $log['action'],
                    substr($log['description'] ?? '', 0, 29),
                    $log['success'] ? '是' : '否',
                    $log['created_at']
                );
            }
            
            // 統計信息
            echo "\n=== 統計信息 ===\n";
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed
                FROM login_logs
            ");
            $stats = $stmt->fetch();
            
            echo "總嘗試次數: " . $stats['total'] . "\n";
            echo "成功次數: " . $stats['successful'] . "\n";
            echo "失敗次數: " . $stats['failed'] . "\n";
            
            if ($stats['total'] > 0) {
                $successRate = round(($stats['successful'] / $stats['total']) * 100, 2);
                echo "成功率: " . $successRate . "%\n";
            }
        } else {
            echo "⚠️  表中暫無記錄\n";
        }
        
    } else {
        echo "❌ login_logs 表不存在！\n";
        echo "\n需要創建 login_logs 表嗎？\n";
        echo "請執行: php setup_login_logs_table.php\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
    echo "\n請檢查:\n";
    echo "1. 資料庫服務是否運行\n";
    echo "2. 資料庫配置是否正確\n";
    echo "3. 用戶權限是否足夠\n";
} catch (Exception $e) {
    echo "❌ 一般錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 檢查完成 ===\n";
?>
