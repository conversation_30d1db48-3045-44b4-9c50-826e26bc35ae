<?php
/**
 * SignAttend 部署檢查腳本
 * 用於驗證正式環境部署是否正確
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignAttend 部署檢查</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            SignAttend 部署檢查報告
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <!-- 基本資訊 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>基本資訊</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>應用程式名稱：</strong></td>
                                        <td><?= APP_NAME ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>版本：</strong></td>
                                        <td><?= APP_VERSION ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>當前環境：</strong></td>
                                        <td>
                                            <span class="badge <?= IS_PRODUCTION ? 'bg-success' : 'bg-warning' ?>">
                                                <?= strtoupper(APP_ENVIRONMENT) ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>域名：</strong></td>
                                        <td><?= $_SERVER['HTTP_HOST'] ?? 'N/A' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>PHP 版本：</strong></td>
                                        <td><?= phpversion() ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>配置檢查</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>除錯模式：</strong></td>
                                        <td>
                                            <span class="badge <?= DEBUG_MODE ? 'bg-warning' : 'bg-success' ?>">
                                                <?= DEBUG_MODE ? '開啟' : '關閉' ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>錯誤記錄：</strong></td>
                                        <td>
                                            <span class="badge <?= LOG_ERRORS ? 'bg-success' : 'bg-danger' ?>">
                                                <?= LOG_ERRORS ? '開啟' : '關閉' ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>HTTPS：</strong></td>
                                        <td>
                                            <span class="badge <?= (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'bg-success' : 'bg-warning' ?>">
                                                <?= (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? '啟用' : '未啟用' ?>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 文件檢查 -->
                        <div class="mb-4">
                            <h5>核心文件檢查</h5>
                            <div class="row">
                                <?php
                                $coreFiles = [
                                    'config/config.php' => '開發環境配置',
                                    'config/config.production.php' => '正式環境配置',
                                    'config/environment.php' => '環境切換器',
                                    'config/.env.production' => '正式環境標記',
                                    'includes/functions.php' => '共用函數',
                                    'utils/ApiClient.php' => 'API 客戶端',
                                    'utils/Auth.php' => '認證類別',
                                    'pages/dashboard.php' => '儀表板頁面',
                                    'pages/login.php' => '登入頁面',
                                    'assets/js/app.js' => '前端腳本'
                                ];
                                
                                foreach ($coreFiles as $file => $description) {
                                    $exists = file_exists(__DIR__ . '/' . $file);
                                    echo '<div class="col-md-6 mb-2">';
                                    echo '<div class="d-flex align-items-center">';
                                    echo '<i class="bi bi-' . ($exists ? 'check-circle text-success' : 'x-circle text-danger') . ' me-2"></i>';
                                    echo '<span class="' . ($exists ? '' : 'text-muted') . '">' . $description . '</span>';
                                    echo '</div>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- 目錄權限檢查 -->
                        <div class="mb-4">
                            <h5>目錄權限檢查</h5>
                            <div class="row">
                                <?php
                                $directories = [
                                    'uploads' => '上傳目錄',
                                    'logs' => '日誌目錄',
                                    'config' => '配置目錄'
                                ];
                                
                                foreach ($directories as $dir => $description) {
                                    $path = __DIR__ . '/' . $dir;
                                    $exists = is_dir($path);
                                    $writable = $exists && is_writable($path);
                                    
                                    echo '<div class="col-md-4 mb-2">';
                                    echo '<div class="d-flex align-items-center">';
                                    echo '<i class="bi bi-folder me-2"></i>';
                                    echo '<span>' . $description . '</span>';
                                    echo '<span class="ms-auto badge ' . ($writable ? 'bg-success' : 'bg-warning') . '">';
                                    echo $writable ? '可寫入' : ($exists ? '唯讀' : '不存在');
                                    echo '</span>';
                                    echo '</div>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- 資料庫連接檢查 -->
                        <div class="mb-4">
                            <h5>資料庫連接檢查</h5>
                            <?php
                            try {
                                $pdo = new PDO(
                                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                                    DB_USER,
                                    DB_PASS,
                                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                                );
                                
                                echo '<div class="alert alert-success">';
                                echo '<i class="bi bi-check-circle me-2"></i>';
                                echo '資料庫連接成功！';
                                echo '<br><small>主機: ' . DB_HOST . ' | 資料庫: ' . DB_NAME . ' | 用戶: ' . DB_USER . '</small>';
                                echo '</div>';
                                
                                // 檢查表格是否存在
                                $tables = ['users', 'profiles', 'meetings', 'attendees'];
                                echo '<div class="mt-3">';
                                echo '<strong>資料表檢查：</strong><br>';
                                foreach ($tables as $table) {
                                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                                    $exists = $stmt->rowCount() > 0;
                                    echo '<span class="badge ' . ($exists ? 'bg-success' : 'bg-warning') . ' me-2">';
                                    echo $table . ($exists ? ' ✓' : ' ✗');
                                    echo '</span>';
                                }
                                echo '</div>';
                                
                            } catch (PDOException $e) {
                                echo '<div class="alert alert-danger">';
                                echo '<i class="bi bi-x-circle me-2"></i>';
                                echo '資料庫連接失敗：' . $e->getMessage();
                                echo '<br><small>請檢查資料庫配置和權限設定</small>';
                                echo '</div>';
                            }
                            ?>
                        </div>

                        <!-- 功能檢查 -->
                        <div class="mb-4">
                            <h5>功能開關狀態</h5>
                            <div class="row">
                                <?php
                                $features = [
                                    'FEATURE_QR_SCANNER' => 'QR Code 掃描',
                                    'FEATURE_SIGNATURE_PAD' => '電子簽名',
                                    'FEATURE_EMAIL_NOTIFICATIONS' => '郵件通知',
                                    'FEATURE_PDF_EXPORT' => 'PDF 匯出',
                                    'FEATURE_EXCEL_EXPORT' => 'Excel 匯出'
                                ];
                                
                                foreach ($features as $constant => $description) {
                                    $enabled = defined($constant) && constant($constant);
                                    echo '<div class="col-md-6 mb-2">';
                                    echo '<div class="d-flex align-items-center">';
                                    echo '<i class="bi bi-gear me-2"></i>';
                                    echo '<span>' . $description . '</span>';
                                    echo '<span class="ms-auto badge ' . ($enabled ? 'bg-success' : 'bg-secondary') . '">';
                                    echo $enabled ? '啟用' : '停用';
                                    echo '</span>';
                                    echo '</div>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- 操作按鈕 -->
                        <div class="text-center">
                            <a href="index.php" class="btn btn-primary me-2">
                                <i class="bi bi-house me-2"></i>前往首頁
                            </a>
                            <a href="pages/dashboard.php" class="btn btn-success me-2">
                                <i class="bi bi-speedometer2 me-2"></i>管理後台
                            </a>
                            <button onclick="location.reload()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-2"></i>重新檢查
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
