<?php
/**
 * 設置登入記錄資料表
 * 執行此腳本來創建 login_logs 資料表
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 設置登入記錄資料表 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n";
echo "資料庫: " . DB_NAME . "\n";
echo "主機: " . DB_HOST . "\n\n";

try {
    // 連接資料庫
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
    
    echo "✅ 資料庫連接成功\n\n";
    
    // 檢查表是否已存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
    if ($stmt->rowCount() > 0) {
        echo "⚠️  login_logs 表已存在\n";
        
        // 顯示現有表結構
        $stmt = $pdo->query("DESCRIBE login_logs");
        $columns = $stmt->fetchAll();
        
        echo "\n現有表結構:\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        echo "| Field           | Type             | Null | Key | Default | Extra          |\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
        foreach ($columns as $column) {
            printf("| %-15s | %-16s | %-4s | %-3s | %-7s | %-14s |\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key'],
                $column['Default'] ?? 'NULL',
                $column['Extra']
            );
        }
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
        // 檢查記錄數量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs");
        $count = $stmt->fetch()['count'];
        echo "\n現有記錄數量: $count\n";
        
    } else {
        echo "📋 創建 login_logs 表...\n";
        
        $sql = "
            CREATE TABLE login_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(36) NULL,
                user_email VARCHAR(255) NOT NULL,
                action VARCHAR(50) NOT NULL,
                description TEXT NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                success BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_login_logs_user_id (user_id),
                INDEX idx_login_logs_user_email (user_email),
                INDEX idx_login_logs_action (action),
                INDEX idx_login_logs_success (success),
                INDEX idx_login_logs_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "✅ login_logs 表創建成功\n";
        
        // 顯示新創建的表結構
        $stmt = $pdo->query("DESCRIBE login_logs");
        $columns = $stmt->fetchAll();
        
        echo "\n新表結構:\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        echo "| Field           | Type             | Null | Key | Default | Extra          |\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
        foreach ($columns as $column) {
            printf("| %-15s | %-16s | %-4s | %-3s | %-7s | %-14s |\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key'],
                $column['Default'] ?? 'NULL',
                $column['Extra']
            );
        }
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
    }
    
    echo "\n=== 測試 DatabaseLogger ===\n";
    
    // 測試 DatabaseLogger
    require_once UTILS_PATH . '/DatabaseLogger.php';
    $logger = new DatabaseLogger();
    
    echo "📝 測試寫入登入記錄...\n";
    
    $testEmail = 'setup_test_' . time() . '@example.com';
    $result = $logger->logActivity(
        'setup_test',
        '設置腳本測試記錄',
        $testEmail,
        null,
        true
    );
    
    if ($result) {
        echo "✅ 測試記錄寫入成功\n";
        
        // 讀取剛寫入的記錄
        $recentLogs = $logger->getRecentLogs(1, $testEmail);
        if (!empty($recentLogs)) {
            $log = $recentLogs[0];
            echo "\n最新記錄:\n";
            echo "ID: " . $log['id'] . "\n";
            echo "郵箱: " . $log['user_email'] . "\n";
            echo "動作: " . $log['action'] . "\n";
            echo "描述: " . $log['description'] . "\n";
            echo "成功: " . ($log['success'] ? '是' : '否') . "\n";
            echo "時間: " . $log['created_at'] . "\n";
        }
        
        // 獲取統計
        $stats = $logger->getLoginStats();
        echo "\n登入統計 (最近7天):\n";
        echo "總嘗試次數: " . $stats['total_attempts'] . "\n";
        echo "成功登入: " . $stats['successful_logins'] . "\n";
        echo "失敗嘗試: " . $stats['failed_attempts'] . "\n";
        echo "成功率: " . $stats['success_rate'] . "%\n";
        
    } else {
        echo "❌ 測試記錄寫入失敗\n";
    }
    
    echo "\n=== 設置完成 ===\n";
    echo "✅ login_logs 表已準備就緒\n";
    echo "✅ DatabaseLogger 測試通過\n";
    echo "✅ 登入記錄現在會寫入資料庫而不是檔案\n";
    echo "\n您現在可以測試 AJAX 登入功能了！\n";
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
    echo "\n請檢查:\n";
    echo "1. 資料庫服務是否運行\n";
    echo "2. 資料庫配置是否正確\n";
    echo "3. 用戶權限是否足夠\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ 一般錯誤: " . $e->getMessage() . "\n";
    exit(1);
}
?>
