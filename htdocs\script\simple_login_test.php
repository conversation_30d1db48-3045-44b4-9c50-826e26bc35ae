<?php
/**
 * 簡化的登入測試頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄頁面訪問
$testLogFile = LOG_PATH . '/simple_test_' . date('Y-m-d') . '.log';
$accessMessage = sprintf(
    "[%s] SIMPLE TEST ACCESS: Method=%s, IP=%s, PostData=%s\n",
    date('Y-m-d H:i:s'),
    $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    json_encode($_POST)
);
file_put_contents($testLogFile, $accessMessage, FILE_APPEND | LOCK_EX);

// 處理登入
$result = null;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    $loginMessage = sprintf(
        "[%s] LOGIN ATTEMPT: Email=%s, HasPassword=%s\n",
        date('Y-m-d H:i:s'),
        $email,
        !empty($password) ? 'yes' : 'no'
    );
    file_put_contents($testLogFile, $loginMessage, FILE_APPEND | LOCK_EX);
    
    // 記錄登入前的認證日誌狀態
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    
    // 執行登入
    $result = $auth->login($email, $password, false);
    
    // 記錄登入後的認證日誌狀態
    $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    
    $resultMessage = sprintf(
        "[%s] LOGIN RESULT: Success=%s, Message=%s, AuthLogBefore=%d, AuthLogAfter=%d\n",
        date('Y-m-d H:i:s'),
        $result['success'] ? 'true' : 'false',
        $result['message'] ?? 'no message',
        $beforeSize,
        $afterSize
    );
    file_put_contents($testLogFile, $resultMessage, FILE_APPEND | LOCK_EX);
    
    if ($result['success']) {
        $success = '登入成功！';
    } else {
        $error = $result['message'] ?? '登入失敗';
        
        // 如果認證日誌沒有增長，手動記錄
        if ($afterSize <= $beforeSize) {
            $manualLogData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'user_id' => null,
                'user_email' => $email,
                'action' => 'login_failed_manual',
                'description' => '簡化測試頁面登入失敗：' . $error,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            $manualLogMessage = json_encode($manualLogData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($authLogFile, $manualLogMessage, FILE_APPEND | LOCK_EX);
            
            $manualMessage = sprintf(
                "[%s] MANUAL LOG: Written to auth log\n",
                date('Y-m-d H:i:s')
            );
            file_put_contents($testLogFile, $manualMessage, FILE_APPEND | LOCK_EX);
        }
    }
}

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡化登入測試</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="email"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
        .alert { padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .alert-danger { background: #fee; color: #c33; border: 1px solid #fcc; }
        .alert-success { background: #efe; color: #3c3; border: 1px solid #cfc; }
        .debug-info { background: #f5f5f5; padding: 15px; border-radius: 4px; margin-top: 20px; font-size: 12px; }
        .log-link { color: #007cba; text-decoration: none; }
    </style>
</head>
<body>
    <h1>🧪 簡化登入測試</h1>
    
    <div class="debug-info">
        <strong>測試信息：</strong><br>
        - 當前時間: <?= date('Y-m-d H:i:s') ?><br>
        - 請求方法: <?= $_SERVER['REQUEST_METHOD'] ?><br>
        - 測試日誌: <a href="#" class="log-link"><?= $testLogFile ?></a><br>
        - 認證日誌: <a href="#" class="log-link"><?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log</a>
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger">
            ❌ <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success">
            ✅ <?= htmlspecialchars($success) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($result): ?>
        <div class="debug-info">
            <strong>登入結果詳情：</strong><br>
            <pre><?= htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <?php generate_csrf_token_field(); ?>
        
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="simple_test_<?= date('His') ?>@example.com" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" value="wrong_password_simple" required>
        </div>
        
        <button type="submit">登入測試</button>
    </form>
    
    <div class="debug-info">
        <strong>使用說明：</strong><br>
        1. 點擊「登入測試」按鈕（已預填測試資料）<br>
        2. 檢查上方是否顯示錯誤信息<br>
        3. 查看 <a href="/monitor_all_logs.php" target="_blank" class="log-link">日誌監控頁面</a> 了解詳細記錄<br>
        4. 這個頁面會記錄完整的登入流程到測試日誌中
    </div>
    
    <script>
        // 記錄表單提交事件
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('簡化測試表單提交:', {
                email: document.getElementById('email').value,
                timestamp: new Date().toISOString()
            });
        });
    </script>
</body>
</html>
