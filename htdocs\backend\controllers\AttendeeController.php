<?php

namespace SignAttend\Controllers;

use PDO;
use SignAttend\Models\Attendee;
use SignAttend\Models\Meeting;

class AttendeeController {
    private $db;
    private $attendeeModel;
    private $meetingModel;

    public function __construct(PDO $db) {
        $this->db = $db;
        $this->attendeeModel = new Attendee($db);
        $this->meetingModel = new Meeting($db);
    }

    // 取得某會議的所有參與者
    public function getAttendeesByMeeting($meeting_id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證會議 ID
            if (empty($meeting_id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '會議 ID 不能為空']);
                return;
            }

            // 檢查會議是否存在
            $meeting = $this->meetingModel->readOne($meeting_id);
            if (!$meeting) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '會議不存在']);
                return;
            }

            // 取得參與者列表
            $attendees = $this->attendeeModel->readByMeetingId($meeting_id);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $attendees,
                'count' => count($attendees),
                'meeting' => $meeting
            ]);

        } catch (\Exception $e) {
            error_log("Get attendees by meeting error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '取得參與者列表失敗：' . $e->getMessage()]);
        }
    }

    // 取得單一參與者詳情
    public function getAttendee($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '參與者 ID 不能為空']);
                return;
            }

            // 取得參與者
            $attendee = $this->attendeeModel->readOne($id);

            if (!$attendee) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '參與者不存在']);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $attendee
            ]);

        } catch (\Exception $e) {
            error_log("Get attendee error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '取得參與者詳情失敗：' . $e->getMessage()]);
        }
    }

    // 新增參與者
    public function createAttendee($meeting_id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證會議 ID
            if (empty($meeting_id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '會議 ID 不能為空']);
                return;
            }

            // 檢查會議是否存在
            $meeting = $this->meetingModel->readOne($meeting_id);
            if (!$meeting) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '會議不存在']);
                return;
            }

            // 獲取 POST 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 驗證必要欄位
            if (!isset($input['name']) || !isset($input['email'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '缺少必要欄位：name, email']);
                return;
            }

            // 驗證電子郵件格式
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '電子郵件格式不正確']);
                return;
            }

            // 檢查是否已經註冊此會議
            $existingAttendee = $this->attendeeModel->findByEmailAndMeeting($input['email'], $meeting_id);
            if ($existingAttendee) {
                http_response_code(409);
                echo json_encode(['status' => 'error', 'message' => '此電子郵件已註冊此會議']);
                return;
            }

            // 準備參與者資料
            $attendeeData = [
                'name' => $input['name'],
                'email' => $input['email'],
                'phone' => $input['phone'] ?? null,
                'organization' => $input['organization'] ?? null,
                'position' => $input['position'] ?? null,
                'meeting_id' => $meeting_id,
                'checked_in' => $input['checked_in'] ?? false,
                'signature_data' => $input['signature_data'] ?? null
            ];

            // 如果是自動簽到，設定簽到時間
            if ($attendeeData['checked_in']) {
                $attendeeData['check_in_time'] = date('Y-m-d H:i:s');
                if ($attendeeData['signature_data']) {
                    $attendeeData['signature_timestamp'] = date('Y-m-d H:i:s');
                }
            }

            // 創建參與者
            $attendee = $this->attendeeModel->create($attendeeData);

            if (!$attendee) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '參與者創建失敗']);
                return;
            }

            http_response_code(201);
            echo json_encode([
                'status' => 'success',
                'message' => '參與者創建成功',
                'data' => $attendee
            ]);

        } catch (\Exception $e) {
            error_log("Create attendee error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '創建參與者失敗：' . $e->getMessage()]);
        }
    }

    // 更新參與者資訊
    public function updateAttendee($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '參與者 ID 不能為空']);
                return;
            }

            // 檢查參與者是否存在
            $existingAttendee = $this->attendeeModel->readOne($id);
            if (!$existingAttendee) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '參與者不存在']);
                return;
            }

            // 獲取 PUT 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 準備更新資料
            $updateData = [];
            if (isset($input['name'])) $updateData['name'] = $input['name'];
            if (isset($input['email'])) {
                // 驗證電子郵件格式
                if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                    http_response_code(400);
                    echo json_encode(['status' => 'error', 'message' => '電子郵件格式不正確']);
                    return;
                }
                $updateData['email'] = $input['email'];
            }
            if (isset($input['phone'])) $updateData['phone'] = $input['phone'];
            if (isset($input['organization'])) $updateData['organization'] = $input['organization'];
            if (isset($input['position'])) $updateData['position'] = $input['position'];
            if (isset($input['checked_in'])) {
                $updateData['checked_in'] = $input['checked_in'];
                // 如果設定為簽到，自動設定簽到時間
                if ($input['checked_in'] && !$existingAttendee['checked_in']) {
                    $updateData['check_in_time'] = date('Y-m-d H:i:s');
                }
            }
            if (isset($input['signature_data'])) {
                $updateData['signature_data'] = $input['signature_data'];
                $updateData['signature_timestamp'] = date('Y-m-d H:i:s');
            }

            // 更新參與者
            $success = $this->attendeeModel->update($id, $updateData);

            if (!$success) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '參與者更新失敗']);
                return;
            }

            // 取得更新後的參與者資料
            $updatedAttendee = $this->attendeeModel->readOne($id);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '參與者更新成功',
                'data' => $updatedAttendee
            ]);

        } catch (\Exception $e) {
            error_log("Update attendee error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '更新參與者失敗：' . $e->getMessage()]);
        }
    }

    // 刪除參與者
    public function deleteAttendee($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '參與者 ID 不能為空']);
                return;
            }

            // 檢查參與者是否存在
            $existingAttendee = $this->attendeeModel->readOne($id);
            if (!$existingAttendee) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '參與者不存在']);
                return;
            }

            // 刪除參與者
            $success = $this->attendeeModel->delete($id);

            if (!$success) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '參與者刪除失敗']);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '參與者刪除成功'
            ]);

        } catch (\Exception $e) {
            error_log("Delete attendee error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '刪除參與者失敗：' . $e->getMessage()]);
        }
    }

    // 參與者簽到
    public function checkInAttendee($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '參與者 ID 不能為空']);
                return;
            }

            // 檢查參與者是否存在
            $attendee = $this->attendeeModel->readOne($id);
            if (!$attendee) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '參與者不存在']);
                return;
            }

            // 檢查是否已經簽到
            if ($attendee['checked_in']) {
                http_response_code(409);
                echo json_encode(['status' => 'error', 'message' => '參與者已經簽到']);
                return;
            }

            // 獲取簽名資料（如果有）
            $input = json_decode(file_get_contents('php://input'), true);
            $signature_data = $input['signature_data'] ?? null;

            // 執行簽到
            $success = $this->attendeeModel->checkIn($id, $signature_data);

            if (!$success) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '簽到失敗']);
                return;
            }

            // 取得更新後的參與者資料
            $updatedAttendee = $this->attendeeModel->readOne($id);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '簽到成功',
                'data' => $updatedAttendee
            ]);

        } catch (\Exception $e) {
            error_log("Check in attendee error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '簽到失敗：' . $e->getMessage()]);
        }
    }

    // 取得參與者資料（用於內部調用）
    public function getAttendeeData($id) {
        return $this->attendeeModel->readOne($id);
    }
}