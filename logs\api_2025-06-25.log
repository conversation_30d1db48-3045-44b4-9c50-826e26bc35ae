{"timestamp":"2025-06-25 08:48:02","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/user\/profile","http_code":404,"success":false,"user_ip":"::1","user_id":null,"error":"404 Not Found - The requested resource was not found."}
{"timestamp":"2025-06-25 08:48:03","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/user\/profile","http_code":404,"success":false,"user_ip":"::1","user_id":null,"error":"404 Not Found - The requested resource was not found."}
{"timestamp":"2025-06-25 08:48:04","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/user\/profile","http_code":404,"success":false,"user_ip":"::1","user_id":null,"error":"404 Not Found - The requested resource was not found."}
{"timestamp":"2025-06-25 08:48:04","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 08:48:10","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 11:06:24","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 11:07:29","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":"9fda4357-f5bf-4ac1-a502-3dddad8dc8d3"}
{"timestamp":"2025-06-25 13:41:45","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 13:42:00","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 13:42:05","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 13:59:09","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 13:59:37","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:00:07","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:02:03","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:08:29","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:22:34","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:22:39","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:30:29","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:33:29","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 14:36:29","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:01:53","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:05:50","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:10:46","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:11:07","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:11:26","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:36:40","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 15:41:08","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 16:07:56","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 16:09:11","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 16:09:19","method":"GET","url":"http:\/\/localhost\/SignAttend\/backend\/api\/test","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 16:09:29","method":"POST","url":"http:\/\/localhost\/SignAttend\/backend\/api\/login","http_code":200,"success":true,"user_ip":"::1","user_id":null}
{"timestamp":"2025-06-25 21:06:27","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:27","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:27","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:32","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:32","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:33","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:47","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:47","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:06:47","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:08:24","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:08:24","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:08:24","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:12:07","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":200,"success":true,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null}
{"timestamp":"2025-06-25 21:12:13","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":200,"success":true,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null}
{"timestamp":"2025-06-25 21:18:31","method":"GET","url":"https:\/\/attendance.app.tn\/api\/test","http_code":200,"success":true,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null}
{"timestamp":"2025-06-25 21:45:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:45:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:45:13","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:45:26","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:45:27","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:45:29","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:52:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:53:06","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:53:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:53:24","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:53:25","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:53:27","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:56:21","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:56:21","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:56:22","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:57:51","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"cURL Error: Operation timed out after 30001 milliseconds with 0 bytes received"}
{"timestamp":"2025-06-25 21:58:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:58:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:58:46","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"cURL Error: Operation timed out after 30001 milliseconds with 0 bytes received"}
{"timestamp":"2025-06-25 21:59:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 21:59:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:01:45","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:01:45","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:01:45","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:36","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:37","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:37","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:47","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:47","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:48","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:57","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:57","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:02:57","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:03:06","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:03:06","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:03:06","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:18:18","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:18:18","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:18:19","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:35:20","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:35:20","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:35:20","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:40:15","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:40:15","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:40:15","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:41:32","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:41:33","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-25 22:41:33","method":"POST","url":"https:\/\/attendance.app.tn\/backend\/api\/login","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:783f:7ed1:d83d:cedb","user_id":null,"error":"Invalid JSON response: Syntax error"}
