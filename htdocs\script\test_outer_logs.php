<?php
// 測試外層 logs 目錄寫入
define('SIGNATTEND_INIT', true);

echo "=== 外層 logs 目錄寫入測試 ===\n";

// 直接計算外層 logs 目錄路徑
$currentFile = __FILE__;
$htdocsDir = dirname($currentFile);
$projectRoot = dirname($htdocsDir);
$outerLogsDir = $projectRoot . '/logs';

echo "當前文件: " . $currentFile . "\n";
echo "htdocs 目錄: " . $htdocsDir . "\n";
echo "專案根目錄: " . $projectRoot . "\n";
echo "外層 logs 目錄: " . $outerLogsDir . "\n";

// 檢查目錄狀態
echo "\n=== 目錄狀態檢查 ===\n";
echo "外層 logs 目錄是否存在: " . (is_dir($outerLogsDir) ? '是' : '否') . "\n";
echo "外層 logs 目錄是否可寫: " . (is_writable($outerLogsDir) ? '是' : '否') . "\n";

// 如果目錄不存在，嘗試創建
if (!is_dir($outerLogsDir)) {
    echo "嘗試創建外層 logs 目錄...\n";
    if (mkdir($outerLogsDir, 0755, true)) {
        echo "✓ 成功創建外層 logs 目錄\n";
    } else {
        echo "✗ 無法創建外層 logs 目錄\n";
    }
}

// 測試寫入多種類型的日誌文件
echo "\n=== 測試寫入日誌文件 ===\n";
$timestamp = date('Y-m-d H:i:s');
$dateStr = date('Y-m-d');

$testLogs = [
    'test_' . $dateStr . '.log' => "[$timestamp] 測試日誌寫入\n",
    'error_' . $dateStr . '.log' => "[$timestamp] ERROR: 測試錯誤日誌\n",
    'api_' . $dateStr . '.log' => "[$timestamp] API: 測試 API 日誌\n",
    'auth_' . $dateStr . '.log' => "[$timestamp] AUTH: 測試認證日誌\n",
    'environment_' . $dateStr . '.log' => "[$timestamp] ENV: 測試環境日誌\n"
];

foreach ($testLogs as $filename => $content) {
    $logFile = $outerLogsDir . '/' . $filename;
    echo "嘗試寫入: " . $filename . "\n";
    
    if (file_put_contents($logFile, $content, FILE_APPEND | LOCK_EX)) {
        echo "  ✓ 成功寫入到: " . $logFile . "\n";
        echo "  文件大小: " . filesize($logFile) . " bytes\n";
    } else {
        echo "  ✗ 寫入失敗: " . $logFile . "\n";
    }
}

// 列出外層 logs 目錄的所有文件
echo "\n=== 外層 logs 目錄內容 ===\n";
if (is_dir($outerLogsDir)) {
    $files = scandir($outerLogsDir);
    $files = array_filter($files, function($file) {
        return $file !== '.' && $file !== '..';
    });
    
    if (empty($files)) {
        echo "外層 logs 目錄為空\n";
    } else {
        echo "外層 logs 目錄包含 " . count($files) . " 個文件:\n";
        foreach ($files as $file) {
            $filePath = $outerLogsDir . '/' . $file;
            $size = is_file($filePath) ? filesize($filePath) : 0;
            $type = is_file($filePath) ? '文件' : '目錄';
            echo "  - " . $file . " (" . $type . ", " . $size . " bytes)\n";
        }
    }
}

// 比較 htdocs/logs 和外層 logs 的內容
echo "\n=== 比較兩個 logs 目錄 ===\n";
$htdocsLogsDir = $htdocsDir . '/logs';
echo "htdocs/logs 目錄: " . $htdocsLogsDir . "\n";

if (is_dir($htdocsLogsDir)) {
    $htdocsFiles = scandir($htdocsLogsDir);
    $htdocsLogFiles = array_filter($htdocsFiles, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "htdocs/logs 中的日誌文件數量: " . count($htdocsLogFiles) . "\n";
} else {
    echo "htdocs/logs 目錄不存在\n";
}

if (is_dir($outerLogsDir)) {
    $outerFiles = scandir($outerLogsDir);
    $outerLogFiles = array_filter($outerFiles, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "外層 logs 中的日誌文件數量: " . count($outerLogFiles) . "\n";
} else {
    echo "外層 logs 目錄不存在\n";
}

echo "\n測試完成！\n";
?>
