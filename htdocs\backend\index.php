<?php
// 除錯日誌
$debugLog = __DIR__ . '/../logs/backend_debug_' . date('Y-m-d') . '.log';
file_put_contents($debugLog, "Backend started at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);

try {
    require 'vendor/autoload.php';
    file_put_contents($debugLog, "Autoload loaded at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);
} catch (Exception $e) {
    file_put_contents($debugLog, "Autoload error: " . $e->getMessage() . "\n", FILE_APPEND | LOCK_EX);
    die("Autoload error");
}

use SignAttend\Controllers\AuthController;
use SignAttend\Controllers\MeetingController;
use SignAttend\Controllers\AttendeeController;
// use SignAttend\Services\QRCodeService; // 暫時註解，避免依賴問題

file_put_contents($debugLog, "Classes imported at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);

// 環境配置
define('DEBUG_MODE', false); // 開發環境設為 true，生產環境設為 false

// Flight 配置
// Flight::set('flight.base_url', '/SignAttend/backend');

// 載入資料庫配置
$dbConfig = require 'config/database.php';

// 建立 PDO 連接
try {
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4, time_zone = '+08:00'"
        ]
    );

    // 將 PDO 物件註冊到 Flight
    Flight::set('db', $pdo);

} catch (PDOException $e) {
    // 資料庫連接失敗，返回錯誤
    Flight::jsonHalt([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $e->getMessage()
    ], 500);
}

// 註冊控制器和服務
Flight::register('authController', AuthController::class, [Flight::get('db')]);
Flight::register('meetingController', MeetingController::class, [Flight::get('db')]);
Flight::register('attendeeController', AttendeeController::class, [Flight::get('db')]);
// Flight::register('qrCodeService', QRCodeService::class); // 暫時註解，避免依賴問題

// 設定 CORS 標頭
Flight::before('start', function () {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    header('Content-Type: application/json; charset=utf-8');

    // 處理 OPTIONS 請求
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
});

// API 路由
// 認證 API
Flight::route('POST /api/register', function() {
    $debugLog = __DIR__ . '/../logs/backend_debug_' . date('Y-m-d') . '.log';
    file_put_contents($debugLog, "Register route called at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);

    try {
        Flight::authController()->register();
        file_put_contents($debugLog, "Register method completed at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);
    } catch (Exception $e) {
        file_put_contents($debugLog, "Register error: " . $e->getMessage() . "\n", FILE_APPEND | LOCK_EX);
        echo json_encode(['status' => 'error', 'message' => 'Internal server error']);
    }
});
Flight::route('POST /api/login', function() {
    Flight::authController()->login();
});
Flight::route('POST /api/forgot-password', function() {
    Flight::authController()->forgotPassword();
});

// 會議管理 API
Flight::route('GET /api/meetings', function() {
    Flight::meetingController()->getAllMeetings();
});
Flight::route('GET /api/meetings/@id', function($id) {
    Flight::meetingController()->getMeeting($id);
});
Flight::route('POST /api/meetings', function() {
    Flight::meetingController()->createMeeting();
});
Flight::route('PUT /api/meetings/@id', function($id) {
    Flight::meetingController()->updateMeeting($id);
});
Flight::route('DELETE /api/meetings/@id', function($id) {
    Flight::meetingController()->deleteMeeting($id);
});

// 參與者管理 API
Flight::route('GET /api/meetings/@meeting_id/attendees', function($meeting_id) {
    Flight::attendeeController()->getAttendeesByMeeting($meeting_id);
});
Flight::route('GET /api/attendees/@id', function($id) {
    Flight::attendeeController()->getAttendee($id);
});
Flight::route('POST /api/meetings/@meeting_id/attendees', function($meeting_id) {
    Flight::attendeeController()->createAttendee($meeting_id);
});
Flight::route('PUT /api/attendees/@id', function($id) {
    Flight::attendeeController()->updateAttendee($id);
});
Flight::route('DELETE /api/attendees/@id', function($id) {
    Flight::attendeeController()->deleteAttendee($id);
});
Flight::route('POST /api/attendees/@id/checkin', function($id) {
    Flight::attendeeController()->checkInAttendee($id);
});

// QR Code API
Flight::route('GET /api/meetings/@id/qrcode', function($id) {
    try {
        $qrCodeService = Flight::qrCodeService();
        $qrCode = $qrCodeService->generateMeetingQRCode($id);

        Flight::json([
            'status' => 'success',
            'data' => $qrCode
        ]);
    } catch (Exception $e) {
        Flight::json([
            'status' => 'error',
            'message' => 'QR Code 生成失敗：' . $e->getMessage()
        ], 500);
    }
});

Flight::route('GET /api/attendees/@id/qrcode', function($id) {
    try {
        // 先獲取參與者資訊
        $attendee = Flight::attendeeController()->getAttendeeData($id);
        if (!$attendee) {
            Flight::json([
                'status' => 'error',
                'message' => '參與者不存在'
            ], 404);
            return;
        }

        $qrCodeService = Flight::qrCodeService();
        $qrCode = $qrCodeService->generateAttendeeQRCode($id, $attendee['meeting_id'], $attendee['name']);

        Flight::json([
            'status' => 'success',
            'data' => $qrCode
        ]);
    } catch (Exception $e) {
        Flight::json([
            'status' => 'error',
            'message' => 'QR Code 生成失敗：' . $e->getMessage()
        ], 500);
    }
});

// 測試端點
Flight::route('GET /api/test', function() {
    Flight::json([
        'status' => 'success',
        'message' => 'API is working!',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
});

// 主頁路由
Flight::route('/', function() {
    echo '<!DOCTYPE html>';
    echo '<html lang="zh-TW">';
    echo '<head><meta charset="UTF-8"><title>SignAttend API</title></head>';
    echo '<body>';
    echo '<h1>SignAttend Flight PHP Backend</h1>';
    echo '<p>API 服務正在運行中</p>';
    echo '<h2>可用的 API 端點：</h2>';
    echo '<ul>';
    echo '<li><strong>POST</strong> /api/register - 使用者註冊</li>';
    echo '<li><strong>POST</strong> /api/login - 使用者登入</li>';
    echo '<li><strong>POST</strong> /api/forgot-password - 忘記密碼重設</li>';
    echo '<li><strong>GET</strong> /api/meetings - 取得所有會議</li>';
    echo '<li><strong>GET</strong> /api/meetings/{id} - 取得單一會議</li>';
    echo '<li><strong>POST</strong> /api/meetings - 建立新會議</li>';
    echo '<li><strong>PUT</strong> /api/meetings/{id} - 更新會議</li>';
    echo '<li><strong>DELETE</strong> /api/meetings/{id} - 刪除會議</li>';
    echo '<li><strong>GET</strong> /api/meetings/{meeting_id}/attendees - 取得會議參與者</li>';
    echo '<li><strong>POST</strong> /api/meetings/{meeting_id}/attendees - 新增參與者</li>';
    echo '<li><strong>GET</strong> /api/attendees/{id} - 取得參與者詳情</li>';
    echo '<li><strong>PUT</strong> /api/attendees/{id} - 更新參與者</li>';
    echo '<li><strong>DELETE</strong> /api/attendees/{id} - 刪除參與者</li>';
    echo '<li><strong>POST</strong> /api/attendees/{id}/checkin - 參與者簽到</li>';
    echo '<li><strong>GET</strong> /api/test - 測試端點</li>';
    echo '</ul>';
    echo '</body></html>';
});

// 處理 404 Not Found
Flight::map('notFound', function(){
    Flight::jsonHalt([
        'status' => 'error',
        'message' => '404 Not Found - The requested resource was not found.'
    ], 404);
});

Flight::start();
