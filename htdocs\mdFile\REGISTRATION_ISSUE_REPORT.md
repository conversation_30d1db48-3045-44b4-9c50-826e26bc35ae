# 註冊功能問題報告

## 🚨 問題描述
使用者嘗試註冊時，前端顯示「系統發生錯誤，請稍後再試。如問題持續發生，請聯繫系統管理員。」，且註冊資料未寫入資料庫。

## 🔍 診斷過程與已執行操作

### 1. 初始日誌檢查
- **檢查檔案**: `logs/api_YYYY-MM-DD.log` 和 `logs/environment_YYYY-MM-DD.log`。
- **結果**: `api.log` 中僅顯示針對 `/backend/api/test` 端點的 `Invalid JSON response: Syntax error` 錯誤，與註冊功能無直接關聯。`error.log` 未產生任何註冊相關錯誤。

### 2. 前端 `Auth.php` 初始化問題
- **問題點**: 發現 `htdocs/utils/Auth.php` 中的 `register` 方法在呼叫 `apiClient->post` 之前，`apiClient` 未被正確初始化。
- **修復操作**: 在 `Auth.php` 的 `register` 方法開頭增加了 `$apiClient = $this->getApiClient();` 以確保 `apiClient` 被正確實例化。

### 3. 後端 `User.php` 資料讀取問題
- **問題點**: `htdocs/backend/models/User.php` 的 `create` 方法在創建使用者後，透過 `readOne` 方法回傳的資料缺少 `password_hash` 欄位，可能導致 `AuthController` 後續創建 `Profile` 時資料不完整。
- **修復操作**: 修改 `User.php` 的 `readOne` 方法，使其包含 `password_hash` 欄位。

### 4. 後端日誌記錄增強
- **問題點**: 註冊失敗時，後端 `error.log` 沒有詳細錯誤訊息。
- **修復操作**: 修改 `htdocs/backend/controllers/AuthController.php` 的 `register` 方法，增加更詳細的錯誤日誌記錄，包含檔案和行號，並在 `DEBUG_MODE` 下包含堆疊追蹤。

### 5. 後端 `DEBUG_MODE` 啟用
- **問題點**: `htdocs/backend/index.php` 中的 `DEBUG_MODE` 預設為 `false`，可能導致錯誤訊息未被完整記錄。
- **修復操作**: 將 `htdocs/backend/index.php` 中的 `DEBUG_MODE` 暫時設定為 `true` 以便除錯。

### 6. API 路由問題 (關鍵發現)
- **問題點**: 檢查 `ApiClient.php` 後，確認前端 API 請求的 `API_BASE_URL` 為 `https://attendance.app.tn/api`。然而，`htdocs/api/.htaccess` 中的重寫規則將所有對 `htdocs/api/` 的請求重寫到 `htdocs/api/index.php`，而非正確的後端入口點 `htdocs/backend/index.php`。這導致註冊請求根本沒有到達後端處理邏輯。
- **修復操作**: 修改 `htdocs/api/.htaccess` 中的重寫規則，將 `RewriteRule ^(.*)$ index.php [QSA,L]` 改為 `RewriteRule ^(.*)$ ../backend/index.php [QSA,L]`，確保請求正確路由到後端。

### 7. `DEBUG_MODE` 恢復
- **修復操作**: 將 `htdocs/backend/index.php` 中的 `DEBUG_MODE` 設定回 `false`。

## 🚧 目前狀況
- 經過上述修改，註冊請求現在應該能正確路由到後端 API。
- `error.log` 仍未顯示註冊相關錯誤，這可能表示：
    - 註冊請求現在已成功處理。
    - 仍有其他未被捕獲的錯誤發生在請求到達 `AuthController` 之前，或者錯誤記錄機制仍有問題。

## ➡️ 下一步行動
請使用者再次嘗試註冊，並密切觀察 `logs/error_YYYY-MM-DD.log` 檔案。如果問題仍然存在，請提供最新的日誌內容。
