<?php
/**
 * AJAX 登入調試工具
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

// 處理 AJAX 請求
if (isset($_POST['action']) && $_POST['action'] === 'test_login') {
    header('Content-Type: application/json');
    
    $response = [
        'success' => false,
        'message' => '',
        'debug_info' => [],
        'environment' => [],
        'database' => [],
        'auth_test' => []
    ];
    
    try {
        // 1. 環境信息
        $response['environment'] = [
            'db_host' => defined('DB_HOST') ? DB_HOST : '未定義',
            'db_name' => defined('DB_NAME') ? DB_NAME : '未定義',
            'db_user' => defined('DB_USER') ? DB_USER : '未定義',
            'db_pass_set' => defined('DB_PASS') && !empty(DB_PASS),
            'debug_mode' => defined('DEBUG_MODE') ? DEBUG_MODE : false,
            'server_name' => $_SERVER['SERVER_NAME'] ?? '未知',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '未知'
        ];
        
        // 2. 測試資料庫連接
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            $response['database']['connection'] = '成功';
            
            // 檢查用戶表
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch()['count'];
            $response['database']['user_count'] = $userCount;
            
            // 檢查測試用戶
            $testEmail = $_POST['email'] ?? '<EMAIL>';
            $stmt = $pdo->prepare("SELECT id, email FROM users WHERE email = ?");
            $stmt->execute([$testEmail]);
            $user = $stmt->fetch();
            
            $response['database']['test_user_exists'] = $user ? true : false;
            if ($user) {
                $response['database']['test_user_id'] = substr($user['id'], 0, 8) . '...';
            }
            
        } catch (Exception $e) {
            $response['database']['connection'] = '失敗: ' . $e->getMessage();
        }
        
        // 3. 測試 Auth 類別
        try {
            $auth = new Auth();
            $response['auth_test']['class_loaded'] = true;
            
            // 測試登入
            $email = $_POST['email'] ?? '<EMAIL>';
            $password = $_POST['password'] ?? 'test123';
            
            $response['auth_test']['test_email'] = $email;
            $response['auth_test']['test_password_length'] = strlen($password);
            
            // 清除 Session
            session_unset();
            
            $loginResult = $auth->login($email, $password);
            
            $response['auth_test']['login_result'] = $loginResult;
            
            if ($loginResult['success']) {
                $response['success'] = true;
                $response['message'] = '登入測試成功';
                
                // 檢查登入狀態
                $isLoggedIn = $auth->isLoggedIn();
                $response['auth_test']['is_logged_in'] = $isLoggedIn;
                
                // 檢查 Session
                $response['auth_test']['session_user'] = isset($_SESSION['user']);
                $response['auth_test']['session_auth_token'] = isset($_SESSION['auth_token']);
                $response['auth_test']['session_profile'] = isset($_SESSION['profile']);
                
            } else {
                $response['message'] = '登入測試失敗: ' . ($loginResult['message'] ?? '未知錯誤');
            }
            
        } catch (Exception $e) {
            $response['auth_test']['class_loaded'] = false;
            $response['auth_test']['error'] = $e->getMessage();
        }
        
    } catch (Exception $e) {
        $response['message'] = '調試過程發生錯誤: ' . $e->getMessage();
        $response['debug_info']['exception'] = $e->getTraceAsString();
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX 登入調試工具</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #005a8b; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .debug-info { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin-top: 10px; border-radius: 4px; }
        .debug-info pre { margin: 0; white-space: pre-wrap; font-size: 12px; }
    </style>
</head>
<body>
    <h1>AJAX 登入調試工具</h1>
    
    <form id="debugForm">
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" value="test123" required>
        </div>
        
        <button type="submit" class="btn">測試 AJAX 登入</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('debugForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'test_login');
            formData.append('email', document.getElementById('email').value);
            formData.append('password', document.getElementById('password').value);
            
            document.getElementById('result').innerHTML = '<div class="debug-info">測試中...</div>';
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                let resultClass = data.success ? 'success' : 'error';
                let html = `<div class="result ${resultClass}">
                    <h3>測試結果: ${data.message}</h3>
                </div>`;
                
                html += '<div class="debug-info"><h4>調試信息:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                
                document.getElementById('result').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `<div class="result error">
                    <h3>請求失敗: ${error.message}</h3>
                </div>`;
            });
        });
    </script>
</body>
</html>
