<?php

namespace SignAttend\Models;

use PDO;
use PDOException;

class Profile {
    private $db;
    private $table_name = "profiles";

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    // 創建個人資料
    public function create($data) {
        try {
            $query = "INSERT INTO " . $this->table_name . " (id, name, email, created_at, updated_at) VALUES (:id, :name, :email, :created_at, :updated_at)";
            $stmt = $this->db->prepare($query);

            $now = date('Y-m-d H:i:s'); // PHP 生成的台北時間

            $stmt->bindParam(":id", $data['id']);
            $stmt->bindParam(":name", $data['name']);
            $stmt->bindParam(":email", $data['email']);
            $stmt->bindParam(":created_at", $now);
            $stmt->bindParam(":updated_at", $now);

            if ($stmt->execute()) {
                return $this->readOne($data['id']);
            }
            return false;
        } catch (PDOException $e) {
            error_log("Profile create error: " . $e->getMessage());
            return false;
        }
    }

    // 讀取單一個人資料
    public function readOne($id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Profile readOne error: " . $e->getMessage());
            return null;
        }
    }

    // 讀取所有個人資料
    public function readAll() {
        try {
            $query = "SELECT * FROM " . $this->table_name . " ORDER BY created_at DESC";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Profile readAll error: " . $e->getMessage());
            return [];
        }
    }

    // 更新個人資料
    public function update($id, $data) {
        try {
            $query = "UPDATE " . $this->table_name . " SET name = :name, email = :email WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(":name", $data['name']);
            $stmt->bindParam(":email", $data['email']);
            $stmt->bindParam(":id", $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Profile update error: " . $e->getMessage());
            return false;
        }
    }

    // 刪除個人資料
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Profile delete error: " . $e->getMessage());
            return false;
        }
    }

    // 根據使用者 ID 查找個人資料
    public function findByUserId($user_id) {
        return $this->readOne($user_id);
    }
}