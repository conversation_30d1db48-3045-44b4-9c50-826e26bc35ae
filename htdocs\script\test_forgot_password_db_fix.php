<?php
/**
 * 測試忘記密碼資料庫連接修復
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 測試忘記密碼資料庫連接修復 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

echo "=== 檢查資料庫配置 ===\n";
echo "DB_HOST: " . (defined('DB_HOST') ? DB_HOST : '未定義') . "\n";
echo "DB_NAME: " . (defined('DB_NAME') ? DB_NAME : '未定義') . "\n";
echo "DB_USER: " . (defined('DB_USER') ? DB_USER : '未定義') . "\n";
echo "DB_PASS: " . (defined('DB_PASS') ? '已設定' : '未設定') . "\n\n";

// 測試直接資料庫連接
echo "=== 測試直接資料庫連接 ===\n";
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    echo "✅ 直接資料庫連接成功\n";
    
    // 測試查詢
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch()['count'];
    echo "✅ 用戶表查詢成功，共 $count 個用戶\n";
    
} catch (PDOException $e) {
    echo "❌ 直接資料庫連接失敗: " . $e->getMessage() . "\n";
    echo "錯誤代碼: " . $e->getCode() . "\n";
}

// 測試 PasswordReset 類別
echo "\n=== 測試 PasswordReset 類別 ===\n";
try {
    require_once UTILS_PATH . '/PasswordReset.php';
    $passwordReset = new PasswordReset();
    echo "✅ PasswordReset 類別初始化成功\n";
    
    // 測試一個不存在的郵箱
    $testEmail = 'test_db_fix_' . time() . '@example.com';
    echo "測試郵箱: $testEmail\n";
    
    $result = $passwordReset->handleForgotPassword($testEmail);
    
    if (!$result['success'] && strpos($result['message'], '找不到') !== false) {
        echo "✅ 不存在郵箱測試正常（正確返回找不到用戶）\n";
    } else {
        echo "⚠️  不存在郵箱測試異常: " . $result['message'] . "\n";
    }
    
    // 測試存在的演示郵箱
    $demoEmail = '<EMAIL>';
    echo "\n測試演示郵箱: $demoEmail\n";
    
    $result = $passwordReset->handleForgotPassword($demoEmail);
    
    if ($result['success']) {
        echo "✅ 演示郵箱密碼重設成功\n";
        echo "臨時密碼: " . $result['temp_password'] . "\n";
    } else {
        echo "❌ 演示郵箱密碼重設失敗: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ PasswordReset 類別測試失敗: " . $e->getMessage() . "\n";
    echo "錯誤檔案: " . $e->getFile() . "\n";
    echo "錯誤行號: " . $e->getLine() . "\n";
}

// 測試忘記密碼頁面配置載入
echo "\n=== 測試忘記密碼頁面配置 ===\n";
try {
    // 模擬忘記密碼頁面的載入過程
    $configPath = __DIR__ . '/config/environment.php';
    echo "配置檔案路徑: $configPath\n";
    echo "配置檔案存在: " . (file_exists($configPath) ? '✅ 是' : '❌ 否') . "\n";
    
    if (file_exists($configPath)) {
        echo "✅ 忘記密碼頁面應該能正確載入配置\n";
    } else {
        echo "❌ 配置檔案不存在，需要檢查路徑\n";
    }
    
} catch (Exception $e) {
    echo "❌ 配置測試失敗: " . $e->getMessage() . "\n";
}

echo "\n=== 修復建議 ===\n";
if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
    echo "✅ 資料庫配置常數已正確定義\n";
    echo "✅ 忘記密碼功能應該可以正常工作\n";
    echo "\n請嘗試:\n";
    echo "1. 訪問忘記密碼頁面: pages/forgot-password.php\n";
    echo "2. 輸入演示郵箱: <EMAIL>\n";
    echo "3. 確認不再出現資料庫連接錯誤\n";
} else {
    echo "❌ 資料庫配置常數未正確定義\n";
    echo "請檢查 config/environment.php 檔案\n";
}

echo "\n=== 測試完成 ===\n";
?>
