<?php
/**
 * 首頁錯誤診斷工具
 * 逐步載入首頁組件，找出問題所在
 */

echo "<h1>🔍 首頁錯誤診斷</h1>";
echo "<div style='font-family: Arial; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 8px;'>";

// 步驟 1: 基本 PHP 檢查
echo "<h2>步驟 1: 基本 PHP 檢查</h2>";
echo "✅ PHP 版本: " . phpversion() . "<br>";
echo "✅ 當前時間: " . date('Y-m-d H:i:s') . "<br>";
echo "✅ 記憶體限制: " . ini_get('memory_limit') . "<br><br>";

// 步驟 2: 常數定義
echo "<h2>步驟 2: 定義常數</h2>";
try {
    define('SIGNATTEND_INIT', true);
    echo "✅ SIGNATTEND_INIT 定義成功<br>";
} catch (Exception $e) {
    echo "❌ 常數定義失敗: " . $e->getMessage() . "<br>";
}

// 步驟 3: Session 啟動
echo "<h2>步驟 3: Session 啟動</h2>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session 啟動成功<br>";
    echo "Session ID: " . session_id() . "<br><br>";
} catch (Exception $e) {
    echo "❌ Session 啟動失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 4: 載入環境配置
echo "<h2>步驟 4: 載入環境配置</h2>";
try {
    require_once __DIR__ . '/config/environment.php';
    echo "✅ 環境配置載入成功<br>";
    echo "當前環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "<br>";
    echo "除錯模式: " . (defined('DEBUG_MODE') && DEBUG_MODE ? '開啟' : '關閉') . "<br><br>";
} catch (Exception $e) {
    echo "❌ 環境配置載入失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 5: 載入共用函數
echo "<h2>步驟 5: 載入共用函數</h2>";
try {
    require_once __DIR__ . '/includes/functions.php';
    echo "✅ 共用函數載入成功<br><br>";
} catch (Exception $e) {
    echo "❌ 共用函數載入失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 6: 資料庫連接測試
echo "<h2>步驟 6: 資料庫連接測試</h2>";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 資料庫連接成功<br>";
    
    // 測試查詢
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM meetings LIMIT 1");
    $stmt->execute();
    echo "✅ 資料表查詢成功<br><br>";
} catch (PDOException $e) {
    echo "❌ 資料庫操作失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 7: 測試會議資料查詢 (首頁的主要邏輯)
echo "<h2>步驟 7: 測試會議資料查詢</h2>";
try {
    $recentMeetings = [];
    $stmt = $pdo->prepare("
        SELECT
            m.id,
            m.name,
            m.description,
            m.start_time,
            m.end_time,
            m.location,
            m.created_at,
            COUNT(a.id) as total_attendees,
            COUNT(CASE WHEN a.checked_in = 1 THEN 1 END) as checked_in_count
        FROM meetings m
        LEFT JOIN attendees a ON m.id = a.meeting_id
        GROUP BY m.id, m.name, m.description, m.start_time, m.end_time, m.location, m.created_at
        ORDER BY m.created_at DESC
        LIMIT 3
    ");
    $stmt->execute();
    $recentMeetings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ 會議資料查詢成功<br>";
    echo "找到 " . count($recentMeetings) . " 個會議<br><br>";
} catch (Exception $e) {
    echo "❌ 會議資料查詢失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 8: 測試認證類別
echo "<h2>步驟 8: 測試認證類別</h2>";
try {
    require_once __DIR__ . '/utils/Auth.php';
    $auth = new Auth();
    echo "✅ Auth 類別載入成功<br>";
    echo "登入狀態: " . ($auth->isLoggedIn() ? '已登入' : '未登入') . "<br><br>";
} catch (Exception $e) {
    echo "❌ Auth 類別載入失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 9: 測試 API 客戶端
echo "<h2>步驟 9: 測試 API 客戶端</h2>";
try {
    require_once __DIR__ . '/utils/ApiClient.php';
    $apiClient = new ApiClient();
    echo "✅ ApiClient 類別載入成功<br><br>";
} catch (Exception $e) {
    echo "❌ ApiClient 類別載入失敗: " . $e->getMessage() . "<br><br>";
}

// 步驟 10: 檢查錯誤日誌
echo "<h2>步驟 10: 檢查最近的錯誤日誌</h2>";
$logFile = __DIR__ . '/logs/error_' . date('Y-m-d') . '.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -10); // 最後 10 行
    echo "最近的錯誤日誌:<br>";
    echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $recentLines));
    echo "</pre>";
} else {
    echo "今日無錯誤日誌文件<br>";
}

echo "<br><h2>🎯 診斷完成</h2>";
echo "<p>如果所有步驟都成功，問題可能在首頁的 HTML 輸出部分。</p>";
echo "<p><a href='index.php' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>重新測試首頁</a></p>";
echo "<p><a href='test_basic.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>返回基本測試</a></p>";

echo "</div>";
?>
