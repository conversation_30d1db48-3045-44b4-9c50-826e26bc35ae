<?php
/**
 * 測試真實的 AJAX 登入請求
 */

echo "=== 真實 AJAX 登入測試 ===\n";
echo "當前時間: " . date('Y-m-d H:i:s') . "\n\n";

// 模擬 AJAX 請求到 login.php
$loginUrl = 'http://localhost/attendance.app.tn/htdocs/pages/login.php';

// 準備 POST 數據
$postData = [
    'ajax_login' => '1',
    'email' => 'test_ajax_' . time() . '@example.com',
    'password' => 'test123',
    'remember_me' => '0'
];

echo "請求 URL: " . $loginUrl . "\n";
echo "POST 數據: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 使用 cURL 發送請求
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $loginUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($postData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => false,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/x-www-form-urlencoded',
        'X-Requested-With: XMLHttpRequest'
    ]
]);

echo "發送 AJAX 請求...\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "cURL 錯誤: " . $error . "\n";
    exit;
}

echo "HTTP 狀態碼: " . $httpCode . "\n";

// 分離標頭和內容
$headerSize = strpos($response, "\r\n\r\n");
if ($headerSize !== false) {
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize + 4);
} else {
    $headers = '';
    $body = $response;
}

echo "響應標頭:\n" . $headers . "\n\n";
echo "響應內容:\n" . $body . "\n\n";

// 嘗試解析 JSON 響應
$jsonData = json_decode($body, true);
if ($jsonData) {
    echo "=== 解析後的響應數據 ===\n";
    echo "成功: " . ($jsonData['success'] ? '是' : '否') . "\n";
    echo "訊息: " . ($jsonData['message'] ?? 'N/A') . "\n";
    echo "重定向: " . ($jsonData['redirect'] ?? 'N/A') . "\n";
    
    if (isset($jsonData['debug_info'])) {
        echo "\n調試信息:\n";
        echo "- 登入前大小: " . ($jsonData['debug_info']['before_size'] ?? 'N/A') . " bytes\n";
        echo "- 登入後大小: " . ($jsonData['debug_info']['after_size'] ?? 'N/A') . " bytes\n";
        echo "- 認證日誌檔案: " . ($jsonData['debug_info']['auth_log_file'] ?? 'N/A') . "\n";
        echo "- 日誌路徑: " . ($jsonData['debug_info']['log_path'] ?? 'N/A') . "\n";
        
        $beforeSize = $jsonData['debug_info']['before_size'] ?? 0;
        $afterSize = $jsonData['debug_info']['after_size'] ?? 0;
        $sizeChange = $afterSize - $beforeSize;
        
        echo "- 大小變化: " . $sizeChange . " bytes\n";
        
        if ($sizeChange > 0) {
            echo "✅ 日誌寫入成功！\n";
        } else {
            echo "❌ 日誌寫入失敗或沒有變化\n";
        }
    }
} else {
    echo "❌ 無法解析 JSON 響應\n";
    echo "原始響應: " . $body . "\n";
}

echo "\n=== 檢查實際日誌檔案 ===\n";

// 檢查實際的日誌檔案
define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/config/environment.php';

$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "認證日誌檔案: " . $authLogFile . "\n";

if (file_exists($authLogFile)) {
    $fileSize = filesize($authLogFile);
    echo "檔案大小: " . $fileSize . " bytes\n";
    
    // 讀取最後幾行
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    $lastLines = array_slice($lines, -3); // 最後 3 行
    
    echo "最後 3 行日誌:\n";
    foreach ($lastLines as $index => $line) {
        if (!empty($line)) {
            $lineNumber = count($lines) - count($lastLines) + $index + 1;
            echo "第 " . $lineNumber . " 行: " . $line . "\n";
            
            // 嘗試解析 JSON
            $logData = json_decode($line, true);
            if ($logData && isset($logData['user_email']) && strpos($logData['user_email'], 'test_ajax_') === 0) {
                echo "  ✅ 找到測試記錄: " . $logData['action'] . " - " . $logData['description'] . "\n";
            }
        }
    }
} else {
    echo "❌ 認證日誌檔案不存在\n";
}

echo "\n=== 測試完成 ===\n";
?>
