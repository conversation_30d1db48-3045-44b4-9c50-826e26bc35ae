<?php
// 測試參與者管理功能
$meeting_id = 'e4810622-47af-4988-ad00-20e8ce10b682'; // 從會議創建響應中獲取

// 1. 創建參與者
echo "=== 測試創建參與者 ===\n";
$attendeeData = [
    'name' => '張三',
    'email' => '<EMAIL>',
    'phone' => '0912345678',
    'organization' => 'ABC公司',
    'position' => '經理'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "http://localhost/SignAttend/backend/api/meetings/$meeting_id/attendees");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($attendeeData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";

$responseData = json_decode($response, true);
$attendee_id = $responseData['data']['id'] ?? null;

if ($attendee_id) {
    echo "\n=== 測試獲取會議參與者列表 ===\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/SignAttend/backend/api/meetings/$meeting_id/attendees");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
    
    echo "\n=== 測試參與者簽到 ===\n";
    $checkinData = [
        'signature_data' => 'base64_encoded_signature_data'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/SignAttend/backend/api/attendees/$attendee_id/checkin");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($checkinData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
}
?>
