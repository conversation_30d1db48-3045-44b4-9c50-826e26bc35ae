<?php
// 在配置文件目錄中測試路徑計算
echo "=== 配置文件目錄中的路徑測試 ===\n";
echo "__FILE__: " . __FILE__ . "\n";
echo "dirname(__FILE__): " . dirname(__FILE__) . "\n";
echo "dirname(dirname(__FILE__)): " . dirname(dirname(__FILE__)) . "\n";

// 這應該是 ROOT_PATH 的值（三層 dirname）
$rootPath = dirname(dirname(dirname(__FILE__)));
echo "ROOT_PATH 應該是: " . $rootPath . "\n";
echo "三層 dirname 結果: " . dirname(dirname(dirname(__FILE__))) . "\n";

// 檢查 logs 目錄
$logsPath = $rootPath . '/logs';
echo "LOG_PATH 應該是: " . $logsPath . "\n";
echo "LOG_PATH 是否存在: " . (is_dir($logsPath) ? '是' : '否') . "\n";
?>
