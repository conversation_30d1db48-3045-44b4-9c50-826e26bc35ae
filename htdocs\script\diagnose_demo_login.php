<?php
/**
 * 診斷演示帳號登入問題
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

echo "=== 診斷演示帳號登入問題 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 演示帳號列表
$demoAccounts = [
    ['email' => '<EMAIL>', 'password' => 'demo123', 'description' => '演示帳號'],
    ['email' => '<EMAIL>', 'password' => 'test123', 'description' => '測試帳號'],
    ['email' => '<EMAIL>', 'password' => 'guest123', 'description' => '訪客帳號']
];

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

foreach ($demoAccounts as $account) {
    echo "=== 測試 " . $account['description'] . " ===\n";
    echo "郵箱: " . $account['email'] . "\n";
    echo "密碼: " . $account['password'] . "\n";
    
    // 檢查資料庫中是否存在該用戶
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $stmt = $pdo->prepare("SELECT id, email, password_hash FROM users WHERE email = ?");
        $stmt->execute([$account['email']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ 用戶存在於資料庫\n";
            echo "用戶ID: " . substr($user['id'], 0, 8) . "...\n";
            
            // 驗證密碼
            if (password_verify($account['password'], $user['password_hash'])) {
                echo "✅ 密碼驗證正確\n";
            } else {
                echo "❌ 密碼驗證失敗\n";
                echo "嘗試重新設置密碼...\n";
                
                // 重新設置密碼
                $newHash = password_hash($account['password'], PASSWORD_DEFAULT);
                $updateStmt = $pdo->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE email = ?");
                $updateResult = $updateStmt->execute([$newHash, $account['email']]);
                
                if ($updateResult) {
                    echo "✅ 密碼重新設置成功\n";
                } else {
                    echo "❌ 密碼重新設置失敗\n";
                }
            }
        } else {
            echo "❌ 用戶不存在於資料庫\n";
            echo "嘗試創建用戶...\n";
            
            // 創建用戶
            $userId = bin2hex(random_bytes(18));
            $passwordHash = password_hash($account['password'], PASSWORD_DEFAULT);
            
            $insertStmt = $pdo->prepare("
                INSERT INTO users (id, email, password_hash, created_at, updated_at) 
                VALUES (?, ?, ?, NOW(), NOW())
            ");
            
            $insertResult = $insertStmt->execute([$userId, $account['email'], $passwordHash]);
            
            if ($insertResult) {
                echo "✅ 用戶創建成功\n";
            } else {
                echo "❌ 用戶創建失敗\n";
            }
        }
        
    } catch (PDOException $e) {
        echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
    }
    
    // 測試 Auth 登入
    echo "\n--- Auth 登入測試 ---\n";
    
    // 模擬 AJAX 登入環境
    $_POST = [
        'ajax_login' => '1',
        'email' => $account['email'],
        'password' => $account['password'],
        'remember_me' => '0'
    ];
    
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_HOST'] = 'localhost';
    $_SERVER['REQUEST_URI'] = '/test';
    $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
    $_SERVER['HTTP_USER_AGENT'] = 'Diagnosis Test Script';
    
    $result = $auth->login($account['email'], $account['password'], false);
    
    if ($result['success']) {
        echo "✅ Auth 登入成功\n";
        echo "訊息: " . ($result['message'] ?? '登入成功') . "\n";
        
        // 檢查 session
        if (isset($_SESSION['user'])) {
            echo "✅ Session 已設置\n";
            echo "Session 用戶ID: " . ($_SESSION['user']['id'] ?? 'N/A') . "\n";
            echo "Session 用戶郵箱: " . ($_SESSION['user']['email'] ?? 'N/A') . "\n";
        } else {
            echo "❌ Session 未設置\n";
        }
        
        // 登出以便測試下一個帳號
        $auth->logout();
        
    } else {
        echo "❌ Auth 登入失敗\n";
        echo "錯誤訊息: " . ($result['message'] ?? '未知錯誤') . "\n";
        
        // 檢查詳細錯誤
        if (isset($result['debug'])) {
            echo "調試信息: " . json_encode($result['debug'], JSON_UNESCAPED_UNICODE) . "\n";
        }
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== 檢查 API 連接 ===\n";

// 測試 API 連接
try {
    $testResult = $apiClient->makeRequest('GET', '/test');
    echo "✅ API 連接正常\n";
    echo "API 響應: " . json_encode($testResult, JSON_UNESCAPED_UNICODE) . "\n";
} catch (Exception $e) {
    echo "❌ API 連接失敗: " . $e->getMessage() . "\n";
}

echo "\n=== 診斷完成 ===\n";
echo "如果問題仍然存在，請檢查:\n";
echo "1. 資料庫連接配置\n";
echo "2. API 服務狀態\n";
echo "3. Session 配置\n";
echo "4. 瀏覽器 JavaScript 控制台錯誤\n";
?>
