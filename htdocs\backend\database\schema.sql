-- SignAttend 資料庫結構
-- 建立資料庫
CREATE DATABASE IF NOT EXISTS signattend CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE signattend;

-- 建立 users 表 (用於認證)
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 建立 profiles 表 (用戶資料)
CREATE TABLE IF NOT EXISTS profiles (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255),
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (id) REFERENCES users(id) ON DELETE CASCADE
);

-- 建立 meetings 表
CREATE TABLE IF NOT EXISTS meetings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    start_date TIMESTAMP NULL,
    end_date TIMESTAMP NULL,
    location VARCHAR(255),
    created_by VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 建立 attendees 表
CREATE TABLE IF NOT EXISTS attendees (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    organization VARCHAR(255),
    position VARCHAR(255),
    meeting_id VARCHAR(36),
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP NULL,
    signature_data TEXT,
    signature_timestamp TIMESTAMP NULL,
    invitation_sent BOOLEAN DEFAULT FALSE,
    invitation_sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_email_meeting (email, meeting_id)
);

-- 建立 password_reset_logs 表 (密碼重設日誌)
CREATE TABLE IF NOT EXISTS password_reset_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 建立索引以提升查詢效能
CREATE INDEX idx_meetings_created_by ON meetings(created_by);
CREATE INDEX idx_attendees_meeting_id ON attendees(meeting_id);
CREATE INDEX idx_attendees_email ON attendees(email);
CREATE INDEX idx_attendees_checked_in ON attendees(checked_in);
CREATE INDEX idx_password_reset_logs_user_id ON password_reset_logs(user_id);
CREATE INDEX idx_password_reset_logs_created_at ON password_reset_logs(created_at);
