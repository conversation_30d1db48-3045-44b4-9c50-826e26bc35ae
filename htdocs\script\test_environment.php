<?php
define('SIGNATTEND_INIT', true);

echo "=== 環境配置測試 ===\n";

// 直接載入 environment.php 來測試
require_once 'config/environment.php';

echo "環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 測試寫入
$testMessage = "[" . date('Y-m-d H:i:s') . "] 環境配置測試\n";
$testLogFile = LOG_PATH . '/env_test_' . date('Y-m-d') . '.log';

if (file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "✓ 成功寫入: " . $testLogFile . "\n";
} else {
    echo "✗ 寫入失敗: " . $testLogFile . "\n";
}

// 檢查外層 logs 目錄
$outerLogsDir = dirname(dirname(__FILE__)) . '/logs';
echo "\n外層 logs 目錄: " . $outerLogsDir . "\n";
if (is_dir($outerLogsDir)) {
    $files = scandir($outerLogsDir);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "外層 logs 文件數量: " . count($logFiles) . "\n";
    if (count($logFiles) > 0) {
        foreach ($logFiles as $file) {
            echo "  - " . $file . "\n";
        }
    }
}
?>
