[2025-06-26 13:55:50] === 新的登入請求開始 ===
[2025-06-26 13:55:50] REQUEST_METHOD: GET
[2025-06-26 13:55:50] HTTP_HOST: attendance.app.tn
[2025-06-26 13:55:50] REQUEST_URI: /debug_real_login.php
[2025-06-26 13:55:50] USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-26 13:55:50] REMOTE_ADDR: 2402:7500:505:fd93:88ba:bb87:589c:aa56
[2025-06-26 13:55:50] 不是 POST 請求，顯示登入表單
[2025-06-26 13:55:50] === 登入請求處理完成 ===

[2025-06-26 13:56:02] === 新的登入請求開始 ===
[2025-06-26 13:56:02] REQUEST_METHOD: POST
[2025-06-26 13:56:02] HTTP_HOST: attendance.app.tn
[2025-06-26 13:56:02] REQUEST_URI: /debug_real_login.php
[2025-06-26 13:56:02] USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-06-26 13:56:02] REMOTE_ADDR: 2402:7500:505:fd93:88ba:bb87:589c:aa56
[2025-06-26 13:56:02] 檢測到 POST 請求
[2025-06-26 13:56:02] POST 資料 - Email: <EMAIL>
[2025-06-26 13:56:02] POST 資料 - Password: [有值]
[2025-06-26 13:56:02] POST 資料 - Remember Me: false
[2025-06-26 13:56:02] POST 資料 - CSRF Token: [有值]
[2025-06-26 13:56:02] Session ID: b95d651092a8fa0b8ce9dc1999f634b0
[2025-06-26 13:56:02] Session 資料: {"csrf_token":"6cbb8c3e77426113869e9a4e140dbe19663d43f3dd63c4f88e24132e39caa918","redirect_after_login":"\/pages\/dashboard.php"}
[2025-06-26 13:56:02] 基本驗證通過，開始登入流程
[2025-06-26 13:56:02] 登入前 - 認證日誌大小: 2441 bytes, 行數: 11
[2025-06-26 13:56:02] 呼叫 auth->login()
[2025-06-26 13:56:03] auth->login() 完成
[2025-06-26 13:56:03] 登入後 - 認證日誌大小: 2778 bytes, 行數: 12
[2025-06-26 13:56:03] 日誌變化 - 大小: 337 bytes, 行數: 1
[2025-06-26 13:56:03] 登入結果: {"success":false,"message":"\u767b\u5165\u5931\u6557"}
[2025-06-26 13:56:03] 登入失敗: 登入失敗
[2025-06-26 13:56:03] ✅ 找到對應的認證日誌記錄
[2025-06-26 13:56:03] 日誌內容: {"timestamp":"2025-06-26 13:56:03","user_id":null,"user_email":"<EMAIL>","action":"login_failed","description":"API 登入失敗：登入失敗","ip_address":"2402:7500:505:fd93:88ba:bb87:589c:aa56","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"}
[2025-06-26 13:56:03] === 登入請求處理完成 ===

