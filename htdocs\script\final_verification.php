<?php
// 最終驗證測試
define('SIGNATTEND_INIT', true);

echo "=== 日誌配置最終驗證 ===\n";

// 載入配置
require_once 'config/config.php';

echo "✅ 配置載入成功\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";
echo "HTDOCS_PATH: " . HTDOCS_PATH . "\n";

// 驗證路徑
$expectedRoot = dirname(dirname(__DIR__));
$expectedLogs = $expectedRoot . '/logs';

echo "\n=== 路徑驗證 ===\n";
echo "預期根目錄: " . $expectedRoot . "\n";
echo "實際根目錄: " . ROOT_PATH . "\n";
echo "根目錄正確: " . (realpath(ROOT_PATH) === realpath($expectedRoot) ? '✅ 是' : '❌ 否') . "\n";

echo "預期日誌目錄: " . $expectedLogs . "\n";
echo "實際日誌目錄: " . LOG_PATH . "\n";
echo "日誌目錄正確: " . (realpath(LOG_PATH) === realpath($expectedLogs) ? '✅ 是' : '❌ 否') . "\n";

// 測試各種日誌寫入
echo "\n=== 測試日誌寫入功能 ===\n";
$timestamp = date('Y-m-d H:i:s');
$dateStr = date('Y-m-d');

$testLogs = [
    'verification_test' => "[$timestamp] 最終驗證測試\n",
    'error_test' => "[$timestamp] ERROR: 測試錯誤日誌\n",
    'api_test' => "[$timestamp] API: 測試 API 日誌\n",
    'auth_test' => "[$timestamp] AUTH: 測試認證日誌\n"
];

foreach ($testLogs as $type => $content) {
    $logFile = LOG_PATH . '/' . $type . '_' . $dateStr . '.log';
    if (file_put_contents($logFile, $content, FILE_APPEND | LOCK_EX)) {
        echo "✅ " . $type . " 日誌寫入成功\n";
    } else {
        echo "❌ " . $type . " 日誌寫入失敗\n";
    }
}

// 檢查外層 logs 目錄內容
echo "\n=== 外層 logs 目錄內容 ===\n";
if (is_dir(LOG_PATH)) {
    $files = scandir(LOG_PATH);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    
    echo "外層 logs 目錄包含 " . count($logFiles) . " 個日誌文件:\n";
    
    // 按修改時間排序，顯示最新的文件
    usort($logFiles, function($a, $b) {
        return filemtime(LOG_PATH . '/' . $b) - filemtime(LOG_PATH . '/' . $a);
    });
    
    foreach (array_slice($logFiles, 0, 10) as $file) {
        $filePath = LOG_PATH . '/' . $file;
        $size = filesize($filePath);
        $mtime = date('Y-m-d H:i:s', filemtime($filePath));
        echo "  📄 " . $file . " (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
    }
}

// 檢查 htdocs/logs 目錄狀態
echo "\n=== htdocs/logs 目錄狀態檢查 ===\n";
$htdocsLogsDir = HTDOCS_PATH . '/logs';
if (is_dir($htdocsLogsDir)) {
    $htdocsFiles = scandir($htdocsLogsDir);
    $htdocsLogFiles = array_filter($htdocsFiles, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    
    echo "htdocs/logs 目錄包含 " . count($htdocsLogFiles) . " 個舊日誌文件\n";
    
    // 檢查是否有最近修改的文件（可能表示配置還沒完全生效）
    $recentFiles = array_filter($htdocsLogFiles, function($file) use ($htdocsLogsDir) {
        $filePath = $htdocsLogsDir . '/' . $file;
        return filemtime($filePath) > (time() - 600); // 最近 10 分鐘
    });
    
    if (!empty($recentFiles)) {
        echo "⚠️  警告：htdocs/logs 中仍有最近修改的文件，可能需要重啟 Web 服務器\n";
        foreach ($recentFiles as $file) {
            $mtime = date('Y-m-d H:i:s', filemtime($htdocsLogsDir . '/' . $file));
            echo "    - " . $file . " (修改: " . $mtime . ")\n";
        }
    } else {
        echo "✅ htdocs/logs 中沒有最近修改的文件\n";
    }
}

// 安全性檢查
echo "\n=== 安全性檢查 ===\n";
echo "外層 logs 目錄位置: " . LOG_PATH . "\n";
echo "Web 根目錄: " . HTDOCS_PATH . "\n";
echo "日誌目錄在 Web 根目錄外: " . (strpos(LOG_PATH, HTDOCS_PATH) === false ? '✅ 是' : '❌ 否') . "\n";

echo "\n=== 總結 ===\n";
if (realpath(LOG_PATH) === realpath($expectedLogs)) {
    echo "🎉 配置修正成功！\n";
    echo "✅ 日誌現在正確寫入到 htdocs 外層的 logs 目錄\n";
    echo "✅ 提升了安全性，日誌文件不再暴露在 Web 可訪問的目錄中\n";
    echo "✅ 符合共享主機的最佳實踐\n";
} else {
    echo "❌ 配置仍需調整\n";
}

echo "\n測試完成！\n";
?>
