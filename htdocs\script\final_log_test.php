<?php
// 最終日誌配置測試
define('SIGNATTEND_INIT', true);

echo "=== 最終日誌配置測試 ===\n";

// 載入環境配置（這是系統實際使用的方式）
require_once 'config/environment.php';

echo "環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 檢查 LOG_PATH 是否指向外層
$expectedOuterPath = dirname(dirname(__FILE__)) . '/logs';
echo "預期外層路徑: " . $expectedOuterPath . "\n";
echo "LOG_PATH 是否指向外層: " . (realpath(LOG_PATH) === realpath($expectedOuterPath) ? '是' : '否') . "\n";

// 使用系統的日誌函數進行測試
echo "\n=== 使用系統函數測試 ===\n";

// 載入共用函數
require_once 'includes/functions.php';

// 測試 debug_log 函數
debug_log('最終測試', ['test' => true, 'timestamp' => time()]);
echo "✓ debug_log 函數測試完成\n";

// 測試錯誤處理函數
$testError = "測試錯誤訊息";
$logMessage = sprintf(
    "[%s] Test Error: %s in %s on line %d\n",
    date('Y-m-d H:i:s'),
    $testError,
    __FILE__,
    __LINE__
);

$errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
if (file_put_contents($errorLogFile, $logMessage, FILE_APPEND | LOCK_EX)) {
    echo "✓ 錯誤日誌寫入成功: " . $errorLogFile . "\n";
} else {
    echo "✗ 錯誤日誌寫入失敗\n";
}

// 檢查外層 logs 目錄的最新內容
echo "\n=== 外層 logs 目錄最新內容 ===\n";
$outerLogsDir = dirname(dirname(__FILE__)) . '/logs';
if (is_dir($outerLogsDir)) {
    $files = scandir($outerLogsDir);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    
    // 按修改時間排序
    usort($logFiles, function($a, $b) use ($outerLogsDir) {
        return filemtime($outerLogsDir . '/' . $b) - filemtime($outerLogsDir . '/' . $a);
    });
    
    echo "外層 logs 目錄包含 " . count($logFiles) . " 個日誌文件:\n";
    foreach (array_slice($logFiles, 0, 10) as $file) { // 只顯示最新的 10 個
        $filePath = $outerLogsDir . '/' . $file;
        $size = filesize($filePath);
        $mtime = date('Y-m-d H:i:s', filemtime($filePath));
        echo "  - " . $file . " (大小: " . $size . " bytes, 修改時間: " . $mtime . ")\n";
    }
}

// 檢查 htdocs/logs 目錄狀態
echo "\n=== htdocs/logs 目錄狀態 ===\n";
$htdocsLogsDir = dirname(__FILE__) . '/logs';
if (is_dir($htdocsLogsDir)) {
    $htdocsFiles = scandir($htdocsLogsDir);
    $htdocsLogFiles = array_filter($htdocsFiles, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "htdocs/logs 目錄包含 " . count($htdocsLogFiles) . " 個日誌文件\n";
    
    // 檢查是否還有新的日誌寫入 htdocs/logs
    $recentFiles = array_filter($htdocsLogFiles, function($file) use ($htdocsLogsDir) {
        $filePath = $htdocsLogsDir . '/' . $file;
        return filemtime($filePath) > (time() - 300); // 最近 5 分鐘內修改的文件
    });
    
    if (!empty($recentFiles)) {
        echo "警告：htdocs/logs 中仍有最近修改的文件:\n";
        foreach ($recentFiles as $file) {
            $filePath = $htdocsLogsDir . '/' . $file;
            $mtime = date('Y-m-d H:i:s', filemtime($filePath));
            echo "  - " . $file . " (修改時間: " . $mtime . ")\n";
        }
    } else {
        echo "✓ htdocs/logs 中沒有最近修改的文件\n";
    }
}

echo "\n=== 測試結論 ===\n";
if (realpath(LOG_PATH) === realpath($expectedOuterPath)) {
    echo "✅ 日誌配置正確：日誌已成功導向外層 logs 目錄\n";
    echo "✅ 安全性提升：日誌文件不再暴露在 Web 可訪問的目錄中\n";
} else {
    echo "❌ 日誌配置仍需調整\n";
}

echo "\n測試完成！\n";
?>
