<?php
/**
 * SignAttend PHP Frontend - 簽到頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入配置檔案
require_once __DIR__ . '/../config/config.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();

// 取得參數
$meetingId = $_GET['meeting_id'] ?? '';
$attendeeId = $_GET['attendee_id'] ?? '';

$error = '';
$success = '';
$meeting = null;
$attendee = null;

// 驗證參數
if (empty($meetingId)) {
    $error = '缺少會議 ID';
} else {
    // 獲取會議資訊
    try {
        $meetingResponse = $apiClient->get("/meetings/$meetingId");
        if ($meetingResponse['success'] && isset($meetingResponse['data']['data'])) {
            $meeting = $meetingResponse['data']['data'];
        } else {
            $error = '會議不存在或已結束';
        }
    } catch (Exception $e) {
        $error = '無法獲取會議資訊：' . $e->getMessage();
    }
}

// 如果有指定參與者 ID，獲取參與者資訊
if (!empty($attendeeId) && !$error) {
    try {
        $attendeeResponse = $apiClient->get("/attendees/$attendeeId");
        if ($attendeeResponse['success'] && isset($attendeeResponse['data']['data'])) {
            $attendee = $attendeeResponse['data']['data'];
        }
    } catch (Exception $e) {
        // 參與者不存在，但不算錯誤，可能是通用簽到
    }
}

// 處理簽到表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $organization = $_POST['organization'] ?? '';
    $position = $_POST['position'] ?? '';
    $signatureData = $_POST['signature_data'] ?? '';
    
    if (empty($name) || empty($email)) {
        $error = '請填寫姓名和電子郵件';
    } else {
        try {
            if ($attendee) {
                // 已知參與者簽到
                $checkinResponse = $apiClient->post("/attendees/{$attendee['id']}/checkin", [
                    'signature_data' => $signatureData
                ]);
                
                if ($checkinResponse['success']) {
                    $success = '簽到成功！歡迎參加會議。';
                } else {
                    $error = $checkinResponse['data']['message'] ?? '簽到失敗';
                }
            } else {
                // 新參與者註冊並簽到
                $attendeeData = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'organization' => $organization,
                    'position' => $position,
                    'checked_in' => true,
                    'signature_data' => $signatureData
                ];
                
                $createResponse = $apiClient->post("/meetings/$meetingId/attendees", $attendeeData);
                
                if ($createResponse['success']) {
                    $success = '註冊並簽到成功！歡迎參加會議。';
                } else {
                    $error = $createResponse['data']['message'] ?? '註冊失敗';
                }
            }
        } catch (Exception $e) {
            $error = '簽到過程發生錯誤：' . $e->getMessage();
        }
    }
}

// 頁面標題
$pageTitle = '會議簽到';

// 包含頁頭
include_once COMPONENTS_PATH . '/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h2 class="text-center mb-4">
                        <i class="bi bi-qr-code-scan"></i> 會議簽到
                    </h2>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> <?= h($error) ?>
                        </div>
                    <?php elseif ($success): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> <?= h($success) ?>
                        </div>
                        <div class="text-center">
                            <a href="<?= page_url('index.php') ?>" class="btn btn-primary">返回首頁</a>
                        </div>
                    <?php elseif ($meeting): ?>
                        <!-- 會議資訊 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">會議資訊</h5>
                            </div>
                            <div class="card-body">
                                <h6><?= h($meeting['name']) ?></h6>
                                <p class="text-muted mb-1"><?= h($meeting['description']) ?></p>
                                <small class="text-muted">
                                    <i class="bi bi-calendar"></i> <?= format_datetime($meeting['start_date']) ?>
                                    <?php if ($meeting['location']): ?>
                                        <br><i class="bi bi-geo-alt"></i> <?= h($meeting['location']) ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        
                        <!-- 簽到表單 -->
                        <form method="POST" action="" id="checkinForm">
                            <?php generate_csrf_token_field(); ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">姓名 *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= h($attendee['name'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">電子郵件 *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= h($attendee['email'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">電話</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= h($attendee['phone'] ?? '') ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="organization" class="form-label">機構/公司</label>
                                <input type="text" class="form-control" id="organization" name="organization" 
                                       value="<?= h($attendee['organization'] ?? '') ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="position" class="form-label">職位</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="<?= h($attendee['position'] ?? '') ?>">
                            </div>
                            
                            <!-- 簽名區域 -->
                            <div class="mb-3">
                                <label class="form-label">數位簽名（選填）</label>
                                <div class="border rounded p-3 text-center">
                                    <canvas id="signaturePad" width="400" height="150" style="border: 1px dashed #ccc; max-width: 100%;"></canvas>
                                    <br>
                                    <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearSignature()">
                                        清除簽名
                                    </button>
                                </div>
                                <input type="hidden" name="signature_data" id="signatureData">
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="bi bi-check-circle"></i> 確認簽到
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 簽名板功能
let canvas = document.getElementById('signaturePad');
let ctx = canvas.getContext('2d');
let isDrawing = false;

canvas.addEventListener('mousedown', startDrawing);
canvas.addEventListener('mousemove', draw);
canvas.addEventListener('mouseup', stopDrawing);
canvas.addEventListener('mouseout', stopDrawing);

// 觸控支援
canvas.addEventListener('touchstart', handleTouch);
canvas.addEventListener('touchmove', handleTouch);
canvas.addEventListener('touchend', stopDrawing);

function startDrawing(e) {
    isDrawing = true;
    draw(e);
}

function draw(e) {
    if (!isDrawing) return;
    
    let rect = canvas.getBoundingClientRect();
    let x = e.clientX - rect.left;
    let y = e.clientY - rect.top;
    
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#000';
    
    ctx.lineTo(x, y);
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(x, y);
}

function stopDrawing() {
    if (isDrawing) {
        isDrawing = false;
        ctx.beginPath();
        
        // 將簽名轉換為 base64
        let signatureData = canvas.toDataURL();
        document.getElementById('signatureData').value = signatureData;
    }
}

function handleTouch(e) {
    e.preventDefault();
    let touch = e.touches[0];
    let mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                   e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function clearSignature() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    document.getElementById('signatureData').value = '';
}
</script>

<?php
// 包含頁尾
include_once COMPONENTS_PATH . '/footer.php';
?>
