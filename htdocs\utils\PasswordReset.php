<?php
/**
 * 密碼重設處理類別
 */

class PasswordReset {
    private $pdo;
    
    public function __construct() {
        try {
            // 檢查必要的資料庫常數
            if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER') || !defined('DB_PASS')) {
                throw new Exception("資料庫配置常數未定義");
            }

            // 使用與其他類別相同的資料庫連接方式
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO(
                $dsn,
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 10
                ]
            );

            // 測試連接
            $this->pdo->query("SELECT 1");

        } catch (PDOException $e) {
            $errorMsg = "PasswordReset database connection failed: " . $e->getMessage();
            error_log($errorMsg);

            // 在開發環境顯示詳細錯誤
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                throw new Exception("資料庫連接失敗: " . $e->getMessage());
            } else {
                throw new Exception("資料庫連接失敗，請稍後再試");
            }
        } catch (Exception $e) {
            error_log("PasswordReset initialization failed: " . $e->getMessage());
            throw new Exception("系統初始化失敗: " . $e->getMessage());
        }
    }
    
    /**
     * 處理忘記密碼請求
     */
    public function handleForgotPassword($email) {
        try {
            // 檢查用戶是否存在
            $stmt = $this->pdo->prepare("SELECT id, email FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                return [
                    'success' => false,
                    'message' => '找不到此電子郵件對應的帳號'
                ];
            }
            
            // 生成臨時密碼
            $tempPassword = $this->generateTempPassword();
            
            // 更新用戶密碼
            $passwordHash = password_hash($tempPassword, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET password_hash = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$passwordHash, $user['id']]);
            
            // 記錄密碼重設日誌
            $this->logPasswordReset($user['id'], $email);
            
            return [
                'success' => true,
                'message' => '密碼重設成功！您的新臨時密碼已生成。',
                'temp_password' => $tempPassword,
                'email' => $email,
                'note' => '請使用此臨時密碼登入，並儘快修改為您的個人密碼。'
            ];
            
        } catch (PDOException $e) {
            error_log("Password reset error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '系統錯誤，請稍後再試'
            ];
        }
    }
    
    /**
     * 生成臨時密碼
     */
    private function generateTempPassword($length = 8) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
    
    /**
     * 記錄密碼重設日誌
     */
    private function logPasswordReset($userId, $email) {
        try {
            // 檢查是否有 password_reset_logs 表
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'password_reset_logs'");
            
            if ($stmt->rowCount() == 0) {
                // 創建密碼重設日誌表
                $this->createPasswordResetLogsTable();
            }
            
            // 記錄密碼重設
            $stmt = $this->pdo->prepare("
                INSERT INTO password_reset_logs 
                (user_id, user_email, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $email,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
        } catch (PDOException $e) {
            error_log("Password reset logging error: " . $e->getMessage());
        }
    }
    
    /**
     * 創建密碼重設日誌表
     */
    private function createPasswordResetLogsTable() {
        $sql = "
            CREATE TABLE password_reset_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                user_email VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_user_email (user_email),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $this->pdo->exec($sql);
    }
    
    /**
     * 獲取密碼重設統計
     */
    public function getResetStats($email = null) {
        try {
            if ($email) {
                $stmt = $this->pdo->prepare("
                    SELECT COUNT(*) as total_resets,
                           MAX(created_at) as last_reset
                    FROM password_reset_logs 
                    WHERE user_email = ?
                ");
                $stmt->execute([$email]);
            } else {
                $stmt = $this->pdo->query("
                    SELECT COUNT(*) as total_resets,
                           COUNT(DISTINCT user_email) as unique_users,
                           MAX(created_at) as last_reset
                    FROM password_reset_logs
                ");
            }
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * 獲取最近的密碼重設記錄
     */
    public function getRecentResets($limit = 10) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT user_email, ip_address, created_at
                FROM password_reset_logs 
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            return [];
        }
    }
}
?>
