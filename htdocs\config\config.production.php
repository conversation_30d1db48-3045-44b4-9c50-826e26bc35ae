<?php
/**
 * SignAttend PHP Frontend 正式環境配置檔案
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 防止直接訪問
if (!defined('SIGNATTEND_INIT')) {
    die('Direct access not allowed');
}

// 應用程式基本配置
define('APP_NAME', 'SignAttend');
define('APP_VERSION', '2.0.0');
define('APP_DESCRIPTION', '簽易通');

// 路徑配置 - 正式環境
define('BASE_URL', 'https://attendance.app.tn');
define('API_BASE_URL', 'https://attendance.app.tn/backend/api');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// 目錄路徑
// ROOT_PATH 指向 htdocs 的上一層目錄（專案根目錄）
// config.production.php 位於 htdocs/config/，所以需要三層 dirname 才能到達專案根目錄
define('ROOT_PATH', dirname(dirname(dirname(__FILE__))));
// HTDOCS_PATH 指向 htdocs 目錄
define('HTDOCS_PATH', dirname(dirname(__FILE__)));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', HTDOCS_PATH . '/includes');
define('PAGES_PATH', HTDOCS_PATH . '/pages');
define('COMPONENTS_PATH', HTDOCS_PATH . '/components');
define('UTILS_PATH', HTDOCS_PATH . '/utils');
define('UPLOADS_PATH', HTDOCS_PATH . '/uploads');

// 安全配置
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 7200); // 2 小時
define('SESSION_NAME', 'SIGNATTEND_SESSION');
define('COOKIE_LIFETIME', 86400); // 24 小時

// 檔案上傳配置
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'xlsx', 'csv']);
define('ALLOWED_MIME_TYPES', [
    'image/jpeg',
    'image/png', 
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
]);

// 分頁配置
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// 快取配置
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 600); // 10 分鐘

// 錯誤處理配置 - 正式環境
define('DEBUG_MODE', false); // 正式環境關閉除錯模式
define('LOG_ERRORS', true);
define('LOG_PATH', ROOT_PATH . '/logs');

// 載入外部敏感配置
$possiblePaths = [
    dirname(__DIR__, 2) . '/config/secrets.production.php', // 正確路徑：../../config/secrets.production.php
    dirname(__DIR__) . '/../config/secrets.production.php', // 備用路徑 1
    '../../config/secrets.production.php',                  // 直接相對路徑
    '../config/secrets.production.php'                      // 相對路徑備用
];

$secretsLoaded = false;
foreach ($possiblePaths as $secretsFile) {
    if (file_exists($secretsFile)) {
        require_once $secretsFile;
        $secretsLoaded = true;
        error_log('成功載入外部敏感配置：' . $secretsFile);
        break;
    }
}

if (!$secretsLoaded) {
    error_log('警告：無法載入外部敏感配置文件，嘗試的路徑：' . implode(', ', $possiblePaths));
}

// 資料庫配置 - 正式環境
define('DB_HOST', 'sql302.byetcluster.com');
define('DB_NAME', 'uoolo_38699510_signattend');
define('DB_USER', 'uoolo_38699510');
define('DB_PASS', defined('DB_PASSWORD_PRODUCTION') ? DB_PASSWORD_PRODUCTION : 'kai@0932540826');

// 時區設定
date_default_timezone_set('Asia/Taipei');

// 語言設定
define('DEFAULT_LANGUAGE', 'zh-TW');
define('SUPPORTED_LANGUAGES', ['zh-TW', 'en-US']);

// 郵件配置
define('MAIL_ENABLED', true);
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>'); // 請修改為實際郵箱
define('MAIL_PASSWORD', 'your_app_password'); // 請修改為實際密碼
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'SignAttend System');

// QR Code 配置
define('QR_CODE_SIZE', 200);
define('QR_CODE_MARGIN', 2);
define('QR_CODE_ERROR_CORRECTION', 'M'); // L, M, Q, H

// PDF 配置
define('PDF_FONT_SIZE', 12);
define('PDF_MARGIN', 15);
define('PDF_ORIENTATION', 'P'); // P=Portrait, L=Landscape

// API 配置
define('API_TIMEOUT', 30); // 秒
define('API_RETRY_COUNT', 3);

// 前端資源版本 (用於快取控制)
define('ASSETS_VERSION', '2.0.1');

// 功能開關
define('FEATURE_QR_SCANNER', true);
define('FEATURE_SIGNATURE_PAD', true);
define('FEATURE_EMAIL_NOTIFICATIONS', true);
define('FEATURE_BULK_IMPORT', true);
define('FEATURE_PDF_EXPORT', true);
define('FEATURE_EXCEL_EXPORT', true);

// 主題配置
define('DEFAULT_THEME', 'light');
define('AVAILABLE_THEMES', ['light', 'dark']);

// 正式環境配置
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('log_errors_max_len', 1024);

// 建立必要目錄
$requiredDirs = [UPLOADS_PATH, LOG_PATH];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 設定錯誤處理函數
function signattend_error_handler($errno, $errstr, $errfile, $errline) {
    if (LOG_ERRORS) {
        $logMessage = sprintf(
            "[%s] Error %d: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $errno,
            $errstr,
            $errfile,
            $errline
        );
        
        $logFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    // 正式環境不顯示詳細錯誤
    return true;
}

// 設定異常處理函數
function signattend_exception_handler($exception) {
    if (LOG_ERRORS) {
        $logMessage = sprintf(
            "[%s] Uncaught Exception: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        );
        
        $logFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    // 正式環境顯示通用錯誤訊息
    echo "<div class='alert alert-danger'>系統發生錯誤，請稍後再試。如問題持續發生，請聯繫系統管理員。</div>";
}

// 註冊錯誤處理函數
set_error_handler('signattend_error_handler');
set_exception_handler('signattend_exception_handler');

// 輔助函數
function asset_url($path) {
    return ASSETS_URL . '/' . ltrim($path, '/') . '?v=' . ASSETS_VERSION;
}

function upload_url($path) {
    return UPLOADS_URL . '/' . ltrim($path, '/');
}

function page_url($path) {
    return BASE_URL . '/' . ltrim($path, '/');
}

function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// 安全標頭設定
function set_security_headers() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // 更寬鬆的CSP配置以支援Dashboard所需的外部資源
    $csp = "default-src 'self'; " .
           "script-src 'self' 'unsafe-inline' 'unsafe-eval' " .
           "https://cdn.jsdelivr.net https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://unpkg.com; " .
           "style-src 'self' 'unsafe-inline' " .
           "https://cdn.jsdelivr.net https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://unpkg.com; " .
           "img-src 'self' data: https: blob:; " .
           "font-src 'self' data: https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
           "connect-src 'self' https: wss: ws:; " .
           "media-src 'self' blob:; " .
           "object-src 'none'; " .
           "base-uri 'self';";

    header('Content-Security-Policy: ' . $csp);
}

// 設定安全標頭
set_security_headers();

// 清理輸出緩衝區
if (ob_get_level()) {
    ob_end_clean();
}

// 啟用輸出緩衝
ob_start();

// 設定 PHP 配置 (只在 session 未啟動時設定)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
}
/*
// 檢查 HTTPS (考慮反向代理的情況)
function isHttps() {
    // 檢查標準 HTTPS
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        return true;
    }

    // 檢查反向代理標頭
    if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        return true;
    }

    // 檢查端口
    if (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) {
        return true;
    }

    return false;
}

// 只在非 HTTPS 時重定向
if (!isHttps() && !headers_sent()) {
    header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'], true, 301);
    exit();
}
*/
