<?php
// 調試路由問題

echo "=== 調試 Flight PHP 路由 ===\n\n";

// 檢查 Flight 是否正確載入
if (!class_exists('Flight')) {
    echo "❌ Flight 類別未載入\n";
    require 'vendor/autoload.php';
    if (class_exists('Flight')) {
        echo "✅ Flight 類別已載入\n";
    } else {
        echo "❌ Flight 類別仍未載入\n";
        exit;
    }
} else {
    echo "✅ Flight 類別已載入\n";
}

// 檢查 namespace 類別
echo "\n=== 檢查 Namespace 類別 ===\n";
$classes = [
    'SignAttend\\Controllers\\MeetingController',
    'SignAttend\\Models\\Meeting'
];

foreach ($classes as $class) {
    if (class_exists($class)) {
        echo "✅ $class 存在\n";
    } else {
        echo "❌ $class 不存在\n";
    }
}

// 檢查資料庫連接
echo "\n=== 檢查資料庫連接 ===\n";
try {
    $dbConfig = require 'config/database.php';
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "✅ 資料庫連接成功\n";
    
    // 檢查 meetings 表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'meetings'");
    if ($stmt->rowCount() > 0) {
        echo "✅ meetings 表存在\n";
    } else {
        echo "❌ meetings 表不存在\n";
    }
    
} catch (Exception $e) {
    echo "❌ 資料庫連接失敗: " . $e->getMessage() . "\n";
}

// 檢查當前請求信息
echo "\n=== 當前請求信息 ===\n";
echo "REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A') . "\n";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "\n";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'N/A') . "\n";
echo "PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'N/A') . "\n";

// 測試直接創建 MeetingController
echo "\n=== 測試直接創建 MeetingController ===\n";
try {
    if (isset($pdo)) {
        $controller = new SignAttend\Controllers\MeetingController($pdo);
        echo "✅ MeetingController 創建成功\n";
        
        // 測試 Meeting 模型
        $meeting = new SignAttend\Models\Meeting($pdo);
        echo "✅ Meeting 模型創建成功\n";
    }
} catch (Exception $e) {
    echo "❌ 創建失敗: " . $e->getMessage() . "\n";
}

// 檢查 .htaccess
echo "\n=== 檢查 .htaccess ===\n";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess 文件存在\n";
    echo "內容:\n";
    echo file_get_contents('.htaccess') . "\n";
} else {
    echo "❌ .htaccess 文件不存在\n";
}

// 檢查 mod_rewrite
echo "\n=== 檢查 mod_rewrite ===\n";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "✅ mod_rewrite 已啟用\n";
    } else {
        echo "❌ mod_rewrite 未啟用\n";
    }
} else {
    echo "⚠️ 無法檢查 Apache 模組\n";
}

echo "\n=== 調試完成 ===\n";
?>
