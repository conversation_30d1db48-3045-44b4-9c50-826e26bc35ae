<?php
/**
 * 資料庫日誌記錄器
 * 負責將登入記錄寫入資料庫而不是檔案
 */

class DatabaseLogger {
    private $pdo;
    
    public function __construct() {
        $this->initDatabase();
    }
    
    /**
     * 初始化資料庫連接
     */
    private function initDatabase() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4, time_zone = '+08:00'"
                ]
            );
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 資料庫連接失敗 - " . $e->getMessage());
            $this->pdo = null;
        }
    }
    
    /**
     * 確保登入記錄表存在
     */
    private function ensureLoginLogsTable() {
        if (!$this->pdo) {
            return false;
        }
        
        try {
            // 檢查表是否存在
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'login_logs'");
            if ($stmt->rowCount() > 0) {
                return true;
            }
            
            // 創建表
            $sql = "
                CREATE TABLE login_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id VARCHAR(36) NULL,
                    user_email VARCHAR(255) NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    description TEXT NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent TEXT NULL,
                    success BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_login_logs_user_id (user_id),
                    INDEX idx_login_logs_user_email (user_email),
                    INDEX idx_login_logs_action (action),
                    INDEX idx_login_logs_success (success),
                    INDEX idx_login_logs_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->pdo->exec($sql);
            return true;
            
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 無法創建 login_logs 表 - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 記錄登入活動到資料庫
     * 
     * @param string $action 動作類型 (login, login_failed, logout 等)
     * @param string $description 描述
     * @param string|null $email 用戶郵箱
     * @param string|null $userId 用戶ID
     * @param bool $success 是否成功
     * @return bool 是否記錄成功
     */
    public function logActivity($action, $description, $email = null, $userId = null, $success = false) {
        if (!$this->pdo) {
            // 如果資料庫不可用，回退到檔案日誌
            return $this->fallbackToFileLog($action, $description, $email, $userId, $success);
        }
        
        // 確保表存在
        if (!$this->ensureLoginLogsTable()) {
            return $this->fallbackToFileLog($action, $description, $email, $userId, $success);
        }
        
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO login_logs (
                    user_id, user_email, action, description, 
                    ip_address, user_agent, success, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $result = $stmt->execute([
                $userId,
                $email,
                $action,
                $description,
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $success ? 1 : 0
            ]);
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 記錄登入活動失敗 - " . $e->getMessage());
            // 回退到檔案日誌
            return $this->fallbackToFileLog($action, $description, $email, $userId, $success);
        }
    }
    
    /**
     * 回退到檔案日誌記錄
     */
    private function fallbackToFileLog($action, $description, $email, $userId, $success) {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'user_id' => $userId,
                'user_email' => $email,
                'action' => $action,
                'description' => $description,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'success' => $success
            ];

            $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            $logFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
            
            $result = file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
            
            if ($result === false) {
                $result = file_put_contents($logFile, $logMessage, FILE_APPEND);
            }
            
            return $result !== false;
            
        } catch (Exception $e) {
            error_log("DatabaseLogger: 檔案日誌回退也失敗 - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 獲取最近的登入記錄
     * 
     * @param int $limit 記錄數量限制
     * @param string|null $email 篩選特定郵箱
     * @return array 登入記錄陣列
     */
    public function getRecentLogs($limit = 50, $email = null) {
        if (!$this->pdo || !$this->ensureLoginLogsTable()) {
            return [];
        }
        
        try {
            $sql = "SELECT * FROM login_logs";
            $params = [];
            
            if ($email) {
                $sql .= " WHERE user_email = ?";
                $params[] = $email;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 獲取登入記錄失敗 - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 獲取登入統計
     * 
     * @param string|null $email 篩選特定郵箱
     * @param int $days 統計天數
     * @return array 統計資料
     */
    public function getLoginStats($email = null, $days = 7) {
        if (!$this->pdo || !$this->ensureLoginLogsTable()) {
            return [
                'total_attempts' => 0,
                'successful_logins' => 0,
                'failed_attempts' => 0,
                'success_rate' => 0
            ];
        }
        
        try {
            $sql = "
                SELECT 
                    COUNT(*) as total_attempts,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_logins,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_attempts
                FROM login_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ";
            
            $params = [$days];
            
            if ($email) {
                $sql .= " AND user_email = ?";
                $params[] = $email;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch();
            
            $successRate = $result['total_attempts'] > 0 
                ? round(($result['successful_logins'] / $result['total_attempts']) * 100, 2)
                : 0;
            
            return [
                'total_attempts' => (int)$result['total_attempts'],
                'successful_logins' => (int)$result['successful_logins'],
                'failed_attempts' => (int)$result['failed_attempts'],
                'success_rate' => $successRate
            ];
            
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 獲取登入統計失敗 - " . $e->getMessage());
            return [
                'total_attempts' => 0,
                'successful_logins' => 0,
                'failed_attempts' => 0,
                'success_rate' => 0
            ];
        }
    }
    
    /**
     * 清理舊的登入記錄
     * 
     * @param int $days 保留天數
     * @return bool 是否清理成功
     */
    public function cleanOldLogs($days = 30) {
        if (!$this->pdo || !$this->ensureLoginLogsTable()) {
            return false;
        }
        
        try {
            $stmt = $this->pdo->prepare("
                DELETE FROM login_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            return $stmt->execute([$days]);
            
        } catch (PDOException $e) {
            error_log("DatabaseLogger: 清理舊日誌失敗 - " . $e->getMessage());
            return false;
        }
    }
}
?>
