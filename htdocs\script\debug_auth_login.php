<?php
// 調試 Auth 類的 login 方法
define('SIGNATTEND_INIT', true);

// 載入配置
require_once 'config/config.php';
require_once 'utils/Auth.php';

echo "=== Auth 類 login 方法調試 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n\n";

// 創建 Auth 實例
$auth = new Auth();

// 獲取 Auth 類的反射
$reflectionClass = new ReflectionClass('Auth');

// 檢查 login 方法
$loginMethod = $reflectionClass->getMethod('login');
echo "login 方法存在: " . ($loginMethod ? '是' : '否') . "\n";
echo "login 方法參數數量: " . $loginMethod->getNumberOfParameters() . "\n";

// 檢查 logActivity 方法
$logActivityMethod = $reflectionClass->getMethod('logActivity');
$logActivityMethod->setAccessible(true);
echo "logActivity 方法存在: " . ($logActivityMethod ? '是' : '否') . "\n";

// 創建一個測試用的 login 方法，添加調試輸出
function debugLogin($auth, $email, $password) {
    echo "開始調用 login 方法...\n";
    echo "參數: email=$email, password=[有值]\n";
    
    // 記錄調用前的日誌文件大小
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "調用前日誌大小: $beforeSize bytes\n";
    
    // 調用 login 方法
    $result = $auth->login($email, $password, false);
    
    // 記錄調用後的日誌文件大小
    $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "調用後日誌大小: $afterSize bytes\n";
    echo "日誌增長: " . ($afterSize - $beforeSize) . " bytes\n";
    
    // 輸出結果
    echo "login 方法返回結果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 如果日誌沒有增長，手動調用 logActivity
    if ($afterSize <= $beforeSize) {
        echo "日誌沒有增長，嘗試手動調用 logActivity...\n";
        
        // 使用反射調用 logActivity
        $reflectionClass = new ReflectionClass('Auth');
        $logActivityMethod = $reflectionClass->getMethod('logActivity');
        $logActivityMethod->setAccessible(true);
        
        try {
            $logActivityMethod->invoke($auth, 'login_failed_debug', '調試登入失敗', $email);
            echo "手動調用 logActivity 成功\n";
            
            // 再次檢查日誌大小
            $manualAfterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
            echo "手動調用後日誌大小: $manualAfterSize bytes\n";
            echo "手動調用增長: " . ($manualAfterSize - $afterSize) . " bytes\n";
        } catch (Exception $e) {
            echo "手動調用 logActivity 失敗: " . $e->getMessage() . "\n";
        }
    }
    
    return $result;
}

// 測試登入失敗情況
echo "\n=== 測試登入失敗 ===\n";
$result = debugLogin($auth, '<EMAIL>', 'wrong_password');

// 檢查 Auth 類中的 login 方法源碼
echo "\n=== 檢查 Auth 類中的 login 方法源碼 ===\n";
try {
    $loginMethod = $reflectionClass->getMethod('login');
    $loginSource = file_get_contents('utils/Auth.php');
    
    // 提取 login 方法的源碼
    preg_match('/public\s+function\s+login\s*\([^)]*\)\s*{(.*?)}/s', $loginSource, $matches);
    
    if (!empty($matches[1])) {
        $loginCode = $matches[1];
        echo "login 方法源碼片段:\n";
        
        // 查找是否有調用 logActivity 的代碼
        if (strpos($loginCode, 'logActivity') !== false) {
            echo "✅ 找到 logActivity 調用\n";
            
            // 提取 logActivity 調用的行
            preg_match_all('/\$this->logActivity\([^;]*\);/', $loginCode, $callMatches);
            
            if (!empty($callMatches[0])) {
                echo "logActivity 調用行:\n";
                foreach ($callMatches[0] as $i => $call) {
                    echo ($i + 1) . ". " . trim($call) . "\n";
                }
            }
        } else {
            echo "❌ 沒有找到 logActivity 調用\n";
        }
    } else {
        echo "無法提取 login 方法源碼\n";
    }
} catch (Exception $e) {
    echo "檢查源碼失敗: " . $e->getMessage() . "\n";
}

// 檢查 Auth 類中的 logActivity 方法源碼
echo "\n=== 檢查 Auth 類中的 logActivity 方法源碼 ===\n";
try {
    $logActivityMethod = $reflectionClass->getMethod('logActivity');
    $logActivityMethod->setAccessible(true);
    
    // 提取 logActivity 方法的源碼
    preg_match('/private\s+function\s+logActivity\s*\([^)]*\)\s*{(.*?)}/s', $loginSource, $matches);
    
    if (!empty($matches[1])) {
        $logActivityCode = $matches[1];
        echo "logActivity 方法源碼片段:\n";
        
        // 檢查是否有條件判斷
        if (strpos($logActivityCode, 'if (!LOG_ERRORS)') !== false) {
            echo "❌ 發現條件判斷: if (!LOG_ERRORS) return;\n";
            echo "這可能是問題所在，如果 LOG_ERRORS 為 false，則不會記錄日誌\n";
        } else {
            echo "✅ 沒有發現 LOG_ERRORS 條件判斷\n";
        }
        
        // 檢查文件寫入代碼
        if (strpos($logActivityCode, 'file_put_contents') !== false) {
            echo "✅ 找到 file_put_contents 調用\n";
        } else {
            echo "❌ 沒有找到 file_put_contents 調用\n";
        }
    } else {
        echo "無法提取 logActivity 方法源碼\n";
    }
} catch (Exception $e) {
    echo "檢查源碼失敗: " . $e->getMessage() . "\n";
}

// 檢查 LOG_ERRORS 常量
echo "\n=== 檢查 LOG_ERRORS 常量 ===\n";
if (defined('LOG_ERRORS')) {
    echo "LOG_ERRORS 已定義: " . (LOG_ERRORS ? 'true' : 'false') . "\n";
} else {
    echo "LOG_ERRORS 未定義\n";
}

echo "\n測試完成！\n";
?>