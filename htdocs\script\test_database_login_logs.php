<?php
/**
 * 測試資料庫登入記錄功能
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';
require_once UTILS_PATH . '/DatabaseLogger.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫登入記錄測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .log-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .log-table th, .log-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .log-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .log-table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 資料庫登入記錄測試</h1>
        
        <div class="result info">
測試說明：
1. 登入記錄現在會寫入資料庫的 login_logs 表
2. 測試 AJAX 登入功能並檢查資料庫記錄
3. 查看登入統計和最近的記錄
        </div>

        <form id="testForm">
            <?php generate_csrf_token_field(); ?>
            
            <div class="form-group">
                <label for="email">電子郵件：</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">密碼：</label>
                <input type="password" id="password" name="password" value="test123" required>
            </div>

            <button type="submit" class="btn" id="submitBtn">🚀 測試 AJAX 登入</button>
            <button type="button" class="btn" onclick="refreshStats()">📊 重新整理統計</button>
            <button type="button" class="btn" onclick="showRecentLogs()">📋 顯示最近記錄</button>
        </form>

        <div id="result"></div>
    </div>

    <div class="container">
        <h2>📊 登入統計</h2>
        <div id="statsContainer">載入中...</div>
    </div>

    <div class="container">
        <h2>📋 最近的登入記錄</h2>
        <div id="logsContainer">載入中...</div>
    </div>

    <script>
        // 自動生成測試郵箱
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('email');
            const timestamp = Math.floor(Date.now() / 1000);
            emailInput.value = 'db_test_' + timestamp + '@example.com';
            
            // 載入初始統計
            refreshStats();
            showRecentLogs();
        });

        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用按鈕
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 測試中...';
            
            // 清除之前的結果
            resultDiv.innerHTML = '';
            
            // 準備表單數據
            const formData = new FormData();
            formData.append('ajax_login', '1');
            formData.append('email', document.getElementById('email').value);
            formData.append('password', document.getElementById('password').value);
            
            // 添加 CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]');
            if (csrfToken) {
                formData.append('csrf_token', csrfToken.value);
            }
            
            const startTime = new Date();
            
            // 發送 AJAX 請求
            fetch('pages/login.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                const endTime = new Date();
                const duration = endTime - startTime;
                
                return response.text().then(text => {
                    return {
                        status: response.status,
                        text: text,
                        duration: duration
                    };
                });
            })
            .then(data => {
                let resultText = '';
                resultText += '=== AJAX 登入測試結果 ===\n';
                resultText += '時間: ' + new Date().toISOString() + '\n';
                resultText += '狀態碼: ' + data.status + '\n';
                resultText += '響應時間: ' + data.duration + 'ms\n\n';
                
                // 嘗試解析 JSON 響應
                try {
                    const jsonData = JSON.parse(data.text);
                    
                    resultText += '=== 登入結果 ===\n';
                    resultText += '成功: ' + (jsonData.success ? '是' : '否') + '\n';
                    resultText += '訊息: ' + (jsonData.message || 'N/A') + '\n';
                    
                    if (jsonData.debug_info) {
                        const debugInfo = jsonData.debug_info;
                        const recordChange = debugInfo.record_change || 0;
                        
                        resultText += '\n=== 資料庫記錄檢查 ===\n';
                        resultText += '儲存類型: ' + debugInfo.storage_type + '\n';
                        resultText += '資料表: ' + debugInfo.table_name + '\n';
                        resultText += '登入前記錄數: ' + debugInfo.before_count + '\n';
                        resultText += '登入後記錄數: ' + debugInfo.after_count + '\n';
                        resultText += '記錄變化: ' + recordChange + '\n';
                        
                        if (recordChange > 0) {
                            resultText += '\n✅ 資料庫記錄寫入成功！\n';
                            resultDiv.className = 'result success';
                        } else {
                            resultText += '\n❌ 資料庫記錄寫入失敗！\n';
                            resultDiv.className = 'result error';
                        }
                    }
                    
                    resultText += '\n=== 完整響應 ===\n';
                    resultText += JSON.stringify(jsonData, null, 2);
                    
                } catch (e) {
                    resultText += '=== JSON 解析失敗 ===\n';
                    resultText += '錯誤: ' + e.message + '\n\n';
                    resultText += '=== 原始響應 ===\n';
                    resultText += data.text;
                    
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = resultText;
                if (!resultDiv.className) {
                    resultDiv.className = 'result info';
                }
                
                // 延遲更新統計和記錄
                setTimeout(() => {
                    refreshStats();
                    showRecentLogs();
                }, 1000);
            })
            .catch(error => {
                console.error('AJAX 錯誤:', error);
                
                let resultText = '';
                resultText += '=== AJAX 請求失敗 ===\n';
                resultText += '錯誤: ' + error.message + '\n';
                resultText += '時間: ' + new Date().toISOString() + '\n';
                
                resultDiv.textContent = resultText;
                resultDiv.className = 'result error';
            })
            .finally(() => {
                // 重新啟用按鈕
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 測試 AJAX 登入';
            });
        });

        function refreshStats() {
            fetch('?action=get_stats')
            .then(response => response.json())
            .then(data => {
                const statsContainer = document.getElementById('statsContainer');
                
                if (data.error) {
                    statsContainer.innerHTML = '<div class="result error">錯誤: ' + data.error + '</div>';
                    return;
                }
                
                let html = '<div class="stats-grid">';
                html += '<div class="stat-card"><h4>總嘗試次數</h4><div style="font-size: 24px; font-weight: bold;">' + data.total_attempts + '</div></div>';
                html += '<div class="stat-card"><h4>成功登入</h4><div style="font-size: 24px; font-weight: bold; color: green;">' + data.successful_logins + '</div></div>';
                html += '<div class="stat-card"><h4>失敗嘗試</h4><div style="font-size: 24px; font-weight: bold; color: red;">' + data.failed_attempts + '</div></div>';
                html += '<div class="stat-card"><h4>成功率</h4><div style="font-size: 24px; font-weight: bold; color: blue;">' + data.success_rate + '%</div></div>';
                html += '</div>';
                
                statsContainer.innerHTML = html;
            })
            .catch(error => {
                document.getElementById('statsContainer').innerHTML = '<div class="result error">載入統計失敗: ' + error.message + '</div>';
            });
        }

        function showRecentLogs() {
            fetch('?action=get_logs')
            .then(response => response.json())
            .then(data => {
                const logsContainer = document.getElementById('logsContainer');
                
                if (data.error) {
                    logsContainer.innerHTML = '<div class="result error">錯誤: ' + data.error + '</div>';
                    return;
                }
                
                if (data.logs.length === 0) {
                    logsContainer.innerHTML = '<div class="result info">暫無登入記錄</div>';
                    return;
                }
                
                let html = '<table class="log-table">';
                html += '<tr><th>ID</th><th>郵箱</th><th>動作</th><th>描述</th><th>成功</th><th>IP</th><th>時間</th></tr>';
                
                data.logs.forEach(log => {
                    html += '<tr>';
                    html += '<td>' + log.id + '</td>';
                    html += '<td>' + log.user_email + '</td>';
                    html += '<td>' + log.action + '</td>';
                    html += '<td>' + (log.description || '') + '</td>';
                    html += '<td>' + (log.success ? '✅' : '❌') + '</td>';
                    html += '<td>' + (log.ip_address || '') + '</td>';
                    html += '<td>' + log.created_at + '</td>';
                    html += '</tr>';
                });
                
                html += '</table>';
                logsContainer.innerHTML = html;
            })
            .catch(error => {
                document.getElementById('logsContainer').innerHTML = '<div class="result error">載入記錄失敗: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>

<?php
// 處理 AJAX 請求
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    $dbLogger = new DatabaseLogger();
    
    if ($_GET['action'] === 'get_stats') {
        try {
            $stats = $dbLogger->getLoginStats();
            echo json_encode($stats);
        } catch (Exception $e) {
            echo json_encode(['error' => $e->getMessage()]);
        }
    } elseif ($_GET['action'] === 'get_logs') {
        try {
            $logs = $dbLogger->getRecentLogs(20);
            echo json_encode(['logs' => $logs]);
        } catch (Exception $e) {
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    exit;
}
?>
