<?php
define('SIGNATTEND_INIT', true);
require_once 'config/environment.php';

echo "開始測試...\n";

// 使用配置的 LOG_PATH
echo "LOG_PATH: " . LOG_PATH . "\n";

// 測試寫入
$testFile = LOG_PATH . '/simple_test_' . date('Y-m-d_H-i-s') . '.log';
$content = date('Y-m-d H:i:s') . " - 簡單測試日誌\n";

echo "嘗試寫入文件: " . $testFile . "\n";

if (file_put_contents($testFile, $content)) {
    echo "✓ 成功寫入!\n";
    echo "文件大小: " . filesize($testFile) . " bytes\n";
} else {
    echo "✗ 寫入失敗!\n";
}

// 列出目錄內容
echo "\nLOG_PATH 目錄內容:\n";
if (is_dir(LOG_PATH)) {
    $files = glob(LOG_PATH . '/*');
    if (empty($files)) {
        echo "目錄為空\n";
    } else {
        foreach ($files as $file) {
            echo "- " . basename($file) . "\n";
        }
    }
} else {
    echo "目錄不存在\n";
}
?>
