[2025-06-26 14:24:55] SIMPLE TEST ACCESS: Method=GET, IP=2402:7500:505:fd93:88ba:bb87:589c:aa56, PostData=[]
[2025-06-26 14:24:59] SIMPLE TEST ACCESS: Method=POST, IP=2402:7500:505:fd93:88ba:bb87:589c:aa56, PostData={"csrf_token":"6cbb8c3e77426113869e9a4e140dbe19663d43f3dd63c4f88e24132e39caa918","email":"<EMAIL>","password":"wrong_password_simple"}
[2025-06-26 14:24:59] LOGIN ATTEMPT: Email=<EMAIL>, HasPassword=yes
[2025-06-26 14:25:00] LOGIN RESULT: Success=false, Message=登入失敗, AuthLogBefore=3326, AuthLogAfter=3680
