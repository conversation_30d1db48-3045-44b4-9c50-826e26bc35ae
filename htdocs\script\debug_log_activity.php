<?php
/**
 * 調試 logActivity 方法的詳細執行過程
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

echo "=== logActivity 方法詳細調試 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n";
echo "當前時間: " . date('Y-m-d H:i:s') . "\n\n";

// 檢查認證日誌檔案
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "認證日誌檔案: " . $authLogFile . "\n";

// 記錄檔案狀態
$beforeExists = file_exists($authLogFile);
$beforeSize = $beforeExists ? filesize($authLogFile) : 0;
$beforeLines = 0;

if ($beforeExists) {
    $content = file_get_contents($authLogFile);
    $beforeLines = substr_count($content, "\n");
}

echo "調試前狀態:\n";
echo "- 檔案存在: " . ($beforeExists ? '是' : '否') . "\n";
echo "- 檔案大小: " . $beforeSize . " bytes\n";
echo "- 行數: " . $beforeLines . "\n\n";

// 創建 Auth 實例
$auth = new Auth();

// 使用反射來訪問私有方法
$reflection = new ReflectionClass($auth);
$logActivityMethod = $reflection->getMethod('logActivity');
$logActivityMethod->setAccessible(true);

// 準備測試數據
$testAction = 'debug_test_' . time();
$testDescription = '調試測試 - ' . date('Y-m-d H:i:s');
$testEmail = '<EMAIL>';

echo "=== 執行 logActivity 方法 ===\n";
echo "動作: " . $testAction . "\n";
echo "描述: " . $testDescription . "\n";
echo "郵箱: " . $testEmail . "\n\n";

// 執行方法
try {
    $result = $logActivityMethod->invoke($auth, $testAction, $testDescription, $testEmail);
    echo "方法執行結果: " . ($result ? '成功' : '失敗') . "\n";
} catch (Exception $e) {
    echo "方法執行異常: " . $e->getMessage() . "\n";
}

// 檢查檔案變化
$afterExists = file_exists($authLogFile);
$afterSize = $afterExists ? filesize($authLogFile) : 0;
$afterLines = 0;

if ($afterExists) {
    $content = file_get_contents($authLogFile);
    $afterLines = substr_count($content, "\n");
}

echo "\n調試後狀態:\n";
echo "- 檔案存在: " . ($afterExists ? '是' : '否') . "\n";
echo "- 檔案大小: " . $afterSize . " bytes\n";
echo "- 行數: " . $afterLines . "\n";
echo "- 大小變化: " . ($afterSize - $beforeSize) . " bytes\n";
echo "- 行數變化: " . ($afterLines - $beforeLines) . "\n\n";

// 如果有變化，顯示新增的內容
if ($afterSize > $beforeSize && $afterExists) {
    echo "=== 新增的日誌內容 ===\n";
    $newContent = substr(file_get_contents($authLogFile), $beforeSize);
    echo $newContent . "\n";
    
    // 嘗試解析 JSON
    $lines = explode("\n", trim($newContent));
    foreach ($lines as $line) {
        if (!empty($line)) {
            $data = json_decode($line, true);
            if ($data) {
                echo "解析成功:\n";
                echo "- 時間戳: " . ($data['timestamp'] ?? 'N/A') . "\n";
                echo "- 動作: " . ($data['action'] ?? 'N/A') . "\n";
                echo "- 描述: " . ($data['description'] ?? 'N/A') . "\n";
                echo "- 郵箱: " . ($data['user_email'] ?? 'N/A') . "\n";
            } else {
                echo "JSON 解析失敗: " . $line . "\n";
            }
        }
    }
}

echo "\n=== 手動測試檔案寫入 ===\n";

// 手動測試檔案寫入
$manualTestFile = LOG_PATH . '/manual_test_' . date('Y-m-d') . '.log';
$manualContent = "[" . date('Y-m-d H:i:s') . "] 手動測試寫入\n";

echo "手動測試檔案: " . $manualTestFile . "\n";

$manualResult = file_put_contents($manualTestFile, $manualContent, FILE_APPEND | LOCK_EX);
echo "手動寫入結果: " . ($manualResult !== false ? '成功 (' . $manualResult . ' bytes)' : '失敗') . "\n";

if ($manualResult !== false && file_exists($manualTestFile)) {
    echo "手動測試檔案大小: " . filesize($manualTestFile) . " bytes\n";
    echo "手動測試檔案內容: " . file_get_contents($manualTestFile);
}

echo "\n=== 檢查錯誤日誌 ===\n";
$errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
if (file_exists($errorLogFile)) {
    echo "錯誤日誌檔案存在，大小: " . filesize($errorLogFile) . " bytes\n";
    $errorContent = file_get_contents($errorLogFile);
    $errorLines = explode("\n", trim($errorContent));
    $recentErrors = array_slice($errorLines, -5); // 最後 5 行
    
    if (!empty($recentErrors)) {
        echo "最近的錯誤:\n";
        foreach ($recentErrors as $error) {
            if (!empty($error)) {
                echo "- " . $error . "\n";
            }
        }
    }
} else {
    echo "錯誤日誌檔案不存在\n";
}

echo "\n=== 調試完成 ===\n";
?>
