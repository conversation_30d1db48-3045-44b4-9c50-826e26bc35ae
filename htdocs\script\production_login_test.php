<?php
/**
 * 正式環境登入測試工具
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';
require_once UTILS_PATH . '/PasswordReset.php';

// 處理 AJAX 請求
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $response = [
        'success' => false,
        'message' => '',
        'debug_info' => []
    ];
    
    try {
        if ($_POST['action'] === 'test_environment') {
            // 環境檢測
            $response['debug_info']['environment'] = [
                'server_name' => $_SERVER['SERVER_NAME'] ?? '未知',
                'http_host' => $_SERVER['HTTP_HOST'] ?? '未知',
                'is_production' => strpos($_SERVER['HTTP_HOST'] ?? '', 'attendance.app.tn') !== false,
                'db_host' => defined('DB_HOST') ? DB_HOST : '未定義',
                'db_name' => defined('DB_NAME') ? DB_NAME : '未定義',
                'db_user' => defined('DB_USER') ? DB_USER : '未定義',
                'debug_mode' => defined('DEBUG_MODE') ? DEBUG_MODE : false
            ];
            
            // 測試資料庫連接
            try {
                $pdo = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_TIMEOUT => 10
                    ]
                );
                
                $response['debug_info']['database'] = [
                    'connection' => '成功',
                    'host' => DB_HOST,
                    'database' => DB_NAME
                ];
                
                // 檢查用戶表
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                $userCount = $stmt->fetch()['count'];
                $response['debug_info']['database']['user_count'] = $userCount;
                
                $response['success'] = true;
                $response['message'] = '環境檢測成功';
                
            } catch (Exception $e) {
                $response['debug_info']['database'] = [
                    'connection' => '失敗',
                    'error' => $e->getMessage()
                ];
                $response['message'] = '資料庫連接失敗: ' . $e->getMessage();
            }
        }
        
        elseif ($_POST['action'] === 'reset_password') {
            $email = $_POST['email'] ?? '';
            
            if (empty($email)) {
                $response['message'] = '請輸入電子郵件';
            } else {
                $passwordReset = new PasswordReset();
                $resetResult = $passwordReset->handleForgotPassword($email);
                
                if ($resetResult['success']) {
                    $response['success'] = true;
                    $response['message'] = '密碼重設成功';
                    $response['debug_info']['temp_password'] = $resetResult['temp_password'];
                    $response['debug_info']['email'] = $email;
                } else {
                    $response['message'] = '密碼重設失敗: ' . $resetResult['message'];
                }
            }
        }
        
        elseif ($_POST['action'] === 'test_login') {
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            
            if (empty($email) || empty($password)) {
                $response['message'] = '請輸入電子郵件和密碼';
            } else {
                // 清除 Session
                session_unset();
                
                $auth = new Auth();
                $loginResult = $auth->login($email, $password);
                
                $response['debug_info']['login_result'] = $loginResult;
                
                if ($loginResult['success']) {
                    $response['success'] = true;
                    $response['message'] = '登入成功';
                    
                    // 檢查登入狀態
                    $isLoggedIn = $auth->isLoggedIn();
                    $response['debug_info']['is_logged_in'] = $isLoggedIn;
                    
                    // 檢查 Session
                    $response['debug_info']['session'] = [
                        'user' => isset($_SESSION['user']),
                        'auth_token' => isset($_SESSION['auth_token']),
                        'profile' => isset($_SESSION['profile'])
                    ];
                    
                    if (isset($_SESSION['user'])) {
                        $response['debug_info']['user_info'] = [
                            'id' => substr($_SESSION['user']['id'], 0, 8) . '...',
                            'email' => $_SESSION['user']['email']
                        ];
                    }
                    
                } else {
                    $response['message'] = '登入失敗: ' . ($loginResult['message'] ?? '未知錯誤');
                }
            }
        }
        
        elseif ($_POST['action'] === 'create_test_users') {
            // 創建測試用戶
            try {
                $pdo = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                
                $testUsers = [
                    ['email' => '<EMAIL>', 'password' => 'demo123'],
                    ['email' => '<EMAIL>', 'password' => 'test123'],
                    ['email' => '<EMAIL>', 'password' => 'guest123'],
                    ['email' => '<EMAIL>', 'password' => 'admin123']
                ];
                
                $created = 0;
                $updated = 0;
                
                foreach ($testUsers as $user) {
                    // 檢查用戶是否存在
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                    $stmt->execute([$user['email']]);
                    $existingUser = $stmt->fetch();
                    
                    $passwordHash = password_hash($user['password'], PASSWORD_DEFAULT);
                    
                    if ($existingUser) {
                        // 更新密碼
                        $stmt = $pdo->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE email = ?");
                        $stmt->execute([$passwordHash, $user['email']]);
                        $updated++;
                    } else {
                        // 創建新用戶
                        $userId = bin2hex(random_bytes(18));
                        $stmt = $pdo->prepare("INSERT INTO users (id, email, password_hash, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
                        $stmt->execute([$userId, $user['email'], $passwordHash]);
                        $created++;
                    }
                }
                
                $response['success'] = true;
                $response['message'] = "測試用戶處理完成：創建 $created 個，更新 $updated 個";
                $response['debug_info']['test_users'] = $testUsers;
                
            } catch (Exception $e) {
                $response['message'] = '創建測試用戶失敗: ' . $e->getMessage();
            }
        }
        
    } catch (Exception $e) {
        $response['message'] = '操作失敗: ' . $e->getMessage();
        $response['debug_info']['exception'] = $e->getTraceAsString();
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正式環境登入測試工具</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px; }
        .panel { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .result { margin-top: 15px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .debug-info { background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin-top: 10px; border-radius: 4px; }
        .debug-info pre { margin: 0; white-space: pre-wrap; font-size: 12px; }
        h2 { color: #495057; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>正式環境登入測試工具</h1>
    
    <div class="panel">
        <h2>1. 環境檢測</h2>
        <button class="btn" onclick="testEnvironment()">檢測環境和資料庫</button>
        <div id="envResult"></div>
    </div>
    
    <div class="panel">
        <h2>2. 創建/更新測試用戶</h2>
        <p>這將創建或更新以下測試帳號的密碼：</p>
        <ul>
            <li><EMAIL> / demo123</li>
            <li><EMAIL> / test123</li>
            <li><EMAIL> / guest123</li>
            <li><EMAIL> / admin123</li>
        </ul>
        <button class="btn btn-warning" onclick="createTestUsers()">創建/更新測試用戶</button>
        <div id="createResult"></div>
    </div>
    
    <div class="panel">
        <h2>3. 忘記密碼測試</h2>
        <div class="form-group">
            <label for="resetEmail">電子郵件:</label>
            <input type="email" id="resetEmail" value="<EMAIL>">
        </div>
        <button class="btn btn-success" onclick="resetPassword()">重設密碼</button>
        <div id="resetResult"></div>
    </div>
    
    <div class="panel">
        <h2>4. 登入測試</h2>
        <div class="form-group">
            <label for="loginEmail">電子郵件:</label>
            <input type="email" id="loginEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="loginPassword">密碼:</label>
            <input type="password" id="loginPassword" value="test123">
        </div>
        <button class="btn" onclick="testLogin()">測試登入</button>
        <div id="loginResult"></div>
    </div>
    
    <script>
        function makeRequest(action, data = {}) {
            const formData = new FormData();
            formData.append('action', action);
            
            for (const [key, value] of Object.entries(data)) {
                formData.append(key, value);
            }
            
            return fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.json());
        }
        
        function showResult(elementId, data) {
            const resultClass = data.success ? 'success' : 'error';
            let html = `<div class="result ${resultClass}">
                <strong>${data.success ? '成功' : '失敗'}:</strong> ${data.message}
            </div>`;
            
            if (data.debug_info && Object.keys(data.debug_info).length > 0) {
                html += '<div class="debug-info"><strong>調試信息:</strong><pre>' + JSON.stringify(data.debug_info, null, 2) + '</pre></div>';
            }
            
            document.getElementById(elementId).innerHTML = html;
        }
        
        function testEnvironment() {
            document.getElementById('envResult').innerHTML = '<div class="debug-info">檢測中...</div>';
            makeRequest('test_environment').then(data => showResult('envResult', data));
        }
        
        function createTestUsers() {
            document.getElementById('createResult').innerHTML = '<div class="debug-info">處理中...</div>';
            makeRequest('create_test_users').then(data => showResult('createResult', data));
        }
        
        function resetPassword() {
            const email = document.getElementById('resetEmail').value;
            document.getElementById('resetResult').innerHTML = '<div class="debug-info">重設中...</div>';
            makeRequest('reset_password', { email }).then(data => showResult('resetResult', data));
        }
        
        function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            document.getElementById('loginResult').innerHTML = '<div class="debug-info">登入中...</div>';
            makeRequest('test_login', { email, password }).then(data => showResult('loginResult', data));
        }
        
        // 頁面載入時自動檢測環境
        window.onload = function() {
            testEnvironment();
        };
    </script>
</body>
</html>
