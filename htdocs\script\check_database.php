<?php
/**
 * 資料表結構檢查工具
 */

// 定義常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料表結構檢查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 資料表結構檢查</h1>
        
        <?php
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<div class='section'>";
            echo "<h2>1. 資料庫基本資訊</h2>";
            echo "<p><strong>資料庫名稱:</strong> " . DB_NAME . "</p>";
            
            // 檢查所有資料表
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<p><strong>現有資料表:</strong> " . implode(', ', $tables) . "</p>";
            echo "</div>";
            
            // 檢查每個必要的資料表
            $requiredTables = ['users', 'profiles', 'meetings', 'attendees'];
            
            foreach ($requiredTables as $tableName) {
                echo "<div class='section'>";
                echo "<h2>2. 檢查資料表: $tableName</h2>";
                
                if (in_array($tableName, $tables)) {
                    echo "<p class='success'>✅ 資料表存在</p>";
                    
                    // 檢查資料表結構
                    $stmt = $pdo->query("DESCRIBE $tableName");
                    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h3>欄位結構:</h3>";
                    echo "<table>";
                    echo "<tr><th>欄位名稱</th><th>資料類型</th><th>允許 NULL</th><th>鍵</th><th>預設值</th></tr>";
                    foreach ($columns as $column) {
                        echo "<tr>";
                        echo "<td>" . $column['Field'] . "</td>";
                        echo "<td>" . $column['Type'] . "</td>";
                        echo "<td>" . $column['Null'] . "</td>";
                        echo "<td>" . $column['Key'] . "</td>";
                        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    // 檢查資料筆數
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM $tableName");
                    $count = $stmt->fetch()['count'];
                    echo "<p><strong>資料筆數:</strong> $count</p>";
                    
                } else {
                    echo "<p class='error'>❌ 資料表不存在</p>";
                }
                echo "</div>";
            }
            
            // 特別檢查 meetings 表的必要欄位
            if (in_array('meetings', $tables)) {
                echo "<div class='section'>";
                echo "<h2>3. meetings 表欄位檢查</h2>";
                
                $stmt = $pdo->query("DESCRIBE meetings");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $requiredColumns = ['id', 'name', 'description', 'start_time', 'end_time', 'location', 'created_at'];
                
                foreach ($requiredColumns as $col) {
                    if (in_array($col, $columns)) {
                        echo "<p class='success'>✅ $col 欄位存在</p>";
                    } else {
                        echo "<p class='error'>❌ $col 欄位缺失</p>";
                    }
                }
                echo "</div>";
            }
            
            // 提供修復 SQL
            echo "<div class='section'>";
            echo "<h2>4. 修復 SQL 指令</h2>";
            echo "<p>如果發現缺失的欄位，請執行以下 SQL 指令：</p>";
            echo "<pre>";
            echo "-- 修復 meetings 表結構\n";
            echo "ALTER TABLE meetings \n";
            echo "ADD COLUMN IF NOT EXISTS start_time DATETIME NOT NULL AFTER description,\n";
            echo "ADD COLUMN IF NOT EXISTS end_time DATETIME NOT NULL AFTER start_time,\n";
            echo "ADD COLUMN IF NOT EXISTS location VARCHAR(200) NULL AFTER end_time,\n";
            echo "ADD COLUMN IF NOT EXISTS qr_code VARCHAR(100) UNIQUE NULL AFTER location,\n";
            echo "ADD COLUMN IF NOT EXISTS check_in_code VARCHAR(10) UNIQUE NULL AFTER qr_code,\n";
            echo "ADD COLUMN IF NOT EXISTS created_by VARCHAR(36) NULL AFTER check_in_code,\n";
            echo "ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE AFTER created_by,\n";
            echo "ADD COLUMN IF NOT EXISTS max_attendees INT NULL AFTER is_active;\n\n";
            
            echo "-- 如果資料表完全不存在，重新執行初始化腳本\n";
            echo "-- SOURCE htdocs/config/database_init.sql;\n";
            echo "</pre>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='section'>";
            echo "<p class='error'>❌ 資料庫連接失敗: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="debug_index.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">返回診斷</a>
            <a href="test_basic.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">基本測試</a>
        </div>
    </div>
</body>
</html>
