<?php
define('SIGNATTEND_INIT', true);

echo "測試開始...\n";

// 直接測試路徑
$currentFile = __FILE__;
$htdocsDir = dirname($currentFile);
$rootDir = dirname($htdocsDir);

echo "當前文件: " . $currentFile . "\n";
echo "htdocs 目錄: " . $htdocsDir . "\n";
echo "根目錄: " . $rootDir . "\n";
echo "預期 LOG_PATH: " . $rootDir . '/logs' . "\n";

// 載入配置
require_once 'config/config.php';

echo "\n配置載入後:\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 測試寫入
$testFile = LOG_PATH . '/simple_test_' . date('Y-m-d') . '.log';
$testMessage = "[" . date('Y-m-d H:i:s') . "] 簡單測試\n";

if (file_put_contents($testFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "✓ 成功寫入: " . $testFile . "\n";
} else {
    echo "✗ 寫入失敗: " . $testFile . "\n";
}
?>
