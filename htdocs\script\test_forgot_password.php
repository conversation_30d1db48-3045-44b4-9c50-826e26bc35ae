<?php
/**
 * 測試忘記密碼功能
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入密碼重設類別
require_once UTILS_PATH . '/PasswordReset.php';

echo "=== 測試忘記密碼功能 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $passwordReset = new PasswordReset();
    
    // 測試用的演示帳號
    $testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>' // 不存在的帳號
    ];
    
    foreach ($testEmails as $email) {
        echo "=== 測試郵箱: $email ===\n";
        
        $result = $passwordReset->handleForgotPassword($email);
        
        if ($result['success']) {
            echo "✅ 密碼重設成功\n";
            echo "訊息: " . $result['message'] . "\n";
            echo "臨時密碼: " . $result['temp_password'] . "\n";
            echo "備註: " . $result['note'] . "\n";
        } else {
            echo "❌ 密碼重設失敗\n";
            echo "錯誤: " . $result['message'] . "\n";
        }
        
        echo "\n";
    }
    
    // 檢查密碼重設統計
    echo "=== 密碼重設統計 ===\n";
    $stats = $passwordReset->getResetStats();
    
    if ($stats) {
        echo "總重設次數: " . $stats['total_resets'] . "\n";
        echo "不重複用戶: " . ($stats['unique_users'] ?? 'N/A') . "\n";
        echo "最後重設時間: " . ($stats['last_reset'] ?? 'N/A') . "\n";
    } else {
        echo "無法獲取統計信息\n";
    }
    
    // 檢查最近的重設記錄
    echo "\n=== 最近的重設記錄 ===\n";
    $recentResets = $passwordReset->getRecentResets(5);
    
    if (!empty($recentResets)) {
        printf("%-30s %-15s %-20s\n", "郵箱", "IP 地址", "重設時間");
        echo str_repeat("-", 70) . "\n";
        
        foreach ($recentResets as $reset) {
            printf("%-30s %-15s %-20s\n",
                substr($reset['user_email'], 0, 29),
                $reset['ip_address'],
                $reset['created_at']
            );
        }
    } else {
        echo "暫無重設記錄\n";
    }
    
    // 測試特定郵箱的統計
    echo "\n=== 特定郵箱統計 ===\n";
    $testEmail = '<EMAIL>';
    $emailStats = $passwordReset->getResetStats($testEmail);
    
    if ($emailStats) {
        echo "郵箱: $testEmail\n";
        echo "重設次數: " . $emailStats['total_resets'] . "\n";
        echo "最後重設: " . ($emailStats['last_reset'] ?? 'N/A') . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 測試過程中發生錯誤: " . $e->getMessage() . "\n";
    echo "錯誤檔案: " . $e->getFile() . "\n";
    echo "錯誤行號: " . $e->getLine() . "\n";
}

echo "\n=== 測試完成 ===\n";
echo "如果看到 '✅ 密碼重設成功' 和臨時密碼，\n";
echo "表示忘記密碼功能正常工作。\n";
echo "\n現在可以測試忘記密碼頁面:\n";
echo "1. 訪問: pages/forgot-password.php\n";
echo "2. 輸入演示帳號郵箱\n";
echo "3. 確認不再出現 JSON 錯誤\n";
?>
