<?php
/**
 * SignAttend PHP Frontend 主入口點
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session (檢查是否已啟動)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once __DIR__ . '/includes/functions.php';

// 獲取最近三個會議資料
$recentMeetings = [];
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 先檢查 meetings 表是否存在以及欄位結構
    $stmt = $pdo->query("SHOW TABLES LIKE 'meetings'");
    if ($stmt->rowCount() > 0) {
        // 檢查必要欄位是否存在
        $stmt = $pdo->query("DESCRIBE meetings");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // 根據現有欄位構建查詢
        $selectFields = ['m.id', 'm.name'];
        if (in_array('description', $columns)) $selectFields[] = 'm.description';
        if (in_array('start_time', $columns)) $selectFields[] = 'm.start_time';
        if (in_array('end_time', $columns)) $selectFields[] = 'm.end_time';
        if (in_array('location', $columns)) $selectFields[] = 'm.location';
        if (in_array('created_at', $columns)) $selectFields[] = 'm.created_at';

        $sql = "SELECT " . implode(', ', $selectFields) . ",
                COUNT(a.id) as total_attendees,
                COUNT(CASE WHEN a.checked_in = 1 THEN 1 END) as checked_in_count
                FROM meetings m
                LEFT JOIN attendees a ON m.id = a.meeting_id
                GROUP BY " . implode(', ', $selectFields) . "
                ORDER BY " . (in_array('created_at', $columns) ? 'm.created_at' : 'm.id') . " DESC
                LIMIT 3";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $recentMeetings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    // 如果資料庫連接失敗，使用預設資料
    $recentMeetings = [
        [
            'id' => 1,
            'name' => '數位轉型研討會',
            'description' => '探討企業數位轉型的策略與實務，邀請業界專家分享經驗。',
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => date('Y-m-d H:i:s', strtotime('+2 hours')),
            'location' => '台北國際會議中心',
            'total_attendees' => 60,
            'checked_in_count' => 45
        ],
        [
            'id' => 2,
            'name' => 'AI 人工智慧論壇',
            'description' => '深入了解人工智慧的最新發展趨勢和應用案例。',
            'start_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'end_time' => date('Y-m-d H:i:s', strtotime('+1 day +3 hours')),
            'location' => '台北世貿中心',
            'total_attendees' => 80,
            'checked_in_count' => 0
        ],
        [
            'id' => 3,
            'name' => '雲端技術分享會',
            'description' => '分享雲端服務的架構設計和最佳實務經驗。',
            'start_time' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'end_time' => date('Y-m-d H:i:s', strtotime('-1 day +2 hours')),
            'location' => '台北科技大學',
            'total_attendees' => 40,
            'checked_in_count' => 35
        ]
    ];
}

// 輔助函數：判斷會議狀態
function getMeetingStatus($startTime, $endTime) {
    $now = time();
    $start = strtotime($startTime);
    $end = strtotime($endTime);

    if ($now < $start) {
        return ['status' => 'upcoming', 'label' => '即將開始', 'class' => 'bg-warning'];
    } elseif ($now >= $start && $now <= $end) {
        return ['status' => 'ongoing', 'label' => '進行中', 'class' => 'bg-success'];
    } else {
        return ['status' => 'ended', 'label' => '已結束', 'class' => 'bg-secondary'];
    }
}

// 載入核心類別並初始化
$apiClient = null;
$auth = null;

try {
    require_once __DIR__ . '/utils/ApiClient.php';
    require_once __DIR__ . '/utils/Auth.php';

    $apiClient = new ApiClient();
    $auth = new Auth();
} catch (Exception $e) {
    // 如果載入失敗，創建簡單的 mock 對象
    error_log('Auth/ApiClient 載入失敗: ' . $e->getMessage());

    $auth = new class {
        public function isLoggedIn() { return false; }
        public function getCurrentUser() { return null; }
        public function getCurrentProfile() { return null; }
    };

    $apiClient = new class {
        public function checkConnection() { return false; }
        public function getApiStatus() { return ['success' => false]; }
    };
}

// 檢查記住我功能
try {
    if ($auth && method_exists($auth, 'isLoggedIn') && !$auth->isLoggedIn()) {
        if (method_exists($auth, 'checkRememberMe')) {
            $auth->checkRememberMe();
        }
    }
} catch (Exception $e) {
    // 忽略記住我功能的錯誤
    error_log('checkRememberMe 錯誤: ' . $e->getMessage());
}

// 設定頁面變數
$pageTitle = APP_NAME . ' - 智慧簽到系統';
$pageDescription = APP_DESCRIPTION;
$currentPage = 'home';

// 檢查 API 連線狀態
try {
    $apiStatus = ($apiClient && method_exists($apiClient, 'checkConnection')) ? $apiClient->checkConnection() : false;
} catch (Exception $e) {
    $apiStatus = false;
    error_log('API 連線檢查錯誤: ' . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= h($pageDescription) ?>">
    <meta name="author" content="SignAttend Team">
    <title><?= h($pageTitle) ?></title>

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= h(APP_NAME) ?> - 智慧簽到系統">
    <meta property="og:description" content="<?= h($pageDescription) ?>">
    <meta property="og:type" content="website">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?= asset_url('css/custom.css') ?>" rel="stylesheet">

    <!-- 頁面特定樣式 -->
    <style>
        body { margin: 0 !important; padding: 0 !important; }
        .hero-section { margin-top: 0 !important; }

        /* 強制 Hero 按鈕 hover 效果 */
        .hero-button:hover,
        button.hero-button:hover {
            background-color: #2563eb !important;
            border-color: #2563eb !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        /* 強制導航按鈕 hover 效果 */
        .nav-button:hover,
        button.nav-button:hover,
        a.nav-button:hover {
            background-color: #2563eb !important;
            border-color: #2563eb !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        /* 確保所有按鈕都有過渡效果 */
        .hero-button, .nav-button {
            transition: all 0.3s ease !important;
        }

        /* 限制 hover 效果只應用於特定按鈕 */
        .hero-button:hover,
        .nav-button:hover,
        .btn-primary:hover,
        .btn-outline-primary:hover {
            background-color: #2563eb !important;
            border-color: #2563eb !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
        }

        /* 手風琴樣式修復 */
        .accordion-button {
            background-color: #ffffff !important;
            color: #333333 !important;
            border: none !important;
            box-shadow: none !important;
            font-weight: 500;
        }

        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa !important;
            color: #2563eb !important;
            box-shadow: none !important;
        }

        .accordion-button:hover {
            background-color: #f8f9fa !important;
            color: #2563eb !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25) !important;
            border-color: transparent !important;
        }

        .accordion-item {
            border: 1px solid #e9ecef !important;
            border-radius: 0.5rem !important;
            overflow: hidden;
        }

        .accordion-body {
            background-color: #ffffff;
            color: #6c757d;
            line-height: 1.6;
        }
    </style>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= asset_url('images/favicon.ico') ?>">
</head>
<body>
    <!-- Hero Section with Navigation -->
    <section class="hero-section">
        <!-- 導航列 -->
        <nav class="bg-white/10 backdrop-blur-sm border-b border-white/20">
            <div class="hero-container mx-auto px-4 pt-20 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-white">
                            <rect width="5" height="5" x="3" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="16" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="3" y="16" rx="1"></rect>
                            <path d="M21 16h-3a2 2 0 0 0-2 2v3"></path>
                            <path d="M21 21v.01"></path>
                            <path d="M12 7v3a2 2 0 0 1-2 2H7"></path>
                            <path d="M3 12h.01"></path>
                            <path d="M12 3h.01"></path>
                            <path d="M12 16v.01"></path>
                            <path d="M16 12h1"></path>
                            <path d="M21 12v.01"></path>
                            <path d="M12 21v-1"></path>
                        </svg>
                        <span class="text-xl font-bold text-white">簽易通</span>
                    </div>
                    <div class="flex gap-4">
                        <button class="nav-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-4 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600" onclick="scrollToFeatures()">
                            功能介紹
                        </button>
                        <button class="nav-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-4 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600" onclick="showContactModal()">
                            聯絡我們
                        </button>
                        <button class="nav-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-4 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600" onclick="showSupportModal()">
                            服務中心
                        </button>
                        <?php if ($auth->isLoggedIn()): ?>
                            <a href="<?= page_url('pages/dashboard.php') ?>" class="nav-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-4 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600 no-underline">
                                儀表板
                            </a>
                        <?php else: ?>
                            <a href="<?= page_url('pages/login.php') ?>" class="nav-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-4 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600 no-underline">
                                管理者登入
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Content -->
        <div class="hero-container mx-auto px-4 pt-8 pb-3">
            <!-- Flash 訊息 -->
            <?php display_flash_messages(); ?>

            <div class="hero-grid">
                <div class="text-white">
                    <h1 class="text-4xl font-bold mb-3">
                        快速、便捷的<br>
                        研討會報到體驗
                    </h1>
                    <p class="text-lg mb-4 text-blue-100">
                        自動產生會議QR Code和報到流程，參與者只需掃描QR碼或輸入報到碼，讓會議管理更輕鬆。
                    </p>
                    <div class="flex gap-4">
                        <button class="hero-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-11 rounded-md px-8 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600" onclick="openScannerModal()">
                            快速體驗
                        </button>
                        <button class="hero-button inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-11 rounded-md px-8 border border-white text-white bg-transparent hover:bg-blue-600 hover:border-blue-600" onclick="scrollToFeatures()">
                            了解更多
                        </button>
                    </div>
                </div>

                <div class="hero-mockup">
                    <div class="hero-card bg-white rounded-2xl shadow-2xl border-2 border-gray-100">
                        <div class="bg-blue-100 rounded-lg flex-1 flex flex-col justify-center">
                        <div class="w-full h-4 bg-blue-300 rounded mb-4"></div>
                        <div class="flex items-center gap-4">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-white">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="h-4 bg-blue-300 rounded mb-2"></div>
                                <div class="h-3 bg-blue-200 rounded w-2/3 mb-2"></div>
                                <div class="flex gap-2">
                                    <div class="h-3 bg-blue-400 rounded w-16"></div>
                                    <div class="h-3 bg-blue-300 rounded w-12"></div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要內容區域 -->
    <main>
        <!-- 研討會報到區塊 -->
        <section class="py-5 bg-white">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold text-gray-900 mb-3">會議報到</h2>
                    <p class="text-gray-600 fs-5">
                        請輸入您的6位報到代碼完成報到，或掃描您的QR Code快速完成報到程序。
                    </p>
                </div>

                <div class="row g-4">
                    <!-- 手動報到表單 -->
                    <div class="col-lg-6">
                        <div class="card h-100 border-0 shadow">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">報到碼報到</h5>
                            </div>
                            <div class="card-body">
                                <form id="checkinForm">
                                    <div class="mb-4">
                                        <label class="form-label fw-bold">請輸入您的6位報到代碼</label>
                                        <input type="text" class="form-control form-control-lg text-center"
                                               name="checkin_code"
                                               placeholder="例如：ABC123"
                                               maxlength="6"
                                               style="letter-spacing: 0.2em; font-size: 1.5rem;"
                                               required>
                                        <div class="form-text text-center mt-2">
                                            報到代碼可在您的邀請函或QR碼下方找到
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 btn-lg">
                                        <i class="bi bi-check-circle me-2"></i>立即報到
                                    </button>
                                </form>

                                <!-- 說明區域 -->
                                <div class="mt-4 p-3 bg-light rounded">
                                    <h6 class="fw-bold mb-2">
                                        <i class="bi bi-info-circle text-primary me-1"></i>
                                        如何找到報到代碼？
                                    </h6>
                                    <ul class="list-unstyled mb-0 small">
                                        <li class="mb-1">• 查看您收到的會議邀請函</li>
                                        <li class="mb-1">• 掃描QR碼下方的6位代碼</li>
                                        <li>• 聯繫會議主辦方獲取代碼</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code 快速報到 -->
                    <div class="col-lg-6">
                        <div class="card h-100 border-0 shadow">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">QR Code 快速報到</h5>
                            </div>
                            <div class="card-body text-center">
                                <div class="bg-light rounded-3 p-5 mb-4">
                                    <div class="bg-gray-300 rounded-3 mx-auto d-flex align-items-center justify-content-center" style="width: 8rem; height: 8rem;">
                                        <i class="bi bi-qr-code display-4 text-gray-500"></i>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-4">
                                    使用相機掃描或選取包含 QR 碼的圖片進行快速報到
                                </p>
                                <button class="btn btn-success w-100" onclick="openScannerModal()">
                                    <i class="bi bi-camera me-2"></i>開始掃描 QR 碼
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
                
        <!-- 功能特色 -->
        <section id="features" class="py-5 bg-light">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold text-gray-900">功能特色</h2>
                    <p class="text-gray-600 fs-5">簡單、快速、可靠的簽到解決方案</p>
                </div>

                <div class="row g-4">
                    <div class="col-md-6 col-lg-3">
                        <div class="card h-100 border-0 shadow-sm text-center">
                            <div class="card-body p-4">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                    <i class="bi bi-qr-code fs-1 text-primary"></i>
                                </div>
                                <h5 class="fw-semibold mb-2">QR Code 簽到</h5>
                                <p class="text-gray-600 small">掃描即可完成簽到，無需排隊等候，支援多種掃描方式</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3">
                        <div class="card h-100 border-0 shadow-sm text-center">
                            <div class="card-body p-4">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                    <i class="bi bi-lightning-charge fs-1 text-success"></i>
                                </div>
                                <h5 class="fw-semibold mb-2">即時統計</h5>
                                <p class="text-gray-600 small">即時查看簽到狀況和參與者統計，動態更新數據</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3">
                        <div class="card h-100 border-0 shadow-sm text-center">
                            <div class="card-body p-4">
                                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                    <i class="bi bi-shield-check fs-1 text-info"></i>
                                </div>
                                <h5 class="fw-semibold mb-2">安全可靠</h5>
                                <p class="text-gray-600 small">企業級安全保護，資料加密傳輸，保護隱私</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-lg-3">
                        <div class="card h-100 border-0 shadow-sm text-center">
                            <div class="card-body p-4">
                                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                    <i class="bi bi-graph-up fs-1 text-warning"></i>
                                </div>
                                <h5 class="fw-semibold mb-2">數據分析</h5>
                                <p class="text-gray-600 small">詳細的參與者分析和報表匯出，支援多種格式</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
                
        <!-- 當前活動 -->
        <section class="py-5 bg-white">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold text-gray-900">當前活動</h2>
                    <p class="text-gray-600 fs-5">正在進行的研討會和活動</p>
                </div>

                <div class="row g-4">
                    <?php if (empty($recentMeetings)): ?>
                        <!-- 沒有會議時的顯示 -->
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">目前沒有會議</h5>
                                <p class="text-muted">請聯繫管理員創建會議</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recentMeetings as $meeting): ?>
                            <?php
                                $status = getMeetingStatus($meeting['start_time'], $meeting['end_time']);
                                $startDate = date('Y-m-d', strtotime($meeting['start_time']));
                                $startTime = date('H:i', strtotime($meeting['start_time']));
                            ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <span class="badge <?= $status['class'] ?>"><?= $status['label'] ?></span>
                                            <small class="text-muted"><?= $startDate ?></small>
                                        </div>
                                        <h5 class="card-title"><?= htmlspecialchars($meeting['name']) ?></h5>
                                        <p class="card-text text-muted small">
                                            <?= htmlspecialchars($meeting['description'] ?: '暫無描述') ?>
                                        </p>
                                        <?php if (!empty($meeting['location'])): ?>
                                            <p class="card-text text-muted small mb-2">
                                                <i class="bi bi-geo-alt me-1"></i><?= htmlspecialchars($meeting['location']) ?>
                                            </p>
                                        <?php endif; ?>
                                        <p class="card-text text-muted small mb-3">
                                            <i class="bi bi-clock me-1"></i><?= $startTime ?>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-people me-1"></i>
                                                <?php if ($status['status'] === 'upcoming'): ?>
                                                    預計參與: <?= $meeting['total_attendees'] ?>人
                                                <?php elseif ($status['status'] === 'ongoing'): ?>
                                                    已報到: <?= $meeting['checked_in_count'] ?>/<?= $meeting['total_attendees'] ?>
                                                <?php else: ?>
                                                    完成報到: <?= $meeting['checked_in_count'] ?>/<?= $meeting['total_attendees'] ?>
                                                <?php endif; ?>
                                            </small>
                                            <?php if ($status['status'] === 'ongoing'): ?>
                                                <button class="btn btn-outline-primary btn-sm" onclick="openScannerModal()">
                                                    立即報到
                                                </button>
                                            <?php elseif ($status['status'] === 'upcoming'): ?>
                                                <button class="btn btn-outline-secondary btn-sm" disabled>
                                                    尚未開放
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-outline-secondary btn-sm" disabled>
                                                    已結束
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- 使用統計 -->
                <div class="row text-center mt-5 pt-5 border-top">
                    <div class="col-md-3 col-6 mb-4">
                        <div class="h2 fw-bold text-primary mb-1">1,000+</div>
                        <div class="text-gray-600">活躍用戶</div>
                    </div>
                    <div class="col-md-3 col-6 mb-4">
                        <div class="h2 fw-bold text-success mb-1">5,000+</div>
                        <div class="text-gray-600">成功簽到</div>
                    </div>
                    <div class="col-md-3 col-6 mb-4">
                        <div class="h2 fw-bold text-info mb-1">500+</div>
                        <div class="text-gray-600">舉辦會議</div>
                    </div>
                    <div class="col-md-3 col-6 mb-4">
                        <div class="h2 fw-bold text-warning mb-1">99.9%</div>
                        <div class="text-gray-600">系統穩定性</div>
                    </div>
                </div>
            </div>
        </section>
        <!-- 常見問題 -->
        <section class="py-5 bg-light">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold text-gray-900">常見問題</h2>
                    <p class="text-gray-600 fs-5">解答您在使用過程中可能遇到的問題</p>
                </div>

                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item border-0 shadow-sm mb-3">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                        如何使用 QR Code 簽到？
                                    </button>
                                </h2>
                                <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        使用手機相機或任何 QR Code 掃描 APP 掃描會議提供的 QR Code，
                                        系統會自動導向簽到頁面，填寫基本資料後即可完成簽到。
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item border-0 shadow-sm mb-3">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                        如何建立新的會議？
                                    </button>
                                </h2>
                                <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        登入後進入儀表板，點擊「建立會議」按鈕，
                                        填寫會議基本資訊如名稱、時間、地點等，系統會自動產生簽到 QR Code。
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item border-0 shadow-sm mb-3">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                        可以匯出參與者名單嗎？
                                    </button>
                                </h2>
                                <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        可以的！在會議管理頁面中，您可以即時查看簽到狀況，
                                        並匯出 Excel 或 PDF 格式的參與者名單和簽到報表。
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item border-0 shadow-sm mb-3">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                        系統支援多少人同時使用？
                                    </button>
                                </h2>
                                <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        我們的系統採用雲端架構，可以支援大型會議的同時簽到需求，
                                        單場會議可支援數千人同時進行簽到操作。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-5 bg-primary text-white">
            <div class="container text-center">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <h2 class="fw-bold mb-3">準備好開始了嗎？</h2>
                        <p class="fs-5 mb-4 opacity-90">
                            立即體驗 SignAttend，享受最簡單高效的簽到管理系統
                        </p>
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                            <button class="btn btn-light btn-lg px-4" onclick="openScannerModal()">
                                <i class="bi bi-rocket-takeoff me-2"></i>立即體驗
                            </button>
                            <?php if (!$auth->isLoggedIn()): ?>
                                <a class="btn btn-outline-light btn-lg px-4" href="<?= page_url('pages/register.php') ?>">
                                    <i class="bi bi-person-plus me-2"></i>免費註冊
                                </a>
                            <?php else: ?>
                                <a class="btn btn-outline-light btn-lg px-4" href="<?= page_url('pages/dashboard.php') ?>">
                                    <i class="bi bi-speedometer2 me-2"></i>進入儀表板
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 頁尾 -->
    <footer class="bg-gray-900 text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-qr-code-scan me-2 text-primary"></i>
                        <span class="fw-semibold"><?= h(APP_NAME) ?></span>
                        <span class="ms-2 text-gray-400">v<?= h(APP_VERSION) ?></span>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <span class="text-gray-400 me-3">
                            &copy; <?= date('Y') ?> SignAttend Team
                        </span>
                        <?php if ($apiStatus): ?>
                            <span class="badge bg-success">系統正常</span>
                        <?php else: ?>
                            <span class="badge bg-warning">檢查中</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- QR Scanner Modal -->
    <div class="modal fade" id="scannerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">QR Code 掃描</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="bg-light rounded-3 p-5 mb-4">
                        <i class="bi bi-camera display-1 text-primary mb-3"></i>
                        <h5>開啟相機掃描 QR Code</h5>
                        <p class="text-muted">請將 QR Code 對準相機鏡頭</p>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="startCamera()">
                            <i class="bi bi-camera me-2"></i>開啟相機
                        </button>
                        <button class="btn btn-outline-secondary" onclick="uploadImage()">
                            <i class="bi bi-upload me-2"></i>上傳圖片
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?= asset_url('js/app.js') ?>"></script>

    <script>
        // 開啟掃描器模態框
        function openScannerModal() {
            const modal = new bootstrap.Modal(document.getElementById('scannerModal'));
            modal.show();
        }

        // 滾動到功能區塊
        function scrollToFeatures() {
            document.getElementById('features').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // 開啟相機（示例功能）
        function startCamera() {
            alert('相機功能將在完整版本中實現');
        }

        // 上傳圖片（示例功能）
        function uploadImage() {
            alert('圖片上傳功能將在完整版本中實現');
        }

        // 顯示聯絡我們模態框
        function showContactModal() {
            alert('聯絡我們：\n電話：02-1234-5678\nEmail：<EMAIL>');
        }

        // 顯示服務中心模態框
        function showSupportModal() {
            alert('服務中心：\n營業時間：週一至週五 9:00-18:00\n客服專線：02-8765-4321');
        }

        // 報到碼輸入格式化
        document.querySelector('input[name="checkin_code"]').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            e.target.value = value;
        });

        // 簽到表單提交
        document.getElementById('checkinForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 獲取報到碼
            const checkinCode = this.querySelector('input[name="checkin_code"]').value.trim();

            // 驗證報到碼
            if (!checkinCode) {
                alert('請輸入報到代碼');
                return;
            }

            if (checkinCode.length !== 6) {
                alert('報到代碼必須是6位數');
                return;
            }

            // 顯示處理中狀態
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>處理中...';

            // 模擬API請求
            setTimeout(() => {
                // 這裡將來會連接到實際的API
                // 目前模擬成功報到
                const attendeeName = '參與者'; // 實際會從API獲取

                // 顯示成功訊息
                alert(`報到成功！\n\n歡迎 ${attendeeName} 參加會議\n報到代碼：${checkinCode}\n報到時間：${new Date().toLocaleString()}`);

                // 重置表單
                this.reset();

                // 恢復按鈕狀態
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                // 可選：跳轉到成功頁面或顯示更詳細的資訊
                // window.location.href = 'checkin-success.php';

            }, 1500); // 模擬網路延遲
        });

        // 手動添加 hover 效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.hero-button, .nav-button, button');

            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#2563eb';
                    this.style.borderColor = '#2563eb';
                    this.style.color = '#ffffff';
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 6px 16px rgba(37, 99, 235, 0.4)';
                    this.style.transition = 'all 0.3s ease';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.borderColor = '#ffffff';
                    this.style.color = '#ffffff';
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
