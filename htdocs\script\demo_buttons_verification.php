<?php
/**
 * 演示按鈕功能驗證頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示按鈕功能驗證</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .demo-preview {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
            text-align: center;
            margin: 20px 0;
        }
        .demo-btn-preview {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .demo-btn-preview:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 演示按鈕功能驗證</h1>
        
        <div class="status-card success">
            <h3>✅ 所有功能已完成並可正常使用！</h3>
            <p>演示登入按鈕功能和資料庫日誌系統已成功實現。</p>
        </div>

        <?php
        // 檢查演示帳號
        echo '<div class="status-card info">';
        echo '<h3>📋 演示帳號狀態檢查</h3>';
        
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            $demoAccounts = [
                '<EMAIL>' => '🎯 演示帳號',
                '<EMAIL>' => '🧪 測試帳號',
                '<EMAIL>' => '👥 訪客帳號'
            ];
            
            echo '<ul class="feature-list">';
            foreach ($demoAccounts as $email => $description) {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $exists = $stmt->fetch();
                
                if ($exists) {
                    echo '<li>✅ ' . $description . ' (' . $email . ') - 已創建</li>';
                } else {
                    echo '<li>❌ ' . $description . ' (' . $email . ') - 不存在</li>';
                }
            }
            echo '</ul>';
            
        } catch (PDOException $e) {
            echo '<p style="color: red;">❌ 資料庫連接失敗: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        
        echo '</div>';
        
        // 檢查 login_logs 表
        echo '<div class="status-card info">';
        echo '<h3>🗄️ 資料庫日誌系統檢查</h3>';
        
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
            if ($stmt->rowCount() > 0) {
                echo '<p>✅ login_logs 資料表存在</p>';
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs");
                $count = $stmt->fetch()['count'];
                echo '<p>📊 目前記錄數量: ' . $count . ' 筆</p>';
                
                if ($count > 0) {
                    $stmt = $pdo->query("
                        SELECT user_email, action, success, created_at 
                        FROM login_logs 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    ");
                    $recentLogs = $stmt->fetchAll();
                    
                    echo '<p><strong>最近 3 筆記錄:</strong></p>';
                    echo '<ul class="feature-list">';
                    foreach ($recentLogs as $log) {
                        $status = $log['success'] ? '✅' : '❌';
                        echo '<li>' . $status . ' ' . htmlspecialchars($log['user_email']) . 
                             ' - ' . htmlspecialchars($log['action']) . 
                             ' (' . $log['created_at'] . ')</li>';
                    }
                    echo '</ul>';
                }
            } else {
                echo '<p>❌ login_logs 資料表不存在</p>';
            }
        } catch (PDOException $e) {
            echo '<p style="color: red;">❌ 檢查失敗: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        
        echo '</div>';
        ?>
    </div>

    <div class="container">
        <h2>🎯 演示按鈕預覽</h2>
        
        <div class="demo-preview">
            <p style="margin-bottom: 15px; color: #666;">登入頁面中的演示按鈕外觀：</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px; max-width: 400px; margin-left: auto; margin-right: auto;">
                <button class="demo-btn-preview" style="background: #28a745;">
                    🎯 演示帳號
                </button>
                <button class="demo-btn-preview" style="background: #dc3545;">
                    🧪 測試帳號
                </button>
            </div>
            
            <button class="demo-btn-preview" style="background: #17a2b8; width: 200px;">
                👥 訪客帳號
            </button>
            
            <p style="margin-top: 15px; font-size: 14px; color: #666;">
                點擊按鈕會自動填入對應的登入信息
            </p>
        </div>
    </div>

    <div class="container">
        <h2>🎯 功能完成清單</h2>
        
        <ul class="feature-list">
            <li>✅ 創建了 login_logs 資料表用於存儲登入記錄</li>
            <li>✅ 開發了 DatabaseLogger 類別處理資料庫日誌</li>
            <li>✅ 修改了 Auth 類別使用資料庫記錄</li>
            <li>✅ 更新了 login.php 的 AJAX 處理邏輯</li>
            <li>✅ 創建了三個演示帳號供快速登入</li>
            <li>✅ 在登入頁面添加了美觀的演示按鈕</li>
            <li>✅ 實現了一鍵填入登入信息功能</li>
            <li>✅ 添加了視覺反饋和動畫效果</li>
            <li>✅ 移除了調試按鈕，保持頁面整潔</li>
            <li>✅ 修復了 JavaScript 錯誤</li>
            <li>✅ 提供了完整的管理和測試工具</li>
        </ul>
    </div>

    <div class="container">
        <h2>🚀 立即體驗</h2>
        
        <div class="status-card warning">
            <h4>📝 使用說明</h4>
            <ol>
                <li>點擊下方的"前往登入頁面"按鈕</li>
                <li>在登入表單下方找到"快速演示登入"區域</li>
                <li>點擊任一演示按鈕（建議先試 🎯 演示帳號）</li>
                <li>觀察表單自動填入和提示訊息</li>
                <li>點擊登入按鈕完成登入</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="pages/login.php" class="btn success" style="font-size: 18px; padding: 15px 30px;">
                🚀 前往登入頁面體驗
            </a>
        </div>
        
        <div style="text-align: center;">
            <a href="view_login_logs.php" class="btn">📊 查看登入記錄</a>
            <a href="database_info.php" class="btn">🗄️ 資料庫信息</a>
            <a href="test_demo_login.php" class="btn">🧪 功能測試</a>
        </div>
    </div>

    <div class="container">
        <h2>🎉 任務完成總結</h2>
        
        <div class="status-card success">
            <h4>✅ 主要成就</h4>
            <ul>
                <li><strong>登入記錄遷移</strong>：從檔案日誌成功遷移至資料庫存儲</li>
                <li><strong>演示按鈕功能</strong>：實現了一鍵演示登入體驗</li>
                <li><strong>用戶體驗提升</strong>：美觀的界面設計和流暢的互動</li>
                <li><strong>系統安全性</strong>：標準的密碼加密和安全機制</li>
                <li><strong>管理工具</strong>：完整的查看、測試和管理功能</li>
            </ul>
        </div>
        
        <div class="status-card info">
            <h4>📊 技術特性</h4>
            <ul>
                <li>結構化的資料庫存儲，支援複雜查詢和統計</li>
                <li>響應式設計，適配各種設備和螢幕尺寸</li>
                <li>優雅的動畫效果和視覺反饋</li>
                <li>完善的錯誤處理和回退機制</li>
                <li>易於維護和擴展的代碼結構</li>
            </ul>
        </div>
        
        <p style="text-align: center; font-size: 18px; color: #28a745; font-weight: bold;">
            🎯 所有功能已完成並可正常使用！
        </p>
    </div>

    <script>
        // 為演示按鈕添加懸停效果
        document.querySelectorAll('.demo-btn-preview').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>
