<?php
$dbConfig = require '../config/database.php';

try {
    $pdo = new PDO('mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'], $dbConfig['user'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "正在清理舊表...\n";
    $pdo->exec('DROP TABLE IF EXISTS attendees');
    echo "✓ 刪除 attendees 表\n";
    
    $pdo->exec('DROP TABLE IF EXISTS meetings');
    echo "✓ 刪除 meetings 表\n";
    
    $pdo->exec('DROP TABLE IF EXISTS profiles');
    echo "✓ 刪除 profiles 表\n";
    
    $pdo->exec('DROP TABLE IF EXISTS users');
    echo "✓ 刪除 users 表\n";
    
    echo "\n舊表已清理完成！\n";
    echo "現在請執行 php init.php 重新建立表格\n";
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}
?>
