<?php
// 簡化的會議 API 測試

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // 載入資料庫配置
    $dbConfig = require 'config/database.php';
    
    // 建立 PDO 連接
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // 處理 POST 請求創建會議
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 獲取 POST 資料
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('無效的 JSON 資料');
        }
        
        // 驗證必要欄位
        if (empty($input['name'])) {
            throw new Exception('會議名稱不能為空');
        }

        if (empty($input['created_by'])) {
            throw new Exception('創建者 ID 不能為空');
        }

        // 檢查會議名稱是否重複
        $checkNameQuery = "SELECT COUNT(*) FROM meetings WHERE name = ? AND created_by = ?";
        $checkStmt = $pdo->prepare($checkNameQuery);
        $checkStmt->execute([$input['name'], $input['created_by']]);
        $nameCount = $checkStmt->fetchColumn();

        if ($nameCount > 0) {
            throw new Exception('會議名稱已存在，請使用不同的名稱');
        }

        // 驗證日期邏輯
        if (!empty($input['start_date']) && !empty($input['end_date'])) {
            $startDate = new DateTime($input['start_date']);
            $endDate = new DateTime($input['end_date']);

            if ($endDate <= $startDate) {
                throw new Exception('結束時間必須晚於開始時間');
            }
        }

        // 驗證開始時間不能是過去時間
        if (!empty($input['start_date'])) {
            $startDate = new DateTime($input['start_date']);
            $now = new DateTime();

            if ($startDate < $now) {
                throw new Exception('會議開始時間不能是過去時間');
            }
        }
        
        // 生成 UUID
        $id = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
        
        // 插入資料庫
        $query = "INSERT INTO meetings (id, name, description, start_date, end_date, location, created_by) 
                  VALUES (:id, :name, :description, :start_date, :end_date, :location, :created_by)";
        $stmt = $pdo->prepare($query);
        
        $result = $stmt->execute([
            ':id' => $id,
            ':name' => $input['name'],
            ':description' => $input['description'] ?? null,
            ':start_date' => $input['start_date'] ?? null,
            ':end_date' => $input['end_date'] ?? null,
            ':location' => $input['location'] ?? null,
            ':created_by' => $input['created_by']
        ]);
        
        if ($result) {
            // 獲取創建的會議
            $stmt = $pdo->prepare("SELECT * FROM meetings WHERE id = ?");
            $stmt->execute([$id]);
            $meeting = $stmt->fetch();
            
            http_response_code(201);
            echo json_encode([
                'status' => 'success',
                'message' => '會議創建成功',
                'data' => $meeting
            ]);
        } else {
            throw new Exception('會議創建失敗');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 獲取會議列表
        $created_by = $_GET['created_by'] ?? null;
        
        if ($created_by) {
            $stmt = $pdo->prepare("SELECT * FROM meetings WHERE created_by = ? ORDER BY created_at DESC");
            $stmt->execute([$created_by]);
        } else {
            $stmt = $pdo->prepare("SELECT * FROM meetings ORDER BY created_at DESC");
            $stmt->execute();
        }
        
        $meetings = $stmt->fetchAll();
        
        echo json_encode([
            'status' => 'success',
            'data' => $meetings,
            'count' => count($meetings)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'status' => 'error',
            'message' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
