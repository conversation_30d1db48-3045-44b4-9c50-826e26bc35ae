<?php
/**
 * SignAttend PHP Frontend - 會議設定頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入配置檔案
require_once __DIR__ . '/../../config/config.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 要求使用者登入
$auth->requireAuth();

// 取得使用者資訊
$user = $auth->getCurrentUser();
$profile = $auth->getCurrentProfile();

// 模擬會議資料
$currentMeeting = [
    'id' => 1,
    'name' => '共同試驗線上說明會議',
    'description' => '探討共同試驗的相關議題和實施方案',
    'location' => '服務大樓4樓',
    'start_date' => '2025-06-18',
    'start_time' => '14:00',
    'end_date' => '2025-06-18',
    'end_time' => '17:00',
    'max_attendees' => 50,
    'status' => 'active'
];

// 處理表單提交
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $start_date = $_POST['start_date'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_date = $_POST['end_date'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $max_attendees = intval($_POST['max_attendees'] ?? 0);
    $status = $_POST['status'] ?? 'active';

    // 基本驗證
    if (empty($name)) {
        $error = '請輸入會議名稱';
    } elseif (empty($start_date) || empty($start_time)) {
        $error = '請選擇會議開始時間';
    } else {
        try {
            // 模擬更新成功
            $success = '會議設定已更新！';
            
            // 更新模擬資料
            $currentMeeting['name'] = $name;
            $currentMeeting['description'] = $description;
            $currentMeeting['location'] = $location;
            $currentMeeting['start_date'] = $start_date;
            $currentMeeting['start_time'] = $start_time;
            $currentMeeting['end_date'] = $end_date;
            $currentMeeting['end_time'] = $end_time;
            $currentMeeting['max_attendees'] = $max_attendees;
            $currentMeeting['status'] = $status;
            
        } catch (Exception $e) {
            $error = '更新會議設定時發生錯誤：' . $e->getMessage();
        }
    }
}

// 頁面標題
$pageTitle = '會議設定';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - 會議報到管理系統</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gray-50">

<!-- Header -->
<header class="border-b bg-white shadow-sm">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center max-w-7xl">
        <h1 class="text-xl font-semibold">會議報到管理系統</h1>
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2 text-sm text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span>歡迎，<?= htmlspecialchars($user['email'] ?? '<EMAIL>') ?></span>
            </div>
            <a href="<?= page_url('pages/logout.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" x2="9" y1="12" y2="12"></line>
                </svg>
                登出
            </a>
        </div>
    </div>
</header>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?= page_url('pages/dashboard.php') ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                    儀表板
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">會議設定</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Page Title -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight">會議設定</h1>
        <p class="text-gray-600 mt-1">管理會議的基本資訊和設定</p>
    </div>

    <!-- Error/Success Messages -->
    <?php if (!empty($error)): ?>
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <p class="text-sm text-red-800"><?= htmlspecialchars($error) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex">
                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <p class="text-sm text-green-800"><?= htmlspecialchars($success) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Form -->
    <div class="bg-white rounded-lg border shadow-sm">
        <div class="p-6">
            <form method="POST" action="" class="space-y-6">
                <?php generate_csrf_token_field(); ?>

                <!-- 會議名稱 -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">會議名稱 *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['name'] ?? $currentMeeting['name']) ?>"
                           placeholder="請輸入會議名稱">
                </div>

                <!-- 會議描述 -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">會議描述</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="請輸入會議描述"><?= htmlspecialchars($_POST['description'] ?? $currentMeeting['description']) ?></textarea>
                </div>

                <!-- 會議地點 -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">會議地點</label>
                    <input type="text" id="location" name="location"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['location'] ?? $currentMeeting['location']) ?>"
                           placeholder="請輸入會議地點">
                </div>

                <!-- 開始時間 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">開始日期 *</label>
                        <input type="date" id="start_date" name="start_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['start_date'] ?? $currentMeeting['start_date']) ?>">
                    </div>
                    <div>
                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">開始時間 *</label>
                        <input type="time" id="start_time" name="start_time" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['start_time'] ?? $currentMeeting['start_time']) ?>">
                    </div>
                </div>

                <!-- 結束時間 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                        <input type="date" id="end_date" name="end_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['end_date'] ?? $currentMeeting['end_date']) ?>">
                    </div>
                    <div>
                        <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">結束時間</label>
                        <input type="time" id="end_time" name="end_time"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['end_time'] ?? $currentMeeting['end_time']) ?>">
                    </div>
                </div>

                <!-- 最大參與者數 -->
                <div>
                    <label for="max_attendees" class="block text-sm font-medium text-gray-700 mb-2">最大參與者數</label>
                    <input type="number" id="max_attendees" name="max_attendees" min="1" max="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['max_attendees'] ?? $currentMeeting['max_attendees']) ?>"
                           placeholder="請輸入最大參與者數">
                </div>

                <!-- 會議狀態 -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">會議狀態</label>
                    <select id="status" name="status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="active" <?= ($_POST['status'] ?? $currentMeeting['status']) === 'active' ? 'selected' : '' ?>>進行中</option>
                        <option value="draft" <?= ($_POST['status'] ?? $currentMeeting['status']) === 'draft' ? 'selected' : '' ?>>草稿</option>
                        <option value="completed" <?= ($_POST['status'] ?? $currentMeeting['status']) === 'completed' ? 'selected' : '' ?>>已結束</option>
                        <option value="cancelled" <?= ($_POST['status'] ?? $currentMeeting['status']) === 'cancelled' ? 'selected' : '' ?>>已取消</option>
                    </select>
                </div>

                <!-- 提交按鈕 -->
                <div class="flex gap-4 pt-4">
                    <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 rounded-md px-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17,21 17,13 7,13 7,21"></polyline>
                            <polyline points="7,3 7,8 15,8"></polyline>
                        </svg>
                        儲存設定
                    </button>
                    <a href="<?= page_url('pages/dashboard.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md px-6 no-underline">
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

</body>
</html>
