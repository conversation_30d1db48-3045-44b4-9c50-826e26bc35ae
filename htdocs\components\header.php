<?php
// 防止直接訪問
if (!defined('SIGNATTEND_INIT')) {
    die('Direct access not allowed');
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自訂 CSS -->
    <link href="<?= asset_url('css/custom.css') ?>" rel="stylesheet">
    
    <!-- 頁面特定 CSS -->
    <?php if (isset($pageStyles)): ?>
        <?php foreach ($pageStyles as $style): ?>
            <link href="<?= asset_url('css/' . $style) ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <header>
        <!-- 導航列 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?= page_url('index.php') ?>">
                    <?= APP_NAME ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= page_url('index.php') ?>">首頁</a>
                        </li>
                        <?php if (isset($auth) && $auth->isLoggedIn()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= page_url('pages/dashboard.php') ?>">儀表板</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= page_url('pages/meetings.php') ?>">會議管理</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex">
                        <?php if (isset($auth) && $auth->isLoggedIn()): ?>
                            <div class="dropdown">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                    <?= e($auth->getCurrentUser()['name'] ?? '使用者') ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="<?= page_url('pages/profile.php') ?>">個人資料</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?= page_url('pages/logout.php') ?>">登出</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <a href="<?= page_url('pages/login.php') ?>" class="btn btn-outline-light">登入</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main>