<?php
// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
// 確保這裡的路徑正確，以便載入 DB_HOST, DB_NAME 等常數
require_once __DIR__ . '/../config/config.production.php';

// 載入 DatabaseLogger 類別
require_once __DIR__ . '/../utils/DatabaseLogger.php';

echo "=== DatabaseLogger 測試開始 ===\n";

try {
    $dbLogger = new DatabaseLogger();

    if (!$dbLogger) {
        echo "錯誤: 無法實例化 DatabaseLogger。\n";
        exit;
    }

    echo "DatabaseLogger 實例化成功。\n";

    // 測試確保 login_logs 表存在
    echo "測試確保 login_logs 表存在...\n";
    $reflection = new ReflectionClass($dbLogger);
    $method = $reflection->getMethod('ensureLoginLogsTable');
    $method->setAccessible(true); // 允許訪問私有方法
    $tableExists = $method->invoke($dbLogger);

    if ($tableExists) {
        echo "login_logs 表存在或已成功創建。\n";
    } else {
        echo "錯誤: login_logs 表不存在或無法創建。請檢查資料庫權限和配置。\n";
        exit;
    }

    // 測試獲取最近的登入記錄
    echo "測試獲取最近的登入記錄...\n";
    $recentLogs = $dbLogger->getRecentLogs(5); // 獲取最近5條記錄

    if (!empty($recentLogs)) {
        echo "成功獲取到最近的登入記錄：\n";
        foreach ($recentLogs as $log) {
            echo json_encode($log, JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "沒有獲取到任何登入記錄，或獲取失敗。\n";
    }

    // 測試記錄一條新的登入活動
    echo "測試記錄一條新的登入活動...\n";
    $testEmail = "<EMAIL>";
    $testUserId = "test_uuid_123";
    $logSuccess = $dbLogger->logActivity("login_test", "測試登入活動", $testEmail, $testUserId, true);

    if ($logSuccess) {
        echo "成功記錄一條新的登入活動。\n";
        // 再次獲取記錄以驗證
        $updatedLogs = $dbLogger->getRecentLogs(1, $testEmail);
        if (!empty($updatedLogs)) {
            echo "新記錄：\n";
            echo json_encode($updatedLogs[0], JSON_UNESCAPED_UNICODE) . "\n";
        }
    } else {
        echo "錯誤: 無法記錄新的登入活動。\n";
    }

} catch (Exception $e) {
    echo "捕獲到異常: " . $e->getMessage() . "\n";
    echo "堆疊追蹤: " . $e->getTraceAsString() . "\n";
}

echo "=== DatabaseLogger 測試結束 ===\n";
?>