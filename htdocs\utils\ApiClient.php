<?php
/**
 * API 客戶端類別
 * 負責與後端 API 進行通訊
 */

class ApiClient {
    private $baseUrl;
    private $token;
    private $timeout;
    private $retryCount;
    
    public function __construct() {
        $this->baseUrl = API_BASE_URL;
        $this->token = $_SESSION['auth_token'] ?? null;
        $this->timeout = API_TIMEOUT;
        $this->retryCount = API_RETRY_COUNT;
    }
    
    /**
     * 設定認證 Token
     */
    public function setToken($token) {
        $this->token = $token;
        $_SESSION['auth_token'] = $token;
    }
    
    /**
     * 清除認證 Token
     */
    public function clearToken() {
        $this->token = null;
        unset($_SESSION['auth_token']);
    }
    
    /**
     * GET 請求
     */
    public function get($endpoint, $params = []) {
        $url = $this->baseUrl . $endpoint;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        return $this->makeRequest('GET', $url);
    }
    
    /**
     * POST 請求
     */
    public function post($endpoint, $data = []) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('POST', $url, $data);
    }
    
    /**
     * PUT 請求
     */
    public function put($endpoint, $data = []) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('PUT', $url, $data);
    }
    
    /**
     * DELETE 請求
     */
    public function delete($endpoint) {
        $url = $this->baseUrl . $endpoint;
        return $this->makeRequest('DELETE', $url);
    }
    
    /**
     * 執行 HTTP 請求
     */
    private function makeRequest($method, $url, $data = null) {
        $attempt = 0;
        
        while ($attempt < $this->retryCount) {
            try {
                $ch = curl_init();
                
                // 基本 cURL 設定
                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => $this->timeout,
                    CURLOPT_CUSTOMREQUEST => $method,
                    CURLOPT_HTTPHEADER => $this->getHeaders(),
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_MAXREDIRS => 3
                ]);
                
                // 如果有資料需要傳送
                if ($data !== null && in_array($method, ['POST', 'PUT'])) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                
                // 執行請求
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                
                curl_close($ch);
                
                // 檢查 cURL 錯誤
                if ($error) {
                    throw new Exception("cURL Error: " . $error);
                }
                
                // 解析回應
                $decodedResponse = json_decode($response, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("Invalid JSON response: " . json_last_error_msg());
                }
                
                // 檢查 HTTP 狀態碼
                if ($httpCode >= 400) {
                    $errorMessage = $decodedResponse['message'] ?? "HTTP Error $httpCode";
                    throw new Exception($errorMessage, $httpCode);
                }
                
                // 記錄成功的 API 呼叫
                $this->logApiCall($method, $url, $httpCode, true);
                
                return [
                    'success' => true,
                    'data' => $decodedResponse,
                    'http_code' => $httpCode
                ];
                
            } catch (Exception $e) {
                $attempt++;
                
                // 記錄失敗的 API 呼叫
                $this->logApiCall($method, $url, $e->getCode(), false, $e->getMessage());
                
                // 如果是最後一次嘗試，拋出異常
                if ($attempt >= $this->retryCount) {
                    return [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'http_code' => $e->getCode()
                    ];
                }
                
                // 等待後重試
                sleep(1);
            }
        }
    }
    
    /**
     * 取得請求標頭
     */
    private function getHeaders() {
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: SignAttend-Frontend/' . APP_VERSION
        ];
        
        // 添加認證標頭
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        
        // 添加 CSRF Token (如果有)
        if (isset($_SESSION[CSRF_TOKEN_NAME])) {
            $headers[] = 'X-CSRF-Token: ' . $_SESSION[CSRF_TOKEN_NAME];
        }
        
        return $headers;
    }
    
    /**
     * 記錄 API 呼叫
     */
    private function logApiCall($method, $url, $httpCode, $success, $error = null) {
        if (!LOG_ERRORS) return;
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'url' => $url,
            'http_code' => $httpCode,
            'success' => $success,
            'user_ip' => function_exists('get_client_ip') ? get_client_ip() : ($_SERVER['REMOTE_ADDR'] ?? 'unknown'),
            'user_id' => $_SESSION['user']['id'] ?? null
        ];
        
        if ($error) {
            $logData['error'] = $error;
        }
        
        $logMessage = json_encode($logData) . "\n";
        $logFile = LOG_PATH . '/api_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 檢查 API 連線狀態
     */
    public function checkConnection() {
        try {
            $response = $this->get('/test');
            return $response['success'];
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 取得 API 狀態資訊
     */
    public function getApiStatus() {
        return $this->get('/test');
    }
    
    /**
     * 上傳檔案
     */
    public function uploadFile($endpoint, $filePath, $fieldName = 'file') {
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'error' => 'File not found: ' . $filePath
            ];
        }
        
        $ch = curl_init();
        $url = $this->baseUrl . $endpoint;
        
        $postData = [
            $fieldName => new CURLFile($filePath)
        ];
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout * 2, // 上傳時間較長
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->token,
                'Accept: application/json'
            ]
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => "Upload Error: " . $error
            ];
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'success' => $httpCode < 400,
            'data' => $decodedResponse,
            'http_code' => $httpCode
        ];
    }
    
    /**
     * 批次請求
     */
    public function batch($requests) {
        $results = [];
        
        foreach ($requests as $key => $request) {
            $method = $request['method'] ?? 'GET';
            $endpoint = $request['endpoint'];
            $data = $request['data'] ?? null;
            
            switch (strtoupper($method)) {
                case 'GET':
                    $results[$key] = $this->get($endpoint, $data);
                    break;
                case 'POST':
                    $results[$key] = $this->post($endpoint, $data);
                    break;
                case 'PUT':
                    $results[$key] = $this->put($endpoint, $data);
                    break;
                case 'DELETE':
                    $results[$key] = $this->delete($endpoint);
                    break;
                default:
                    $results[$key] = [
                        'success' => false,
                        'error' => 'Unsupported method: ' . $method
                    ];
            }
        }
        
        return $results;
    }
}
?>
