<?php

namespace SignAttend\Controllers;

use PDO;
use SignAttend\Models\Meeting;

class MeetingController {
    private $db;
    private $meetingModel;

    public function __construct(PDO $db) {
        $this->db = $db;
        $this->meetingModel = new Meeting($db);
    }

    // 取得所有會議
    public function getAllMeetings() {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 獲取查詢參數
            $created_by = $_GET['created_by'] ?? null;

            // 取得會議列表
            $meetings = $this->meetingModel->readAll($created_by);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $meetings,
                'count' => count($meetings)
            ]);

        } catch (\Exception $e) {
            error_log("Get all meetings error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '取得會議列表失敗：' . $e->getMessage()]);
        }
    }

    // 取得單一會議詳情
    public function getMeeting($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '會議 ID 不能為空']);
                return;
            }

            // 取得會議
            $meeting = $this->meetingModel->readOne($id);

            if (!$meeting) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '會議不存在']);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'data' => $meeting
            ]);

        } catch (\Exception $e) {
            error_log("Get meeting error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '取得會議詳情失敗：' . $e->getMessage()]);
        }
    }

    // 建立新會議
    public function createMeeting() {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 獲取 POST 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 驗證必要欄位
            if (!isset($input['name']) || !isset($input['created_by'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '缺少必要欄位：name, created_by']);
                return;
            }

            // 準備會議資料
            $meetingData = [
                'name' => $input['name'],
                'description' => $input['description'] ?? null,
                'start_date' => $input['start_date'] ?? null,
                'end_date' => $input['end_date'] ?? null,
                'location' => $input['location'] ?? null,
                'created_by' => $input['created_by']
            ];

            // 驗證日期格式（如果提供）
            if ($meetingData['start_date'] && !$this->isValidDateTime($meetingData['start_date'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '開始日期格式不正確']);
                return;
            }

            if ($meetingData['end_date'] && !$this->isValidDateTime($meetingData['end_date'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '結束日期格式不正確']);
                return;
            }

            // 創建會議
            $meeting = $this->meetingModel->create($meetingData);

            if (!$meeting) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '會議創建失敗']);
                return;
            }

            http_response_code(201);
            echo json_encode([
                'status' => 'success',
                'message' => '會議創建成功',
                'data' => $meeting
            ]);

        } catch (\Exception $e) {
            error_log("Create meeting error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '創建會議失敗：' . $e->getMessage()]);
        }
    }

    // 更新會議
    public function updateMeeting($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '會議 ID 不能為空']);
                return;
            }

            // 檢查會議是否存在
            $existingMeeting = $this->meetingModel->readOne($id);
            if (!$existingMeeting) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '會議不存在']);
                return;
            }

            // 獲取 PUT 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 準備更新資料
            $updateData = [];
            if (isset($input['name'])) $updateData['name'] = $input['name'];
            if (isset($input['description'])) $updateData['description'] = $input['description'];
            if (isset($input['start_date'])) $updateData['start_date'] = $input['start_date'];
            if (isset($input['end_date'])) $updateData['end_date'] = $input['end_date'];
            if (isset($input['location'])) $updateData['location'] = $input['location'];

            // 驗證日期格式（如果提供）
            if (isset($updateData['start_date']) && $updateData['start_date'] && !$this->isValidDateTime($updateData['start_date'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '開始日期格式不正確']);
                return;
            }

            if (isset($updateData['end_date']) && $updateData['end_date'] && !$this->isValidDateTime($updateData['end_date'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '結束日期格式不正確']);
                return;
            }

            // 更新會議
            $success = $this->meetingModel->update($id, $updateData);

            if (!$success) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '會議更新失敗']);
                return;
            }

            // 取得更新後的會議資料
            $updatedMeeting = $this->meetingModel->readOne($id);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '會議更新成功',
                'data' => $updatedMeeting
            ]);

        } catch (\Exception $e) {
            error_log("Update meeting error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '更新會議失敗：' . $e->getMessage()]);
        }
    }

    // 刪除會議
    public function deleteMeeting($id) {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 驗證 ID
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '會議 ID 不能為空']);
                return;
            }

            // 檢查會議是否存在
            $existingMeeting = $this->meetingModel->readOne($id);
            if (!$existingMeeting) {
                http_response_code(404);
                echo json_encode(['status' => 'error', 'message' => '會議不存在']);
                return;
            }

            // 刪除會議
            $success = $this->meetingModel->delete($id);

            if (!$success) {
                http_response_code(500);
                echo json_encode(['status' => 'error', 'message' => '會議刪除失敗']);
                return;
            }

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '會議刪除成功'
            ]);

        } catch (\Exception $e) {
            error_log("Delete meeting error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '刪除會議失敗：' . $e->getMessage()]);
        }
    }

    // 驗證日期時間格式
    private function isValidDateTime($dateTime) {
        if (empty($dateTime)) return true; // 允許空值

        $formats = ['Y-m-d H:i:s', 'Y-m-d\TH:i:s', 'Y-m-d\TH:i:s\Z'];

        foreach ($formats as $format) {
            $d = \DateTime::createFromFormat($format, $dateTime);
            if ($d && $d->format($format) === $dateTime) {
                return true;
            }
        }

        return false;
    }
}