<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikecao/flight' => array(
            'pretty_version' => 'v1.3.9',
            'version' => '1.3.9.0',
            'reference' => 'bc5e03ca59accb3ef37e31b6d4a2a9c78c7bb7ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikecao/flight',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
