<?php
/**
 * 修復Dashboard CSP和版面問題
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 修復Dashboard CSP和版面問題 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 1. 檢查當前環境
echo "1. 檢查當前環境\n";
echo "-------------------\n";
echo "伺服器名稱: " . ($_SERVER['SERVER_NAME'] ?? '未知') . "\n";
echo "HTTP主機: " . ($_SERVER['HTTP_HOST'] ?? '未知') . "\n";
echo "是否正式環境: " . (strpos($_SERVER['HTTP_HOST'] ?? '', 'attendance.app.tn') !== false ? '是' : '否') . "\n";
echo "調試模式: " . (defined('DEBUG_MODE') && DEBUG_MODE ? '開啟' : '關閉') . "\n\n";

// 2. 檢查CSP配置
echo "2. 檢查CSP配置\n";
echo "-------------------\n";

// 讀取當前配置文件
$configFile = __DIR__ . '/config/config.production.php';
if (file_exists($configFile)) {
    $configContent = file_get_contents($configFile);
    
    // 檢查CSP配置
    if (strpos($configContent, 'https://cdn.tailwindcss.com') !== false) {
        echo "✅ CSP已包含 cdn.tailwindcss.com\n";
    } else {
        echo "❌ CSP缺少 cdn.tailwindcss.com\n";
    }
    
    if (strpos($configContent, 'https://cdnjs.cloudflare.com') !== false) {
        echo "✅ CSP已包含 cdnjs.cloudflare.com\n";
    } else {
        echo "❌ CSP缺少 cdnjs.cloudflare.com\n";
    }
    
    if (strpos($configContent, 'https://unpkg.com') !== false) {
        echo "✅ CSP已包含 unpkg.com\n";
    } else {
        echo "❌ CSP缺少 unpkg.com\n";
    }
} else {
    echo "❌ 找不到正式環境配置文件\n";
}
echo "\n";

// 3. 檢查Dashboard文件
echo "3. 檢查Dashboard文件\n";
echo "-------------------\n";

$dashboardFile = __DIR__ . '/pages/dashboard.php';
if (file_exists($dashboardFile)) {
    echo "✅ Dashboard文件存在\n";
    
    $dashboardContent = file_get_contents($dashboardFile);
    
    // 檢查外部CDN引用
    $cdnPatterns = [
        'https://cdn.tailwindcss.com' => 'Tailwind CSS',
        'https://cdnjs.cloudflare.com' => 'Cloudflare CDN',
        'https://cdn.jsdelivr.net' => 'jsDelivr CDN'
    ];
    
    foreach ($cdnPatterns as $pattern => $name) {
        if (strpos($dashboardContent, $pattern) !== false) {
            echo "✅ 使用了 $name ($pattern)\n";
        } else {
            echo "❌ 未使用 $name ($pattern)\n";
        }
    }
    
    // 檢查Tailwind配置
    if (strpos($dashboardContent, 'tailwind.config') !== false) {
        echo "✅ 包含Tailwind配置\n";
    } else {
        echo "❌ 缺少Tailwind配置\n";
    }
    
} else {
    echo "❌ Dashboard文件不存在\n";
}
echo "\n";

// 4. 創建測試頁面
echo "4. 創建CSP測試頁面\n";
echo "-------------------\n";

$testPageContent = '<?php
/**
 * CSP測試頁面
 */

// 定義應用程式初始化常數
define(\'SIGNATTEND_INIT\', true);

// 載入環境配置檔案
require_once __DIR__ . \'/config/environment.php\';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP測試頁面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-item { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">CSP和CDN測試頁面</h1>
        
        <div id="results">
            <div class="test-item" id="tailwind-test">
                <h3>Tailwind CSS測試</h3>
                <p>如果這個文字有樣式，表示Tailwind CSS加載成功</p>
            </div>
            
            <div class="test-item" id="qrcode-test">
                <h3>QRCode庫測試</h3>
                <p>QRCode庫狀態: <span id="qrcode-status">檢測中...</span></p>
            </div>
            
            <div class="test-item" id="html5qrcode-test">
                <h3>Html5Qrcode庫測試</h3>
                <p>Html5Qrcode庫狀態: <span id="html5qrcode-status">檢測中...</span></p>
            </div>
        </div>
        
        <div class="mt-6">
            <h2 class="text-2xl font-bold mb-4">測試結果</h2>
            <div id="test-summary"></div>
        </div>
    </div>

    <!-- 測試外部CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"></script>
    
    <script>
        // 測試Tailwind CSS
        if (typeof tailwind !== \'undefined\') {
            document.getElementById(\'tailwind-test\').className += \' success\';
        } else {
            document.getElementById(\'tailwind-test\').className += \' error\';
        }
        
        // 測試QRCode庫
        setTimeout(function() {
            const qrcodeStatus = document.getElementById(\'qrcode-status\');
            const qrcodeTest = document.getElementById(\'qrcode-test\');
            
            if (typeof QRCode !== \'undefined\') {
                qrcodeStatus.textContent = \'✅ 加載成功\';
                qrcodeTest.className += \' success\';
            } else {
                qrcodeStatus.textContent = \'❌ 加載失敗\';
                qrcodeTest.className += \' error\';
            }
            
            // 測試Html5Qrcode庫
            const html5qrcodeStatus = document.getElementById(\'html5qrcode-status\');
            const html5qrcodeTest = document.getElementById(\'html5qrcode-test\');
            
            if (typeof Html5Qrcode !== \'undefined\') {
                html5qrcodeStatus.textContent = \'✅ 加載成功\';
                html5qrcodeTest.className += \' success\';
            } else {
                html5qrcodeStatus.textContent = \'❌ 加載失敗\';
                html5qrcodeTest.className += \' error\';
            }
            
            // 生成測試總結
            const summary = document.getElementById(\'test-summary\');
            const successCount = document.querySelectorAll(\'.success\').length;
            const totalTests = 3;
            
            if (successCount === totalTests) {
                summary.innerHTML = \'<div class="test-item success"><strong>✅ 所有測試通過</strong><br>CSP配置正確，所有外部資源都能正常加載。</div>\';
            } else {
                summary.innerHTML = \'<div class="test-item error"><strong>❌ 部分測試失敗</strong><br>通過: \' + successCount + \'/\' + totalTests + \'<br>請檢查CSP配置或網路連接。</div>\';
            }
        }, 1000);
    </script>
</body>
</html>';

$testPageFile = __DIR__ . '/csp_test.php';
if (file_put_contents($testPageFile, $testPageContent)) {
    echo "✅ CSP測試頁面創建成功: csp_test.php\n";
} else {
    echo "❌ CSP測試頁面創建失敗\n";
}
echo "\n";

// 5. 提供修復建議
echo "5. 修復建議\n";
echo "-------------------\n";
echo "1. 訪問CSP測試頁面: " . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/csp_test.php' : 'csp_test.php') . "\n";
echo "2. 如果測試失敗，請檢查以下配置:\n";
echo "   - config.production.php 中的 Content-Security-Policy 設置\n";
echo "   - 確保包含所有必要的CDN域名\n";
echo "3. 如果版面跑版，可能是Tailwind CSS未正確加載\n";
echo "4. 檢查瀏覽器開發者工具的Console和Network標籤\n\n";

echo "=== 修復完成 ===\n";
echo "請訪問測試頁面確認修復效果\n";
?>
