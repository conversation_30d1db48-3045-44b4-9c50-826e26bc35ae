<?php
/**
 * 模擬真實 Web 登入環境
 */

// 模擬真實的 Web 環境變數
$_SERVER['HTTP_HOST'] = 'attendance.app.tn';
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
$_SERVER['REMOTE_ADDR'] = '*************';

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

echo "=== 模擬真實 Web 登入環境 ===\n";
echo "HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "\n";
echo "USER_AGENT: " . $_SERVER['HTTP_USER_AGENT'] . "\n";
echo "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "\n";

// 載入環境配置檔案（這會檢測到生產環境）
require_once __DIR__ . '/config/environment.php';

echo "\n當前環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "LOG_ERRORS: " . (defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義') . "\n";
echo "LOG_PATH: " . (defined('LOG_PATH') ? LOG_PATH : '未定義') . "\n";

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 模擬 pages/login.php 的完整流程
echo "\n=== 模擬 pages/login.php 完整流程 ===\n";

// 檢查是否已經登入（模擬 pages/login.php 的檢查）
if ($auth->isLoggedIn()) {
    echo "用戶已登入，會重定向到儀表板\n";
} else {
    echo "用戶未登入，繼續登入流程\n";
}

// 模擬表單提交
$_POST['email'] = '<EMAIL>';
$_POST['password'] = 'definitely_wrong_password';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    echo "\n處理 POST 請求:\n";
    echo "Email: " . $email . "\n";
    echo "Password: [隱藏]\n";

    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
        echo "驗證失敗: " . $error . "\n";
    } else {
        echo "開始登入驗證...\n";
        
        // 記錄登入前的日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeContent = file_exists($authLogFile) ? file_get_contents($authLogFile) : '';
        $beforeLines = empty($beforeContent) ? 0 : count(explode("\n", trim($beforeContent)));
        
        echo "登入前日誌行數: " . $beforeLines . "\n";
        
        $result = $auth->login($email, $password, $rememberMe);
        
        // 記錄登入後的日誌狀態
        $afterContent = file_exists($authLogFile) ? file_get_contents($authLogFile) : '';
        $afterLines = empty($afterContent) ? 0 : count(explode("\n", trim($afterContent)));
        
        echo "登入後日誌行數: " . $afterLines . "\n";
        echo "新增日誌行數: " . ($afterLines - $beforeLines) . "\n";

        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            echo "登入成功: " . $success . "\n";
        } else {
            $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
            echo "登入失敗: " . $error . "\n";
        }
        
        // 檢查最新的日誌記錄
        if (file_exists($authLogFile)) {
            $lines = explode("\n", trim($afterContent));
            $lastLine = end($lines);
            
            if (!empty($lastLine)) {
                $data = json_decode($lastLine, true);
                if ($data && $data['user_email'] === $email) {
                    echo "\n✅ 找到新的日誌記錄:\n";
                    echo "   時間: " . $data['timestamp'] . "\n";
                    echo "   動作: " . $data['action'] . "\n";
                    echo "   描述: " . $data['description'] . "\n";
                    echo "   IP: " . $data['ip_address'] . "\n";
                } else {
                    echo "\n❌ 沒有找到對應的新日誌記錄\n";
                    if ($data) {
                        echo "   最後記錄的 email: " . ($data['user_email'] ?? '無') . "\n";
                    }
                }
            }
        }
    }
}

echo "\n模擬完成！\n";
?>
