<?php
// 簡單的 PHP 測試檔案
echo "<h1>PHP 測試頁面</h1>";
echo "<p>PHP 版本: " . phpversion() . "</p>";
echo "<p>當前時間: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>當前目錄: " . __DIR__ . "</p>";

// 測試檔案路徑
echo "<h2>檔案路徑測試</h2>";
echo "<p>config.php 存在: " . (file_exists(__DIR__ . '/config/config.php') ? '是' : '否') . "</p>";
echo "<p>functions.php 存在: " . (file_exists(__DIR__ . '/includes/functions.php') ? '是' : '否') . "</p>";
echo "<p>ApiClient.php 存在: " . (file_exists(__DIR__ . '/utils/ApiClient.php') ? '是' : '否') . "</p>";

// 測試後端配置檔案
$backendConfigPath = dirname(__DIR__) . '/backend/config/database.php';
echo "<p>後端配置檔案路徑: " . $backendConfigPath . "</p>";
echo "<p>後端配置檔案存在: " . (file_exists($backendConfigPath) ? '是' : '否') . "</p>";

// 測試 Session
session_start();
echo "<p>Session 啟動成功</p>";

echo "<h2>測試完成</h2>";
echo "<p><a href='index.php'>返回首頁</a></p>";
?>
