<?php

namespace SignAttend\Controllers;

use PDO;
use SignAttend\Models\User;
use SignAttend\Models\Profile;

class AuthController {
    private $db;
    private $userModel;
    private $profileModel;

    public function __construct(PDO $db) {
        $this->db = $db;
        $this->userModel = new User($db);
        $this->profileModel = new Profile($db);
    }

    // 使用者註冊
    public function register() {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 獲取 POST 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 驗證必要欄位
            if (!isset($input['email']) || !isset($input['password']) || !isset($input['name'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '缺少必要欄位：email, password, name']);
                return;
            }

            // 驗證電子郵件格式
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '電子郵件格式不正確']);
                return;
            }

            // 檢查密碼長度
            if (strlen($input['password']) < 6) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '密碼長度至少需要 6 個字元']);
                return;
            }

            // 檢查電子郵件是否已存在
            if ($this->userModel->findByEmail($input['email'])) {
                http_response_code(409);
                echo json_encode(['status' => 'error', 'message' => '此電子郵件已被註冊']);
                return;
            }

            // 開始交易
            $this->db->beginTransaction();

            try {
                // 創建使用者
                $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
                $userData = [
                    'email' => $input['email'],
                    'password_hash' => $hashedPassword
                ];
                $user = $this->userModel->create($userData);

                if (!$user) {
                    throw new \Exception('使用者創建失敗');
                }

                // 創建個人資料
                $profileData = [
                    'id' => $user['id'],
                    'name' => $input['name'],
                    'email' => $input['email']
                ];
                $profile = $this->profileModel->create($profileData);

                if (!$profile) {
                    throw new \Exception('個人資料創建失敗');
                }

                // 提交交易
                $this->db->commit();

                // 返回成功響應（不包含密碼）
                http_response_code(201);
                echo json_encode([
                    'status' => 'success',
                    'message' => '註冊成功',
                    'data' => [
                        'user' => [
                            'id' => $user['id'],
                            'email' => $user['email']
                        ],
                        'profile' => $profile
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滾交易
                $this->db->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            $errorMessage = "Register error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $errorMessage .= "\nStack trace:\n" . $e->getTraceAsString();
            }
            error_log($errorMessage);
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '註冊失敗：' . $e->getMessage()]);
        }
    }

    // 使用者登入
    public function login() {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 獲取 POST 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 驗證必要欄位
            if (!isset($input['email']) || !isset($input['password'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '缺少必要欄位：email, password']);
                return;
            }

            // 驗證使用者
            $user = $this->userModel->verifyPassword($input['email'], $input['password']);

            if (!$user) {
                // 記錄登入失敗
                $this->logLoginAttempt($input['email'], false, '密碼錯誤');

                http_response_code(401);
                echo json_encode(['status' => 'error', 'message' => '電子郵件或密碼錯誤']);
                return;
            }

            // 獲取個人資料
            $profile = $this->profileModel->findByUserId($user['id']);

            // 生成簡單的 session token (在實際應用中應該使用 JWT)
            $token = bin2hex(random_bytes(32));

            // 在實際應用中，你應該將 token 儲存在資料庫或 Redis 中
            // 這裡只是示例

            // 記錄登入成功
            $this->logLoginAttempt($input['email'], true);

            http_response_code(200);
            echo json_encode([
                'status' => 'success',
                'message' => '登入成功',
                'data' => [
                    'user' => $user,
                    'profile' => $profile,
                    'token' => $token
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Login error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '登入失敗：' . $e->getMessage()]);
        }
    }

    // 忘記密碼 - 重設密碼
    public function forgotPassword() {
        try {
            // 檢查請求方法
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
                return;
            }

            // 獲取 POST 資料
            $input = json_decode(file_get_contents('php://input'), true);

            // 驗證必要欄位
            if (!isset($input['email'])) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '缺少必要欄位：email']);
                return;
            }

            // 驗證電子郵件格式
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => '電子郵件格式不正確']);
                return;
            }

            // 查找使用者
            $user = $this->userModel->findByEmail($input['email']);

            if (!$user) {
                // 為了安全考量，即使找不到使用者也返回成功訊息
                http_response_code(200);
                echo json_encode([
                    'status' => 'success',
                    'message' => '如果該電子郵件存在於系統中，重設密碼指示已發送'
                ]);
                return;
            }

            // 生成臨時密碼
            $tempPassword = 'temp' . rand(1000, 9999);
            $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);

            // 更新使用者密碼
            $updateResult = $this->userModel->updatePassword($user['id'], $hashedPassword);

            if (!$updateResult) {
                throw new \Exception('密碼更新失敗');
            }

            // 獲取使用者個人資料
            $profile = $this->profileModel->findByUserId($user['id']);
            $userName = $profile['name'] ?? $user['email'];

            // 記錄密碼重設操作
            $this->logPasswordReset($user['id']);

            // 在開發環境中直接返回臨時密碼，生產環境應該發送郵件
            $isDevelopment = defined('DEBUG_MODE') && DEBUG_MODE;

            if ($isDevelopment) {
                http_response_code(200);
                echo json_encode([
                    'status' => 'success',
                    'message' => '密碼重設成功',
                    'data' => [
                        'temp_password' => $tempPassword,
                        'user_name' => $userName,
                        'note' => '這是開發環境，臨時密碼直接顯示。生產環境會通過郵件發送。'
                    ]
                ]);
            } else {
                // 生產環境：發送郵件（這裡只是示例，實際需要配置郵件服務）
                $emailSent = $this->sendPasswordResetEmail($user['email'], $userName, $tempPassword);

                http_response_code(200);
                echo json_encode([
                    'status' => 'success',
                    'message' => '密碼重設指示已發送到您的電子郵件',
                    'data' => [
                        'email_sent' => $emailSent
                    ]
                ]);
            }

        } catch (\Exception $e) {
            error_log("Forgot password error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['status' => 'error', 'message' => '密碼重設失敗：' . $e->getMessage()]);
        }
    }

    // 記錄密碼重設操作
    private function logPasswordReset($userId) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO password_reset_logs (user_id, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?)
            ");

            $stmt->execute([
                $userId,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 記錄失敗不影響主要流程
            error_log("Password reset log error: " . $e->getMessage());
        }
    }

    // 發送密碼重設郵件（示例實作）
    private function sendPasswordResetEmail($email, $userName, $tempPassword) {
        try {
            $subject = 'SignAttend - 密碼重設通知';
            $message = "
親愛的 {$userName} 您好，

您的 SignAttend 帳戶密碼已成功重設。以下是您的新臨時密碼：

臨時密碼：{$tempPassword}

請使用此臨時密碼登入系統，並立即修改為您的新密碼。

登入網址：" . (defined('BASE_URL') ? BASE_URL : 'http://localhost/SignAttend/frontend') . "

如果您沒有申請密碼重設，請立即聯絡系統管理員。

SignAttend 智慧簽到系統
";

            $headers = "From: <EMAIL>\r\n";
            $headers .= "Reply-To: <EMAIL>\r\n";
            $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

            // 在實際環境中，這裡應該使用專業的郵件服務
            return @mail($email, $subject, $message, $headers);
        } catch (\Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    // 記錄登入嘗試
    private function logLoginAttempt($email, $success, $reason = '') {
        try {
            // 記錄到文件日誌
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'user_email' => $email,
                'action' => $success ? 'login_success' : 'login_failed',
                'description' => $success ? 'API 登入成功' : 'API 登入失敗：' . $reason,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];

            $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";

            // 確保 LOG_PATH 已定義
            if (defined('LOG_PATH')) {
                $logFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
                file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
            }

        } catch (\Exception $e) {
            // 記錄失敗不影響主要流程
            error_log("Login attempt log error: " . $e->getMessage());
        }
    }
}