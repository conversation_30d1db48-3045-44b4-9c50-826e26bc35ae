<?php
/**
 * 實時監控真實的 AJAX 登入請求
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>實時 AJAX 登入監控</title>
    <style>
        body { font-family: monospace; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log-entry { background: #f8f9fa; padding: 8px; margin: 4px 0; border-left: 3px solid #007bff; }
        .test-form { background: #e9ecef; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 實時 AJAX 登入監控</h1>
        
        <div class="status info">
            <strong>監控狀態:</strong> 
            LOG_PATH: <?= LOG_PATH ?><br>
            當前時間: <?= date('Y-m-d H:i:s') ?><br>
            認證日誌檔案: <?= LOG_PATH . '/auth_' . date('Y-m-d') . '.log' ?>
        </div>

        <?php
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $currentSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        $currentLines = 0;
        
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $currentLines = substr_count($content, "\n");
        }
        ?>

        <div class="status">
            <strong>當前日誌狀態:</strong><br>
            檔案存在: <?= file_exists($authLogFile) ? '✅ 是' : '❌ 否' ?><br>
            檔案大小: <?= $currentSize ?> bytes<br>
            行數: <?= $currentLines ?><br>
            最後修改: <?= file_exists($authLogFile) ? date('Y-m-d H:i:s', filemtime($authLogFile)) : 'N/A' ?>
        </div>

        <div class="test-form">
            <h3>🧪 測試 AJAX 登入</h3>
            <p>使用下面的表單測試 AJAX 登入，同時監控日誌寫入情況：</p>
            
            <form id="testForm">
                <label>郵箱: <input type="email" id="email" value="test_monitor_<?= time() ?>@example.com" style="width: 300px;"></label><br><br>
                <label>密碼: <input type="password" id="password" value="test123" style="width: 300px;"></label><br><br>
                <button type="submit" class="btn">🚀 執行 AJAX 登入測試</button>
            </form>
        </div>

        <div id="results"></div>

        <h3>📋 最近的日誌記錄</h3>
        <div id="logEntries">
            <?php
            if (file_exists($authLogFile)) {
                $content = file_get_contents($authLogFile);
                $lines = explode("\n", trim($content));
                $recentLines = array_slice($lines, -5); // 最後 5 行
                
                foreach ($recentLines as $line) {
                    if (!empty($line)) {
                        echo '<div class="log-entry">' . htmlspecialchars($line) . '</div>';
                    }
                }
            } else {
                echo '<div class="status error">認證日誌檔案不存在</div>';
            }
            ?>
        </div>

        <button class="btn" onclick="refreshLogs()">🔄 重新整理日誌</button>
    </div>

    <script>
        let initialSize = <?= $currentSize ?>;
        let initialLines = <?= $currentLines ?>;

        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="status info">🔄 正在執行 AJAX 登入測試...</div>';
            
            // 記錄測試前狀態
            const beforeTime = new Date().toISOString();
            
            // 準備 AJAX 請求
            const formData = new FormData();
            formData.append('ajax_login', '1');
            formData.append('email', email);
            formData.append('password', password);
            formData.append('remember_me', '0');
            
            // 發送 AJAX 請求到 login.php
            fetch('pages/login.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers.get('content-type'));
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                let result = '';
                result += '<div class="status success">✅ AJAX 請求完成</div>';
                result += '<div class="status info"><strong>請求時間:</strong> ' + beforeTime + '</div>';
                result += '<div class="status info"><strong>響應狀態:</strong> 200</div>';
                
                try {
                    const data = JSON.parse(text);
                    result += '<div class="status info"><strong>響應數據:</strong></div>';
                    result += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    if (data.debug_info) {
                        const beforeSize = data.debug_info.before_size || 0;
                        const afterSize = data.debug_info.after_size || 0;
                        const sizeChange = afterSize - beforeSize;
                        
                        result += '<div class="status ' + (sizeChange > 0 ? 'success' : 'error') + '">';
                        result += '<strong>日誌寫入檢查:</strong><br>';
                        result += '登入前大小: ' + beforeSize + ' bytes<br>';
                        result += '登入後大小: ' + afterSize + ' bytes<br>';
                        result += '大小變化: ' + sizeChange + ' bytes<br>';
                        result += sizeChange > 0 ? '✅ 日誌寫入成功' : '❌ 日誌寫入失敗';
                        result += '</div>';
                    }
                } catch (e) {
                    result += '<div class="status error">❌ JSON 解析失敗</div>';
                    result += '<div class="status info"><strong>原始響應:</strong></div>';
                    result += '<pre>' + text + '</pre>';
                }
                
                resultsDiv.innerHTML = result;
                
                // 延遲 1 秒後重新整理日誌
                setTimeout(refreshLogs, 1000);
            })
            .catch(error => {
                console.error('AJAX error:', error);
                resultsDiv.innerHTML = '<div class="status error">❌ AJAX 請求失敗: ' + error.message + '</div>';
            });
        });

        function refreshLogs() {
            fetch('?action=get_logs')
            .then(response => response.json())
            .then(data => {
                const logDiv = document.getElementById('logEntries');
                logDiv.innerHTML = '';
                
                if (data.logs && data.logs.length > 0) {
                    data.logs.forEach(log => {
                        logDiv.innerHTML += '<div class="log-entry">' + log + '</div>';
                    });
                } else {
                    logDiv.innerHTML = '<div class="status error">沒有找到日誌記錄</div>';
                }
                
                // 更新狀態信息
                if (data.file_info) {
                    const statusDiv = document.querySelector('.status');
                    statusDiv.innerHTML = '<strong>當前日誌狀態:</strong><br>' +
                        '檔案存在: ' + (data.file_info.exists ? '✅ 是' : '❌ 否') + '<br>' +
                        '檔案大小: ' + data.file_info.size + ' bytes<br>' +
                        '行數: ' + data.file_info.lines + '<br>' +
                        '最後修改: ' + data.file_info.modified;
                }
            })
            .catch(error => {
                console.error('Refresh error:', error);
            });
        }

        // 每 5 秒自動重新整理
        setInterval(refreshLogs, 5000);
    </script>
</body>
</html>

<?php
// 處理 AJAX 請求
if (isset($_GET['action']) && $_GET['action'] === 'get_logs') {
    header('Content-Type: application/json');
    
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $response = [
        'file_info' => [
            'exists' => file_exists($authLogFile),
            'size' => file_exists($authLogFile) ? filesize($authLogFile) : 0,
            'lines' => 0,
            'modified' => file_exists($authLogFile) ? date('Y-m-d H:i:s', filemtime($authLogFile)) : 'N/A'
        ],
        'logs' => []
    ];
    
    if (file_exists($authLogFile)) {
        $content = file_get_contents($authLogFile);
        $response['file_info']['lines'] = substr_count($content, "\n");
        
        $lines = explode("\n", trim($content));
        $recentLines = array_slice($lines, -5); // 最後 5 行
        
        foreach ($recentLines as $line) {
            if (!empty($line)) {
                $response['logs'][] = htmlspecialchars($line);
            }
        }
    }
    
    echo json_encode($response);
    exit;
}
?>
