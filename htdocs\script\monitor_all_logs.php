<?php
/**
 * 監控所有相關日誌文件
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入配置
require_once __DIR__ . '/config/environment.php';

// 處理 AJAX 請求
if (isset($_GET['action']) && $_GET['action'] === 'get_all_logs') {
    header('Content-Type: application/json');
    
    $today = date('Y-m-d');
    $logFiles = [
        'auth' => LOG_PATH . '/auth_' . $today . '.log',
        'error' => LOG_PATH . '/error_' . $today . '.log',
        'backup' => LOG_PATH . '/login_backup_' . $today . '.log',
        'debug' => LOG_PATH . '/debug_login_' . $today . '.log',
        'access' => LOG_PATH . '/page_access_' . $today . '.log'
    ];
    
    $response = [
        'timestamp' => date('Y-m-d H:i:s'),
        'logs' => []
    ];
    
    foreach ($logFiles as $type => $file) {
        $logInfo = [
            'type' => $type,
            'file' => $file,
            'exists' => file_exists($file),
            'size' => file_exists($file) ? filesize($file) : 0,
            'modified' => file_exists($file) ? filemtime($file) : 0,
            'recent_lines' => []
        ];
        
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $lines = explode("\n", trim($content));
            $lines = array_filter($lines);
            
            // 取最後 3 行
            $recentLines = array_slice($lines, -3);
            
            foreach ($recentLines as $index => $line) {
                $lineNumber = count($lines) - count($recentLines) + $index + 1;
                
                // 嘗試解析 JSON
                $data = json_decode($line, true);
                if ($data) {
                    $logInfo['recent_lines'][] = [
                        'line_number' => $lineNumber,
                        'timestamp' => $data['timestamp'] ?? 'N/A',
                        'content' => $line,
                        'parsed' => $data
                    ];
                } else {
                    // 非 JSON 格式
                    $logInfo['recent_lines'][] = [
                        'line_number' => $lineNumber,
                        'timestamp' => 'N/A',
                        'content' => $line,
                        'parsed' => null
                    ];
                }
            }
        }
        
        $response['logs'][$type] = $logInfo;
    }
    
    echo json_encode($response);
    exit;
}

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全方位日誌監控</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1400px; margin: 20px auto; padding: 20px; }
        .header { background: #007cba; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .test-panel { background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 2px solid #007cba; }
        .logs-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .log-panel { background: #f8f9fa; border-radius: 8px; padding: 15px; border: 1px solid #ddd; }
        .log-header { background: #007cba; color: white; padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .log-status { display: flex; justify-content: space-between; margin-bottom: 10px; font-size: 12px; }
        .log-line { background: white; margin: 5px 0; padding: 8px; border-radius: 4px; border-left: 3px solid #007cba; font-size: 11px; }
        .log-line-error { border-left-color: #dc3545; }
        .log-line-auth { border-left-color: #28a745; }
        .log-line-backup { border-left-color: #ffc107; }
        .log-line-debug { border-left-color: #17a2b8; }
        .log-line-access { border-left-color: #6f42c1; }
        .btn { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px; }
        .status-exists { background: #28a745; }
        .status-missing { background: #dc3545; }
        .auto-refresh { color: #28a745; font-weight: bold; }
        .highlight { background: #ffeb3b !important; animation: highlight 2s ease-out; }
        @keyframes highlight { from { background: #ffeb3b; } to { background: white; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 全方位日誌監控系統</h1>
        <p>即時監控所有登入相關的日誌文件</p>
    </div>
    
    <div class="test-panel">
        <h3>🧪 測試步驟</h3>
        <ol>
            <li><strong>保持此頁面開啟</strong>（每 2 秒自動更新）</li>
            <li><strong>開啟登入頁面</strong>：<a href="/pages/login.php" target="_blank" class="btn">開啟 pages/login.php</a></li>
            <li><strong>輸入測試資料</strong>：
                <ul>
                    <li>Email: <code>monitor_test_<?= date('His') ?>@example.com</code></li>
                    <li>Password: <code>wrong_password_monitor</code></li>
                </ul>
            </li>
            <li><strong>點擊登入</strong>並觀察下方日誌變化</li>
        </ol>
        
        <p><strong>監控狀態：</strong> <span id="monitor-status" class="auto-refresh">自動更新中</span></p>
        <p><strong>最後更新：</strong> <span id="last-update">載入中...</span></p>
    </div>
    
    <div class="logs-grid" id="logs-container">
        載入中...
    </div>
    
    <script>
        let lastData = {};
        
        function updateLogs() {
            fetch('?action=get_all_logs')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('last-update').textContent = data.timestamp;
                    
                    // 檢查是否有變化
                    let hasChanges = false;
                    for (let type in data.logs) {
                        const currentLog = data.logs[type];
                        const lastLog = lastData[type];
                        
                        if (!lastLog || currentLog.size !== lastLog.size || currentLog.modified !== lastLog.modified) {
                            hasChanges = true;
                            break;
                        }
                    }
                    
                    if (hasChanges) {
                        document.getElementById('monitor-status').textContent = '🆕 檢測到變化！';
                        document.getElementById('monitor-status').style.color = '#dc3545';
                        
                        setTimeout(() => {
                            document.getElementById('monitor-status').textContent = '自動更新中';
                            document.getElementById('monitor-status').style.color = '#28a745';
                        }, 3000);
                    }
                    
                    // 更新顯示
                    const container = document.getElementById('logs-container');
                    let html = '';
                    
                    const logTypes = ['auth', 'error', 'backup', 'debug', 'access'];
                    const logTitles = {
                        'auth': '🔐 認證日誌',
                        'error': '❌ 錯誤日誌',
                        'backup': '💾 備用日誌',
                        'debug': '🐛 調試日誌',
                        'access': '📋 頁面訪問日誌'
                    };
                    
                    logTypes.forEach(type => {
                        const log = data.logs[type];
                        const statusClass = log.exists ? 'status-exists' : 'status-missing';
                        const statusText = log.exists ? '存在' : '不存在';
                        
                        html += `
                            <div class="log-panel">
                                <div class="log-header">
                                    <span class="status-indicator ${statusClass}"></span>
                                    ${logTitles[type]}
                                </div>
                                <div class="log-status">
                                    <span>狀態: ${statusText}</span>
                                    <span>大小: ${log.size} bytes</span>
                                </div>
                                <div class="log-status">
                                    <span>修改: ${log.modified ? new Date(log.modified * 1000).toLocaleString() : 'N/A'}</span>
                                </div>
                        `;
                        
                        if (log.recent_lines.length > 0) {
                            html += '<div style="margin-top: 10px;">';
                            log.recent_lines.forEach(line => {
                                const isNew = hasChanges && lastData[type] && line.line_number > (lastData[type].recent_lines.length || 0);
                                const highlightClass = isNew ? 'highlight' : '';
                                
                                html += `
                                    <div class="log-line log-line-${type} ${highlightClass}">
                                        <strong>第 ${line.line_number} 行:</strong><br>
                                        <small>${line.content}</small>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        } else if (log.exists) {
                            html += '<div style="color: #666; font-style: italic;">文件為空</div>';
                        } else {
                            html += '<div style="color: #999; font-style: italic;">文件不存在</div>';
                        }
                        
                        html += '</div>';
                    });
                    
                    container.innerHTML = html;
                    lastData = data.logs;
                })
                .catch(error => {
                    console.error('更新失敗:', error);
                    document.getElementById('logs-container').innerHTML = '<p style="color: red;">更新失敗: ' + error.message + '</p>';
                });
        }
        
        // 初始載入
        updateLogs();
        
        // 每 2 秒自動更新
        setInterval(updateLogs, 2000);
        
        // 頁面可見性變化時立即更新
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                updateLogs();
            }
        });
    </script>
</body>
</html>
