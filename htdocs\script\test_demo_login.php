<?php
/**
 * 測試演示登入功能
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示登入測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .account-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .account-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .account-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 演示登入功能測試</h1>
        
        <p>這個頁面用來測試演示帳號的登入功能，確保登入頁面的演示按鈕能正常工作。</p>
        
        <?php
        // 演示帳號列表
        $demoAccounts = [
            [
                'email' => '<EMAIL>',
                'password' => 'demo123',
                'description' => '演示帳號',
                'color' => '#28a745'
            ],
            [
                'email' => '<EMAIL>', 
                'password' => 'test123',
                'description' => '測試帳號',
                'color' => '#dc3545'
            ],
            [
                'email' => '<EMAIL>',
                'password' => 'guest123', 
                'description' => '訪客帳號',
                'color' => '#17a2b8'
            ]
        ];
        
        $auth = new Auth();
        
        foreach ($demoAccounts as $account) {
            echo '<div class="account-card">';
            echo '<h3>' . $account['description'] . '</h3>';
            echo '<p><strong>郵箱：</strong> ' . $account['email'] . '</p>';
            echo '<p><strong>密碼：</strong> ' . $account['password'] . '</p>';
            
            // 測試登入
            $result = $auth->login($account['email'], $account['password'], false);
            
            if ($result['success']) {
                echo '<p style="color: green;">✅ <strong>登入測試：成功</strong></p>';
                echo '<p>訊息：' . ($result['message'] ?? '登入成功') . '</p>';
                
                // 登出
                $auth->logout();
                
                echo '<div class="code">';
                echo '// 演示按鈕代碼<br>';
                echo '&lt;button data-email="' . $account['email'] . '" data-password="' . $account['password'] . '"<br>';
                echo '        style="background: ' . $account['color'] . '; color: white;"&gt;<br>';
                echo '    ' . $account['description'] . '<br>';
                echo '&lt;/button&gt;';
                echo '</div>';
                
            } else {
                echo '<p style="color: red;">❌ <strong>登入測試：失敗</strong></p>';
                echo '<p>錯誤：' . ($result['message'] ?? '未知錯誤') . '</p>';
            }
            
            echo '</div>';
        }
        ?>
        
        <div class="container">
            <h2>📋 使用說明</h2>
            
            <h3>1. 登入頁面演示按鈕</h3>
            <p>在登入頁面 (<code>pages/login.php</code>) 中，用戶可以看到以下演示按鈕：</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 15px 0;">
                <button class="btn success">🎯 演示帳號</button>
                <button class="btn danger">🧪 測試帳號</button>
            </div>
            <button class="btn" style="background: #17a2b8; width: 100%;">👥 訪客帳號</button>
            
            <h3>2. 使用方式</h3>
            <ol>
                <li>用戶點擊任一演示按鈕</li>
                <li>系統自動填入對應的郵箱和密碼</li>
                <li>顯示成功提示訊息</li>
                <li>登入按鈕會有脈衝動畫提示</li>
                <li>用戶點擊登入按鈕即可登入</li>
            </ol>
            
            <h3>3. 技術實現</h3>
            <div class="code">
// JavaScript 代碼片段<br>
document.querySelectorAll('.demo-btn').forEach(btn => {<br>
&nbsp;&nbsp;&nbsp;&nbsp;btn.addEventListener('click', function() {<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const email = this.getAttribute('data-email');<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;const password = this.getAttribute('data-password');<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById('email').value = email;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;document.getElementById('password').value = password;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;showMessage('已填入登入資訊', 'success');<br>
&nbsp;&nbsp;&nbsp;&nbsp;});<br>
});
            </div>
        </div>
        
        <div class="container">
            <h2>🔗 相關連結</h2>
            <a href="pages/login.php" class="btn success">🚀 前往登入頁面測試</a>
            <a href="view_login_logs.php" class="btn">📊 查看登入記錄</a>
            <a href="test_database_login_logs.php" class="btn">🧪 AJAX 登入測試</a>
        </div>
    </div>
</body>
</html>
