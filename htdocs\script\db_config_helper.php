<?php
/**
 * 資料庫配置檢測工具
 * 幫助找到正確的資料庫連接參數
 */
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫配置檢測</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .config-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 資料庫配置檢測工具</h1>
        
        <div class="info">
            <strong>當前主機環境：</strong><br>
            主機商：ihostfull.com<br>
            路徑：/home/<USER>/ihostfull.com/uoolo_38699510/attendance.app.tn/htdocs<br>
            PHP 版本：<?= phpversion() ?>
        </div>

        <h2>1. 常見資料庫主機測試</h2>
        <?php
        $hosts_to_test = [
            '127.0.0.1' => 'IP 地址',
            'localhost' => '本地主機',
            'mysql' => 'MySQL 服務',
            'db' => '資料庫服務',
            'mysql.ihostfull.com' => '主機商 MySQL',
            'localhost:3306' => '指定端口'
        ];

        foreach ($hosts_to_test as $host => $description) {
            echo "<div class='config-box'>";
            echo "<strong>測試主機：</strong> $host ($description)<br>";
            
            try {
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", 'root', '', [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 5
                ]);
                echo "<span class='success'>✓ 連接成功</span>";
                
                // 嘗試獲取資料庫列表
                try {
                    $stmt = $pdo->query("SHOW DATABASES");
                    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    echo "<br><strong>可用資料庫：</strong> " . implode(', ', $databases);
                } catch (Exception $e) {
                    echo "<br><span class='warning'>無法列出資料庫</span>";
                }
                
            } catch (PDOException $e) {
                echo "<span class='error'>✗ 連接失敗：" . $e->getMessage() . "</span>";
            }
            echo "</div>";
        }
        ?>

        <h2>2. 主機商資料庫資訊檢查</h2>
        <div class="info">
            <p>請檢查您的主機商控制面板，通常會提供：</p>
            <ul>
                <li><strong>資料庫主機：</strong> 可能是 localhost、127.0.0.1 或特定主機名</li>
                <li><strong>資料庫名稱：</strong> 通常有前綴，如 uoolo_38699510_signattend</li>
                <li><strong>用戶名：</strong> 可能有前綴，如 uoolo_38699510_user</li>
                <li><strong>密碼：</strong> 您設定的密碼</li>
                <li><strong>端口：</strong> 通常是 3306</li>
            </ul>
        </div>

        <h2>3. 建議的配置</h2>
        <div class="config-box">
            <p>根據您的主機環境，建議使用以下配置：</p>
            <pre>
// 在 htdocs/config/config.production.php 中修改：
define('DB_HOST', '127.0.0.1');  // 或 localhost
define('DB_NAME', 'uoolo_38699510_signattend');  // 可能的資料庫名
define('DB_USER', 'uoolo_38699510_user');        // 可能的用戶名
define('DB_PASS', 'your_actual_password');       // 您的實際密碼
            </pre>
        </div>

        <h2>4. 手動測試連接</h2>
        <div class="info">
            <p>如果您知道正確的資料庫資訊，可以在下面測試：</p>
            <form method="post" style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <p>
                    <label>主機：</label>
                    <input type="text" name="test_host" value="<?= $_POST['test_host'] ?? '127.0.0.1' ?>" style="width: 200px; padding: 5px;">
                </p>
                <p>
                    <label>資料庫：</label>
                    <input type="text" name="test_db" value="<?= $_POST['test_db'] ?? 'signattend_prod' ?>" style="width: 200px; padding: 5px;">
                </p>
                <p>
                    <label>用戶：</label>
                    <input type="text" name="test_user" value="<?= $_POST['test_user'] ?? 'root' ?>" style="width: 200px; padding: 5px;">
                </p>
                <p>
                    <label>密碼：</label>
                    <input type="password" name="test_pass" value="<?= $_POST['test_pass'] ?? '' ?>" style="width: 200px; padding: 5px;">
                </p>
                <p>
                    <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">測試連接</button>
                </p>
            </form>

            <?php
            if ($_POST) {
                $host = $_POST['test_host'];
                $db = $_POST['test_db'];
                $user = $_POST['test_user'];
                $pass = $_POST['test_pass'];
                
                echo "<div class='config-box'>";
                echo "<strong>測試結果：</strong><br>";
                
                try {
                    $pdo = new PDO("mysql:host=$host;dbname=$db;charset=utf8mb4", $user, $pass, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    echo "<span class='success'>✓ 連接成功！</span><br>";
                    echo "請將這些參數更新到配置文件中。";
                } catch (PDOException $e) {
                    echo "<span class='error'>✗ 連接失敗：" . $e->getMessage() . "</span>";
                }
                echo "</div>";
            }
            ?>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="test_basic.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">返回基本測試</a>
        </div>
    </div>
</body>
</html>
