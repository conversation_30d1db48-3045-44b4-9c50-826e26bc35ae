<?php
// 測試登入失敗日誌記錄
define('SIGNATTEND_INIT', true);

echo "=== 登入失敗日誌記錄測試 ===\n";

// 啟動 session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 載入配置
require_once 'config/config.php';
require_once 'utils/Auth.php';

echo "LOG_PATH: " . LOG_PATH . "\n\n";

// 創建 Auth 實例
$auth = new Auth();

echo "=== 測試登入失敗情況 ===\n";

// 測試 1: 錯誤密碼
echo "測試 1: 錯誤密碼登入\n";
$result1 = $auth->login('<EMAIL>', 'wrong_password');
echo "結果: " . ($result1['success'] ? '成功' : '失敗') . "\n";
echo "訊息: " . $result1['message'] . "\n\n";

// 測試 2: 不存在的用戶
echo "測試 2: 不存在的用戶登入\n";
$result2 = $auth->login('<EMAIL>', 'any_password');
echo "結果: " . ($result2['success'] ? '成功' : '失敗') . "\n";
echo "訊息: " . $result2['message'] . "\n\n";

// 檢查今天的認證日誌
echo "=== 檢查認證日誌 ===\n";
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "日誌文件: " . $authLogFile . "\n";

if (file_exists($authLogFile)) {
    echo "文件存在，大小: " . filesize($authLogFile) . " bytes\n";
    
    // 讀取最後幾行
    $lines = file($authLogFile, FILE_IGNORE_NEW_LINES);
    $lastLines = array_slice($lines, -5);
    
    echo "\n最後 5 行日誌內容:\n";
    foreach ($lastLines as $i => $line) {
        $data = json_decode($line, true);
        if ($data) {
            echo ($i + 1) . ". [" . $data['timestamp'] . "] " . $data['action'] . "\n";
            echo "   用戶: " . ($data['user_email'] ?? '未知') . "\n";
            echo "   描述: " . $data['description'] . "\n";
            echo "   IP: " . $data['ip_address'] . "\n";
            echo "\n";
        } else {
            echo ($i + 1) . ". " . $line . "\n\n";
        }
    }
} else {
    echo "日誌文件不存在\n";
}

// 檢查錯誤日誌
echo "=== 檢查錯誤日誌 ===\n";
$errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
echo "錯誤日誌文件: " . $errorLogFile . "\n";

if (file_exists($errorLogFile)) {
    echo "文件存在，大小: " . filesize($errorLogFile) . " bytes\n";
    
    // 檢查是否有登入相關的錯誤
    $content = file_get_contents($errorLogFile);
    if (strpos($content, 'Login') !== false || strpos($content, 'login') !== false) {
        echo "發現登入相關錯誤記錄\n";
    } else {
        echo "沒有發現登入相關錯誤記錄\n";
    }
} else {
    echo "錯誤日誌文件不存在\n";
}

echo "\n=== 總結 ===\n";
echo "✅ 已添加登入失敗日誌記錄功能\n";
echo "📁 登入失敗記錄位置: " . LOG_PATH . "/auth_YYYY-MM-DD.log\n";
echo "📝 記錄內容包括:\n";
echo "   - 時間戳記\n";
echo "   - 嘗試登入的 email\n";
echo "   - 登入結果 (login_failed)\n";
echo "   - 失敗原因描述\n";
echo "   - IP 地址\n";
echo "   - 瀏覽器資訊\n";

echo "\n測試完成！\n";
?>
