<?php
// 簡單的登入日誌測試
define('SIGNATTEND_INIT', true);
require_once 'config/config.php';

echo "=== 登入日誌記錄位置檢查 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n\n";

// 檢查現有的日誌文件
$logTypes = ['auth', 'error', 'api'];
$today = date('Y-m-d');

foreach ($logTypes as $type) {
    $logFile = LOG_PATH . '/' . $type . '_' . $today . '.log';
    echo "=== " . strtoupper($type) . " 日誌 ===\n";
    echo "文件路徑: " . $logFile . "\n";
    
    if (file_exists($logFile)) {
        $size = filesize($logFile);
        $mtime = date('Y-m-d H:i:s', filemtime($logFile));
        echo "文件狀態: 存在 (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
        
        // 顯示最後幾行內容
        $lines = file($logFile, FILE_IGNORE_NEW_LINES);
        $lastLines = array_slice($lines, -3);
        echo "最後 3 行內容:\n";
        foreach ($lastLines as $line) {
            echo "  " . $line . "\n";
        }
    } else {
        echo "文件狀態: 不存在\n";
    }
    echo "\n";
}

// 模擬登入失敗日誌
echo "=== 模擬登入失敗日誌 ===\n";

// 認證日誌
$authLogData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_id' => null,
    'user_email' => '<EMAIL>',
    'action' => 'login_failed',
    'description' => '登入失敗：密碼錯誤',
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Test Browser'
];

$authLogFile = LOG_PATH . '/auth_' . $today . '.log';
$authLogMessage = json_encode($authLogData, JSON_UNESCAPED_UNICODE) . "\n";

if (file_put_contents($authLogFile, $authLogMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 認證失敗日誌寫入成功\n";
} else {
    echo "❌ 認證失敗日誌寫入失敗\n";
}

// 錯誤日誌
$errorLogFile = LOG_PATH . '/error_' . $today . '.log';
$errorMessage = "[" . date('Y-m-d H:i:s') . "] Login Error: Invalid password <NAME_EMAIL> from IP " . ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1') . "\n";

if (file_put_contents($errorLogFile, $errorMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 錯誤日誌寫入成功\n";
} else {
    echo "❌ 錯誤日誌寫入失敗\n";
}

echo "\n=== 總結 ===\n";
echo "當使用者輸入錯誤密碼登入時，日誌會記錄在：\n";
echo "1. 📁 " . LOG_PATH . "/auth_YYYY-MM-DD.log - 認證相關日誌\n";
echo "2. 📁 " . LOG_PATH . "/error_YYYY-MM-DD.log - 系統錯誤日誌\n";
echo "3. 📁 " . LOG_PATH . "/api_YYYY-MM-DD.log - API 呼叫日誌 (如果通過 API)\n";

echo "\n這些日誌文件現在都位於安全的外層 logs 目錄中！\n";
?>
