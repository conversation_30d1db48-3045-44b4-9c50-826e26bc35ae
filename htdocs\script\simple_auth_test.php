<?php
define('SIGNATTEND_INIT', true);
require_once 'config/config.php';

echo "測試認證日誌功能\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 模擬登入失敗日誌
$logData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_id' => null,
    'user_email' => '<EMAIL>',
    'action' => 'login_failed',
    'description' => '登入失敗：密碼錯誤',
    'ip_address' => '127.0.0.1',
    'user_agent' => 'Test Browser'
];

$logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';

if (file_put_contents($authLogFile, $logMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 登入失敗日誌寫入成功\n";
    echo "文件: " . $authLogFile . "\n";
} else {
    echo "❌ 登入失敗日誌寫入失敗\n";
}

// 檢查日誌內容
if (file_exists($authLogFile)) {
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    echo "\n日誌文件包含 " . count($lines) . " 行記錄\n";
    echo "最後一行: " . end($lines) . "\n";
}
?>
