<?php
/**
 * AJAX 調試頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄調試信息
$debugLogFile = LOG_PATH . '/ajax_debug_' . date('Y-m-d') . '.log';

function writeDebugLog($message) {
    global $debugLogFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($debugLogFile, $logMessage, FILE_APPEND | LOCK_EX);
}

writeDebugLog("=== AJAX 調試開始 ===");
writeDebugLog("REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'undefined'));
writeDebugLog("POST 數據: " . json_encode($_POST));
writeDebugLog("LOG_PATH: " . LOG_PATH);
writeDebugLog("LOG_ERRORS: " . (LOG_ERRORS ? 'true' : 'false'));

// 處理 AJAX 請求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    writeDebugLog("進入 POST 處理");
    
    $response = [
        'success' => false,
        'message' => '',
        'debug_info' => []
    ];
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    writeDebugLog("Email: $email, Password: " . (!empty($password) ? '[有值]' : '[空]'));
    
    if (empty($email) || empty($password)) {
        $response['message'] = '請輸入電子郵件和密碼';
        writeDebugLog("驗證失敗: 空的 email 或密碼");
    } else {
        // 記錄登入前的認證日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        
        writeDebugLog("認證日誌文件: $authLogFile");
        writeDebugLog("登入前大小: $beforeSize bytes");
        
        // 執行登入
        writeDebugLog("開始執行 auth->login()");
        $result = $auth->login($email, $password, false);
        writeDebugLog("auth->login() 完成，結果: " . json_encode($result));
        
        // 記錄登入後的認證日誌狀態
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        writeDebugLog("登入後大小: $afterSize bytes");
        
        if ($result['success']) {
            $response['success'] = true;
            $response['message'] = '登入成功！';
            writeDebugLog("登入成功");
        } else {
            $response['message'] = $result['message'] ?? '登入失敗';
            writeDebugLog("登入失敗: " . $response['message']);
            
            // 如果認證日誌沒有增長，手動記錄
            if ($afterSize <= $beforeSize) {
                writeDebugLog("認證日誌沒有增長，嘗試手動記錄");
                
                $manualLogData = [
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => null,
                    'user_email' => $email,
                    'action' => 'login_failed_ajax_debug',
                    'description' => 'AJAX 調試登入失敗：' . $response['message'],
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];
                
                $manualLogMessage = json_encode($manualLogData, JSON_UNESCAPED_UNICODE) . "\n";
                
                if (file_put_contents($authLogFile, $manualLogMessage, FILE_APPEND | LOCK_EX)) {
                    writeDebugLog("手動記錄成功");
                } else {
                    writeDebugLog("手動記錄失敗");
                }
                
                // 檢查文件是否真的增長了
                $finalSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
                writeDebugLog("最終大小: $finalSize bytes");
            } else {
                writeDebugLog("認證日誌已自動增長");
            }
        }
        
        $response['debug_info'] = [
            'before_size' => $beforeSize,
            'after_size' => $afterSize,
            'auth_log_file' => $authLogFile,
            'log_path' => LOG_PATH,
            'log_errors' => LOG_ERRORS
        ];
    }
    
    writeDebugLog("返回響應: " . json_encode($response));
    echo json_encode($response);
    exit;
}

writeDebugLog("=== AJAX 調試結束 ===");
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX 調試</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .debug-info { background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 10px 0; font-size: 12px; }
        .result { padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🐛 AJAX 調試工具</h1>
    
    <div class="debug-info">
        <strong>調試信息：</strong><br>
        - LOG_PATH: <?= LOG_PATH ?><br>
        - LOG_ERRORS: <?= LOG_ERRORS ? 'true' : 'false' ?><br>
        - 調試日誌: <?= $debugLogFile ?><br>
        - 認證日誌: <?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log
    </div>
    
    <form id="debugForm">
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="ajax_debug_<?= date('His') ?>@example.com" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" value="wrong_password_debug" required>
        </div>
        
        <button type="submit">測試 AJAX 登入</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('debugForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            console.log('發送 AJAX 請求:', { email, password: '[隱藏]' });
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            fetch('<?= $_SERVER['PHP_SELF'] ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('響應狀態:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('原始響應:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('解析後數據:', data);
                    
                    const resultDiv = document.getElementById('result');
                    resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                    resultDiv.innerHTML = `
                        <strong>結果:</strong> ${data.message}<br>
                        <strong>調試信息:</strong><br>
                        <pre>${JSON.stringify(data.debug_info, null, 2)}</pre>
                    `;
                } catch (e) {
                    console.error('JSON 解析錯誤:', e);
                    document.getElementById('result').innerHTML = `
                        <div class="result error">
                            <strong>錯誤:</strong> JSON 解析失敗<br>
                            <strong>原始響應:</strong> ${text}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('請求錯誤:', error);
                document.getElementById('result').innerHTML = `
                    <div class="result error">
                        <strong>錯誤:</strong> ${error.message}
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
