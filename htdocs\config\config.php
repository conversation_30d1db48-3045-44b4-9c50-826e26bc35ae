<?php
/**
 * SignAttend PHP Frontend 配置檔案
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 防止直接訪問
if (!defined('SIGNATTEND_INIT')) {
    die('Direct access not allowed');
}

// 應用程式基本配置
define('APP_NAME', 'SignAttend');
define('APP_VERSION', '2.0.0');
define('APP_DESCRIPTION', '智慧簽到系統');

// 路徑配置
define('BASE_URL', 'https://attendance.app.tn/');
define('API_BASE_URL', 'https://attendance.app.tn/api');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// 目錄路徑
// ROOT_PATH 指向 htdocs 的上一層目錄（專案根目錄）
// config.php 位於 htdocs/config/，所以需要三層 dirname 才能到達專案根目錄
define('ROOT_PATH', dirname(dirname(dirname(__FILE__))));
// HTDOCS_PATH 指向 htdocs 目錄
define('HTDOCS_PATH', dirname(dirname(__FILE__)));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', HTDOCS_PATH . '/includes');
define('PAGES_PATH', HTDOCS_PATH . '/pages');
define('COMPONENTS_PATH', HTDOCS_PATH . '/components');
define('UTILS_PATH', HTDOCS_PATH . '/utils');
define('UPLOADS_PATH', HTDOCS_PATH . '/uploads');

// 安全配置
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 小時
define('SESSION_NAME', 'SIGNATTEND_SESSION');
define('COOKIE_LIFETIME', 86400); // 24 小時

// 檔案上傳配置
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'xlsx', 'csv']);
define('ALLOWED_MIME_TYPES', [
    'image/jpeg',
    'image/png', 
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
]);

// 分頁配置
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// 快取配置
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 300); // 5 分鐘

// 錯誤處理配置
define('DEBUG_MODE', true); // 生產環境請設為 false
define('LOG_ERRORS', true);
define('LOG_PATH', HTDOCS_PATH . '/logs');

// 資料庫配置 (從後端共用)
$backendDbConfigPath = dirname(dirname(__DIR__)) . '/backend/config/database.php';
if (file_exists($backendDbConfigPath)) {
    $backendDbConfig = require_once $backendDbConfigPath;
    define('DB_HOST', $backendDbConfig['host']);
    define('DB_NAME', $backendDbConfig['database']);
    define('DB_USER', $backendDbConfig['user']);
    define('DB_PASS', $backendDbConfig['password']);
} else {
    // 預設資料庫配置
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'signattend');
    define('DB_USER', 'root');
    define('DB_PASS', '');
}

// 時區設定
date_default_timezone_set('Asia/Taipei');

// 語言設定
define('DEFAULT_LANGUAGE', 'zh-TW');
define('SUPPORTED_LANGUAGES', ['zh-TW', 'en-US']);

// 郵件配置 (如果需要)
define('MAIL_ENABLED', false);
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '');
define('MAIL_PASSWORD', '');
define('MAIL_FROM_EMAIL', '<EMAIL>');
define('MAIL_FROM_NAME', 'SignAttend System');

// QR Code 配置
define('QR_CODE_SIZE', 200);
define('QR_CODE_MARGIN', 2);
define('QR_CODE_ERROR_CORRECTION', 'M'); // L, M, Q, H

// PDF 配置
define('PDF_FONT_SIZE', 12);
define('PDF_MARGIN', 15);
define('PDF_ORIENTATION', 'P'); // P=Portrait, L=Landscape

// API 配置
define('API_TIMEOUT', 30); // 秒
define('API_RETRY_COUNT', 3);

// 前端資源版本 (用於快取控制)
define('ASSETS_VERSION', '2.0.0');

// 功能開關
define('FEATURE_QR_SCANNER', true);
define('FEATURE_SIGNATURE_PAD', true);
define('FEATURE_EMAIL_NOTIFICATIONS', false);
define('FEATURE_BULK_IMPORT', true);
define('FEATURE_PDF_EXPORT', true);
define('FEATURE_EXCEL_EXPORT', true);

// 主題配置
define('DEFAULT_THEME', 'light');
define('AVAILABLE_THEMES', ['light', 'dark']);

// 開發模式配置
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// 建立必要目錄
$requiredDirs = [UPLOADS_PATH, LOG_PATH];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 設定錯誤處理函數
function signattend_error_handler($errno, $errstr, $errfile, $errline) {
    if (LOG_ERRORS) {
        $logMessage = sprintf(
            "[%s] Error %d: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $errno,
            $errstr,
            $errfile,
            $errline
        );
        
        $logFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>";
        echo "<strong>Error {$errno}:</strong> {$errstr}<br>";
        echo "<small>File: {$errfile} | Line: {$errline}</small>";
        echo "</div>";
    }
}

// 設定異常處理函數
function signattend_exception_handler($exception) {
    if (LOG_ERRORS) {
        $logMessage = sprintf(
            "[%s] Uncaught Exception: %s in %s on line %d\n",
            date('Y-m-d H:i:s'),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine()
        );
        
        $logFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<small>File: " . $exception->getFile() . " | Line: " . $exception->getLine() . "</small>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>系統發生錯誤，請稍後再試。</div>";
    }
}

// 註冊錯誤處理函數
set_error_handler('signattend_error_handler');
set_exception_handler('signattend_exception_handler');

// 輔助函數
function asset_url($path) {
    return ASSETS_URL . '/' . ltrim($path, '/') . '?v=' . ASSETS_VERSION;
}

function upload_url($path) {
    return UPLOADS_URL . '/' . ltrim($path, '/');
}

function page_url($path) {
    return BASE_URL . '/' . ltrim($path, '/');
}

function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

function get_client_ip() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
?>
