<?php
// 直接測試 POST /api/meetings 端點

$url = 'http://localhost/SignAttend/backend/api/meetings';
$data = [
    'name' => '測試會議 - ' . date('Y-m-d H:i:s'),
    'description' => '這是一個測試會議',
    'location' => '會議室A',
    'start_date' => '2025-06-20 10:00:00',
    'end_date' => '2025-06-20 12:00:00',
    'created_by' => '9fda4357-f5bf-4ac1-a502-3dddad8dc8d3' // 測試用戶 ID
];

echo "=== 測試 POST /api/meetings ===\n";
echo "URL: $url\n";
echo "Data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_VERBOSE => true,
    CURLOPT_STDERR => fopen('php://output', 'w')
]);

echo "=== cURL 詳細信息 ===\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

echo "\n=== 響應結果 ===\n";
echo "HTTP Code: $httpCode\n";
echo "cURL Error: " . ($error ?: 'None') . "\n";
echo "Response: $response\n";

curl_close($ch);

// 解析響應
if ($response) {
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "\n=== 解析後的響應 ===\n";
        echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
    }
}

// 測試 GET /api/test 確認 API 基本功能
echo "\n=== 測試 GET /api/test ===\n";
$testUrl = 'http://localhost/SignAttend/backend/api/test';
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $testUrl,
    CURLOPT_RETURNTRANSFER => true
]);

$testResponse = curl_exec($ch);
$testHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Test URL: $testUrl\n";
echo "Test HTTP Code: $testHttpCode\n";
echo "Test Response: $testResponse\n";
?>
