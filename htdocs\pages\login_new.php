<?php
/**
 * 登入頁面 - 使用有效的登入邏輯
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄頁面訪問
$accessLogFile = LOG_PATH . '/page_access_' . date('Y-m-d') . '.log';
$accessMessage = sprintf(
    "[%s] NEW LOGIN PAGE ACCESS: Method=%s, Host=%s, IP=%s, UserAgent=%s\n",
    date('Y-m-d H:i:s'),
    $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    $_SERVER['HTTP_HOST'] ?? 'unknown',
    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100)
);
file_put_contents($accessLogFile, $accessMessage, FILE_APPEND | LOCK_EX);

// 處理 JavaScript 調試日誌
if (isset($_POST['debug_js_log'])) {
    $jsLogData = json_decode($_POST['debug_js_log'], true);
    if ($jsLogData) {
        $jsLogMessage = sprintf(
            "[%s] NEW LOGIN JS DEBUG: %s\n",
            date('Y-m-d H:i:s'),
            json_encode($jsLogData, JSON_UNESCAPED_UNICODE)
        );
        file_put_contents($accessLogFile, $jsLogMessage, FILE_APPEND | LOCK_EX);
    }
    // 如果只是調試日誌請求，直接返回
    if (!isset($_POST['email']) && !isset($_POST['password'])) {
        http_response_code(200);
        exit;
    }
}

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    $redirectMessage = sprintf(
        "[%s] NEW LOGIN ALREADY LOGGED IN: Redirecting to dashboard\n",
        date('Y-m-d H:i:s')
    );
    file_put_contents($accessLogFile, $redirectMessage, FILE_APPEND | LOCK_EX);
    
    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理登入表單提交 - 使用簡化測試頁面的有效邏輯
$result = null;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    $rememberMe = isset($_POST['remember_me']);
    
    $loginMessage = sprintf(
        "[%s] NEW LOGIN ATTEMPT: Email=%s, HasPassword=%s, RememberMe=%s\n",
        date('Y-m-d H:i:s'),
        $email,
        !empty($password) ? 'yes' : 'no',
        $rememberMe ? 'yes' : 'no'
    );
    file_put_contents($accessLogFile, $loginMessage, FILE_APPEND | LOCK_EX);
    
    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
        
        $errorMessage = sprintf(
            "[%s] NEW LOGIN VALIDATION ERROR: %s\n",
            date('Y-m-d H:i:s'),
            $error
        );
        file_put_contents($accessLogFile, $errorMessage, FILE_APPEND | LOCK_EX);
    } else {
        // 記錄登入前的認證日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        
        // 執行登入
        $result = $auth->login($email, $password, $rememberMe);
        
        // 記錄登入後的認證日誌狀態
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        
        $resultMessage = sprintf(
            "[%s] NEW LOGIN RESULT: Success=%s, Message=%s, AuthLogBefore=%d, AuthLogAfter=%d\n",
            date('Y-m-d H:i:s'),
            $result['success'] ? 'true' : 'false',
            $result['message'] ?? 'no message',
            $beforeSize,
            $afterSize
        );
        file_put_contents($accessLogFile, $resultMessage, FILE_APPEND | LOCK_EX);
        
        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            
            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);

            // 使用 JavaScript 延遲跳轉以顯示成功訊息
            echo "<script>
                setTimeout(function() {
                    window.location.href = '" . page_url($redirectUrl) . "';
                }, 1500);
            </script>";
        } else {
            $error = $result['message'] ?? '登入失敗';
            
            // 如果認證日誌沒有增長，手動記錄（使用簡化測試頁面的邏輯）
            if ($afterSize <= $beforeSize) {
                $manualLogData = [
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => null,
                    'user_email' => $email,
                    'action' => 'login_failed_manual_new_pages',
                    'description' => '新登入頁面登入失敗：' . $error,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];
                
                $manualLogMessage = json_encode($manualLogData, JSON_UNESCAPED_UNICODE) . "\n";
                file_put_contents($authLogFile, $manualLogMessage, FILE_APPEND | LOCK_EX);
                
                $manualMessage = sprintf(
                    "[%s] NEW LOGIN MANUAL LOG: Written to auth log\n",
                    date('Y-m-d H:i:s')
                );
                file_put_contents($accessLogFile, $manualMessage, FILE_APPEND | LOCK_EX);
            }
        }
    }
}

// 頁面標題
$pageTitle = 'SignAttend - 登入';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/login.css') ?>">
    <link rel="icon" type="image/x-icon" href="<?= asset_url('images/favicon.ico') ?>">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="<?= asset_url('images/logo.png') ?>" alt="SignAttend Logo" class="logo">
                <h1>SignAttend</h1>
                <p>員工簽到系統</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <form id="loginForm" method="POST" action="" class="login-form">
                <?php generate_csrf_token_field(); ?>
                
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        電子郵件
                    </label>
                    <input type="email" id="email" name="email" required 
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                           placeholder="請輸入您的電子郵件">
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        密碼
                    </label>
                    <input type="password" id="password" name="password" required 
                           placeholder="請輸入您的密碼">
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me" id="remember_me">
                        <span class="checkmark"></span>
                        記住我
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                    <span class="btn-text">登入</span>
                    <span class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                        登入中...
                    </span>
                </button>
            </form>

            <div class="login-footer">
                <p><a href="<?= page_url('pages/forgot-password.php') ?>">忘記密碼？</a></p>
                <p class="copyright">© 2024 SignAttend. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script src="<?= asset_url('js/common.js') ?>"></script>
    <script>
        function setLoading(loading) {
            const btn = document.getElementById('loginBtn');
            const btnText = btn.querySelector('.btn-text');
            const btnLoading = btn.querySelector('.btn-loading');
            
            if (loading) {
                btn.disabled = true;
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                btnText.style.display = 'inline-block';
                btnLoading.style.display = 'none';
            }
        }

        // 表單提交時顯示載入狀態
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const formData = {
                email: document.getElementById('email').value,
                password: document.getElementById('password').value ? '[有密碼]' : '[無密碼]',
                method: this.method,
                action: this.action,
                timestamp: new Date().toISOString()
            };
            
            console.log('新登入頁面表單提交:', formData);
            
            // 將調試信息發送到服務器保存
            fetch('<?= $_SERVER['PHP_SELF'] ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'debug_js_log=' + encodeURIComponent(JSON.stringify({
                    action: 'new_login_form_submit_triggered',
                    data: formData,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                }))
            }).catch(err => console.log('Debug log failed:', err));
            
            setLoading(true);
        });

        // 頁面載入完成後重置載入狀態
        window.addEventListener('load', function() {
            setLoading(false);
        });
    </script>
</body>
</html>
