<?php
// 測試 QR Code 生成功能
require 'vendor/autoload.php';

use SignAttend\Services\QRCodeService;

echo "=== 測試 QR Code 生成功能 ===\n";

try {
    $qrCodeService = new QRCodeService();
    
    // 測試會議 QR Code 生成
    echo "\n1. 測試會議 QR Code 生成\n";
    $meetingId = 'e4810622-47af-4988-ad00-20e8ce10b682';
    $meetingQR = $qrCodeService->generateMeetingQRCode($meetingId, '測試會議');
    
    echo "會議 QR Code 生成成功\n";
    echo "URL: " . $meetingQR['qr_code_url'] . "\n";
    echo "MIME Type: " . $meetingQR['mime_type'] . "\n";
    echo "Data Length: " . strlen($meetingQR['qr_code_data']) . " bytes\n";
    
    // 測試參與者 QR Code 生成
    echo "\n2. 測試參與者 QR Code 生成\n";
    $attendeeId = 'fd897ca1-d109-4688-991e-efbcc7222d66';
    $attendeeQR = $qrCodeService->generateAttendeeQRCode($attendeeId, $meetingId, '張三');
    
    echo "參與者 QR Code 生成成功\n";
    echo "URL: " . $attendeeQR['qr_code_url'] . "\n";
    echo "MIME Type: " . $attendeeQR['mime_type'] . "\n";
    echo "Data Length: " . strlen($attendeeQR['qr_code_data']) . " bytes\n";
    
    // 測試儲存 QR Code 到檔案
    echo "\n3. 測試儲存 QR Code 到檔案\n";
    $filename = 'meeting_' . $meetingId . '_qr.png';
    $filepath = $qrCodeService->saveQRCodeToFile($meetingQR['qr_code_data'], $filename);
    
    if ($filepath) {
        echo "QR Code 儲存成功: $filepath\n";
        echo "檔案大小: " . filesize($filepath) . " bytes\n";
    } else {
        echo "QR Code 儲存失敗\n";
    }
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}
?>
