<?php
/**
 * 即時日誌監控器
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入配置
require_once __DIR__ . '/config/environment.php';

// 處理 AJAX 請求
if (isset($_GET['action']) && $_GET['action'] === 'get_logs') {
    header('Content-Type: application/json');
    
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $response = [
        'exists' => file_exists($authLogFile),
        'size' => file_exists($authLogFile) ? filesize($authLogFile) : 0,
        'modified' => file_exists($authLogFile) ? filemtime($authLogFile) : 0,
        'logs' => []
    ];
    
    if (file_exists($authLogFile)) {
        $content = file_get_contents($authLogFile);
        $lines = explode("\n", trim($content));
        $lines = array_filter($lines);
        
        // 取最後 5 筆記錄
        $recentLines = array_slice($lines, -5);
        
        foreach ($recentLines as $line) {
            $data = json_decode($line, true);
            if ($data) {
                $response['logs'][] = [
                    'timestamp' => $data['timestamp'],
                    'email' => $data['user_email'] ?? '未知',
                    'action' => $data['action'],
                    'description' => $data['description'],
                    'ip' => $data['ip_address'],
                    'user_agent' => substr($data['user_agent'], 0, 50) . '...'
                ];
            }
        }
    }
    
    echo json_encode($response);
    exit;
}

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即時日誌監控器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
        .monitor-panel { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status { display: flex; gap: 20px; margin-bottom: 15px; }
        .status-item { background: white; padding: 10px; border-radius: 4px; flex: 1; text-align: center; }
        .log-entry { background: white; margin: 10px 0; padding: 15px; border-radius: 4px; border-left: 4px solid #007cba; }
        .log-failed { border-left-color: #dc3545; }
        .timestamp { font-weight: bold; color: #666; }
        .email { color: #007cba; font-weight: bold; }
        .description { margin: 5px 0; }
        .meta { font-size: 12px; color: #666; }
        .test-panel { background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-danger { background: #dc3545; }
        .auto-refresh { color: #28a745; font-weight: bold; }
        .manual-refresh { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 即時日誌監控器</h1>
    
    <div class="test-panel">
        <h3>🧪 測試步驟</h3>
        <ol>
            <li>保持此頁面開啟（每 3 秒自動更新）</li>
            <li>開啟新分頁：<a href="/pages/login.php" target="_blank" class="btn">開啟登入頁面</a></li>
            <li>在登入頁面輸入任意 email 和錯誤密碼</li>
            <li>點擊登入按鈕</li>
            <li>回到此頁面觀察是否出現新的日誌記錄</li>
        </ol>
        <button class="btn btn-danger" onclick="clearLogs()">清空今日日誌</button>
    </div>
    
    <div class="monitor-panel">
        <h3>📊 監控狀態</h3>
        <div class="status">
            <div class="status-item">
                <div>日誌文件</div>
                <div id="file-status">檢查中...</div>
            </div>
            <div class="status-item">
                <div>文件大小</div>
                <div id="file-size">檢查中...</div>
            </div>
            <div class="status-item">
                <div>最後修改</div>
                <div id="last-modified">檢查中...</div>
            </div>
            <div class="status-item">
                <div>更新狀態</div>
                <div id="refresh-status" class="auto-refresh">自動更新中</div>
            </div>
        </div>
        
        <p><strong>日誌路徑:</strong> <code><?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log</code></p>
        <p><strong>當前時間:</strong> <span id="current-time"><?= date('Y-m-d H:i:s') ?></span></p>
    </div>
    
    <div id="logs-container">
        <h3>📋 最近的認證記錄</h3>
        <div id="logs-list">載入中...</div>
    </div>
    
    <script>
        let autoRefresh = true;
        let lastSize = 0;
        
        function updateLogs() {
            fetch('?action=get_logs')
                .then(response => response.json())
                .then(data => {
                    // 更新狀態
                    document.getElementById('file-status').textContent = data.exists ? '存在' : '不存在';
                    document.getElementById('file-size').textContent = data.size + ' bytes';
                    document.getElementById('last-modified').textContent = data.modified ? new Date(data.modified * 1000).toLocaleString() : '無';
                    document.getElementById('current-time').textContent = new Date().toLocaleString();
                    
                    // 檢查文件是否有變化
                    if (data.size !== lastSize) {
                        document.getElementById('refresh-status').textContent = '檢測到變化！';
                        document.getElementById('refresh-status').className = 'manual-refresh';
                        setTimeout(() => {
                            document.getElementById('refresh-status').textContent = '自動更新中';
                            document.getElementById('refresh-status').className = 'auto-refresh';
                        }, 2000);
                    }
                    lastSize = data.size;
                    
                    // 更新日誌列表
                    const logsList = document.getElementById('logs-list');
                    if (data.logs.length === 0) {
                        logsList.innerHTML = '<p>今日尚無認證記錄</p>';
                    } else {
                        let html = '';
                        data.logs.reverse().forEach(log => {
                            const cssClass = log.action.includes('failed') ? 'log-entry log-failed' : 'log-entry';
                            html += `
                                <div class="${cssClass}">
                                    <div class="timestamp">⏰ ${log.timestamp}</div>
                                    <div class="email">👤 ${log.email}</div>
                                    <div class="description">📝 ${log.description}</div>
                                    <div class="meta">🌐 IP: ${log.ip} | 🔧 動作: ${log.action} | 🖥️ 瀏覽器: ${log.user_agent}</div>
                                </div>
                            `;
                        });
                        logsList.innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('更新失敗:', error);
                    document.getElementById('logs-list').innerHTML = '<p style="color: red;">更新失敗: ' + error.message + '</p>';
                });
        }
        
        function clearLogs() {
            if (confirm('確定要清空今日的認證日誌嗎？')) {
                // 這裡可以添加清空日誌的功能
                alert('此功能需要後端支援，目前僅為演示');
            }
        }
        
        // 初始載入
        updateLogs();
        
        // 每 3 秒自動更新
        setInterval(() => {
            if (autoRefresh) {
                updateLogs();
            }
        }, 3000);
        
        // 頁面可見性變化時暫停/恢復自動更新
        document.addEventListener('visibilitychange', () => {
            autoRefresh = !document.hidden;
            if (autoRefresh) {
                updateLogs();
            }
        });
    </script>
</body>
</html>
