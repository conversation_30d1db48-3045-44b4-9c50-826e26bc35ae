<?php
/**
 * 測試資料庫直接登入功能
 */

define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/../config/environment.php';

echo "=== 測試資料庫直接登入功能 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 載入 Auth 類
    require_once __DIR__ . '/../utils/Auth.php';
    
    // 創建 Auth 實例
    $auth = new Auth();
    
    // 測試帳號
    $testAccount = [
        'email' => '<EMAIL>',
        'password' => 'demo123'
    ];
    
    echo "測試帳號: " . $testAccount['email'] . "\n";
    echo "測試密碼: " . $testAccount['password'] . "\n\n";
    
    // 測試資料庫直接登入
    echo "--- 測試 loginWithDatabase() 方法 ---\n";
    $result = $auth->loginWithDatabase($testAccount['email'], $testAccount['password'], false);
    
    echo "登入結果:\n";
    echo "成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "訊息: " . $result['message'] . "\n";
    
    if ($result['success']) {
        echo "用戶資料: " . json_encode($result['user'], JSON_UNESCAPED_UNICODE) . "\n";
        
        // 檢查 Session
        echo "\n--- Session 檢查 ---\n";
        echo "Session 用戶: " . (isset($_SESSION['user']) ? json_encode($_SESSION['user'], JSON_UNESCAPED_UNICODE) : '未設置') . "\n";
        echo "Session 認證 Token: " . (isset($_SESSION['auth_token']) ? '已設置' : '未設置') . "\n";
        echo "Session 登入時間: " . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : '未設置') . "\n";
        
        // 測試 isLoggedIn() 方法
        echo "\n--- 測試 isLoggedIn() 方法 ---\n";
        $isLoggedIn = $auth->isLoggedIn();
        echo "登入狀態: " . ($isLoggedIn ? '已登入' : '未登入') . "\n";
    }
    
    echo "\n=== 測試完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 測試失敗: " . $e->getMessage() . "\n";
    echo "錯誤詳情: " . $e->getTraceAsString() . "\n";
}
?>
