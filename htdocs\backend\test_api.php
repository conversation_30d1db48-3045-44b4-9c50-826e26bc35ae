<?php
/**
 * API 測試腳本
 * 用於測試 SignAttend API 的基本功能
 */

$baseUrl = 'http://localhost/SignAttend';

function makeRequest($method, $url, $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "=== SignAttend API 測試 ===\n\n";

// 測試 1: 檢查 API 狀態
echo "1. 測試 API 狀態...\n";
$response = makeRequest('GET', $baseUrl . '/api/test');
echo "狀態碼: " . $response['status_code'] . "\n";
echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";

// 測試 2: 註冊使用者
echo "2. 測試使用者註冊...\n";
$userData = [
    'name' => '測試使用者',
    'email' => '<EMAIL>',
    'password' => 'password123'
];
$response = makeRequest('POST', $baseUrl . '/api/register', $userData);
echo "狀態碼: " . $response['status_code'] . "\n";
echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";

// 測試 3: 使用者登入
echo "3. 測試使用者登入...\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];
$response = makeRequest('POST', $baseUrl . '/api/login', $loginData);
echo "狀態碼: " . $response['status_code'] . "\n";
echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";

// 儲存使用者 ID 用於後續測試
$userId = $response['body']['data']['user']['id'] ?? null;

if ($userId) {
    // 測試 4: 建立會議
    echo "4. 測試建立會議...\n";
    $meetingData = [
        'name' => '測試會議',
        'description' => '這是一個測試會議',
        'start_date' => '2025-06-20 10:00:00',
        'end_date' => '2025-06-20 12:00:00',
        'location' => '會議室 A',
        'created_by' => $userId
    ];
    $response = makeRequest('POST', $baseUrl . '/api/meetings', $meetingData);
    echo "狀態碼: " . $response['status_code'] . "\n";
    echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";
    
    $meetingId = $response['body']['data']['id'] ?? null;
    
    if ($meetingId) {
        // 測試 5: 取得會議列表
        echo "5. 測試取得會議列表...\n";
        $response = makeRequest('GET', $baseUrl . '/api/meetings');
        echo "狀態碼: " . $response['status_code'] . "\n";
        echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";
        
        // 測試 6: 新增參與者
        echo "6. 測試新增參與者...\n";
        $attendeeData = [
            'name' => '測試參與者',
            'email' => '<EMAIL>',
            'phone' => '0912345678',
            'organization' => '測試公司',
            'position' => '測試職位'
        ];
        $response = makeRequest('POST', $baseUrl . '/api/meetings/' . $meetingId . '/attendees', $attendeeData);
        echo "狀態碼: " . $response['status_code'] . "\n";
        echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";
        
        $attendeeId = $response['body']['data']['id'] ?? null;
        
        if ($attendeeId) {
            // 測試 7: 取得會議參與者
            echo "7. 測試取得會議參與者...\n";
            $response = makeRequest('GET', $baseUrl . '/api/meetings/' . $meetingId . '/attendees');
            echo "狀態碼: " . $response['status_code'] . "\n";
            echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";
            
            // 測試 8: 參與者簽到
            echo "8. 測試參與者簽到...\n";
            $checkinData = [
                'signature_data' => 'base64_encoded_signature_data'
            ];
            $response = makeRequest('POST', $baseUrl . '/api/attendees/' . $attendeeId . '/checkin', $checkinData);
            echo "狀態碼: " . $response['status_code'] . "\n";
            echo "回應: " . json_encode($response['body'], JSON_UNESCAPED_UNICODE) . "\n\n";
        }
    }
}

echo "=== API 測試完成 ===\n";
?>
