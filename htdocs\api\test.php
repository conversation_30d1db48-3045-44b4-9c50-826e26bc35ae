<?php
/**
 * API 測試端點
 * 用於檢查 API 連接狀態
 */

// 設定 JSON 回應標頭
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置
require_once __DIR__ . '/../config/environment.php';

try {
    // 基本狀態檢查
    $status = [
        'success' => true,
        'message' => 'API 連接正常',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => APP_VERSION,
        'environment' => APP_ENVIRONMENT
    ];
    
    // 檢查資料庫連接
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $status['database'] = 'connected';
    } catch (PDOException $e) {
        $status['database'] = 'disconnected';
        $status['database_error'] = $e->getMessage();
    }
    
    // 檢查系統狀態
    $status['system'] = [
        'php_version' => phpversion(),
        'memory_usage' => memory_get_usage(true),
        'server_time' => date('Y-m-d H:i:s')
    ];
    
    http_response_code(200);
    echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'API 錯誤',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
