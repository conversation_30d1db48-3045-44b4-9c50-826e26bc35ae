<?php
/**
 * 修復時區問題：將 TIMESTAMP 欄位改為 DATETIME
 */

$dbConfig = require __DIR__ . '/../config/database.php';

try {
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "正在修復時區問題...\n";
    
    // 1. 修改 users 表
    echo "修改 users 表的時間欄位...\n";
    
    $alterUsers = "
        ALTER TABLE users 
        MODIFY COLUMN created_at DATETIME NOT NULL,
        MODIFY COLUMN updated_at DATETIME NOT NULL
    ";
    
    $pdo->exec($alterUsers);
    echo "✓ users 表修改完成\n";
    
    // 2. 修改 profiles 表
    echo "修改 profiles 表的時間欄位...\n";
    
    $alterProfiles = "
        ALTER TABLE profiles 
        MODIFY COLUMN created_at DATETIME NOT NULL,
        MODIFY COLUMN updated_at DATETIME NOT NULL
    ";
    
    $pdo->exec($alterProfiles);
    echo "✓ profiles 表修改完成\n";
    
    // 3. 驗證修改結果
    echo "\n驗證修改結果...\n";
    
    $tables = ['users', 'profiles'];
    foreach ($tables as $table) {
        echo "\n=== {$table} 表結構 ===\n";
        $stmt = $pdo->query("DESCRIBE {$table}");
        while ($row = $stmt->fetch()) {
            if (in_array($row['Field'], ['created_at', 'updated_at'])) {
                echo sprintf("%-15s %-20s\n", $row['Field'], $row['Type']);
            }
        }
    }
    
    echo "\n✓ 時區問題修復完成！\n";
    echo "現在 created_at 和 updated_at 欄位使用 DATETIME 類型，不會自動轉換時區。\n";
    
} catch (PDOException $e) {
    echo "❌ 修復失敗: " . $e->getMessage() . "\n";
    exit(1);
}
?>
