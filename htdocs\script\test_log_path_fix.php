<?php
/**
 * 測試日誌路徑修復
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入配置檔案
require_once __DIR__ . '/config/config.php';

echo "=== 測試日誌路徑修復 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

echo "=== 路徑配置檢查 ===\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "HTDOCS_PATH: " . HTDOCS_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 檢查路徑是否在允許的範圍內
echo "\n=== 路徑安全檢查 ===\n";
echo "LOG_PATH 是否在 HTDOCS_PATH 內: " . (strpos(LOG_PATH, HTDOCS_PATH) === 0 ? '✅ 是' : '❌ 否') . "\n";

// 檢查目錄是否存在
echo "\n=== 目錄狀態檢查 ===\n";
echo "LOG_PATH 目錄存在: " . (is_dir(LOG_PATH) ? '✅ 是' : '❌ 否') . "\n";
echo "LOG_PATH 可寫入: " . (is_writable(LOG_PATH) ? '✅ 是' : '❌ 否') . "\n";

// 測試日誌寫入
echo "\n=== 測試日誌寫入 ===\n";
$testLogFile = LOG_PATH . '/test_' . date('Y-m-d') . '.log';
$testMessage = "[" . date('Y-m-d H:i:s') . "] 測試日誌寫入功能\n";

try {
    $result = file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX);
    if ($result !== false) {
        echo "✅ 日誌寫入成功\n";
        echo "寫入檔案: " . $testLogFile . "\n";
        echo "寫入字節數: " . $result . "\n";
    } else {
        echo "❌ 日誌寫入失敗\n";
    }
} catch (Exception $e) {
    echo "❌ 日誌寫入異常: " . $e->getMessage() . "\n";
}

// 檢查 .htaccess 保護
echo "\n=== 安全保護檢查 ===\n";
$htaccessFile = LOG_PATH . '/.htaccess';
echo ".htaccess 檔案存在: " . (file_exists($htaccessFile) ? '✅ 是' : '❌ 否') . "\n";

if (file_exists($htaccessFile)) {
    $htaccessContent = file_get_contents($htaccessFile);
    echo "包含拒絕訪問規則: " . (strpos($htaccessContent, 'Deny from all') !== false ? '✅ 是' : '❌ 否') . "\n";
}

// 測試各種日誌功能
echo "\n=== 測試各種日誌功能 ===\n";

// 測試錯誤日誌
try {
    $errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
    $errorMessage = "[" . date('Y-m-d H:i:s') . "] 測試錯誤日誌\n";
    file_put_contents($errorLogFile, $errorMessage, FILE_APPEND | LOCK_EX);
    echo "✅ 錯誤日誌寫入成功\n";
} catch (Exception $e) {
    echo "❌ 錯誤日誌寫入失敗: " . $e->getMessage() . "\n";
}

// 測試 API 日誌
try {
    $apiLogFile = LOG_PATH . '/api_' . date('Y-m-d') . '.log';
    $apiMessage = "[" . date('Y-m-d H:i:s') . "] 測試 API 日誌\n";
    file_put_contents($apiLogFile, $apiMessage, FILE_APPEND | LOCK_EX);
    echo "✅ API 日誌寫入成功\n";
} catch (Exception $e) {
    echo "❌ API 日誌寫入失敗: " . $e->getMessage() . "\n";
}

// 測試認證日誌
try {
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $authMessage = "[" . date('Y-m-d H:i:s') . "] 測試認證日誌\n";
    file_put_contents($authLogFile, $authMessage, FILE_APPEND | LOCK_EX);
    echo "✅ 認證日誌寫入成功\n";
} catch (Exception $e) {
    echo "❌ 認證日誌寫入失敗: " . $e->getMessage() . "\n";
}

// 檢查現有日誌檔案
echo "\n=== 現有日誌檔案 ===\n";
if (is_dir(LOG_PATH)) {
    $files = scandir(LOG_PATH);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    
    echo "日誌檔案數量: " . count($logFiles) . "\n";
    
    if (!empty($logFiles)) {
        echo "最近的日誌檔案:\n";
        // 按修改時間排序
        usort($logFiles, function($a, $b) {
            return filemtime(LOG_PATH . '/' . $b) - filemtime(LOG_PATH . '/' . $a);
        });
        
        foreach (array_slice($logFiles, 0, 5) as $file) {
            $filePath = LOG_PATH . '/' . $file;
            $size = filesize($filePath);
            $mtime = date('Y-m-d H:i:s', filemtime($filePath));
            echo "  📄 " . $file . " (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
        }
    }
}

echo "\n=== 忘記密碼功能測試建議 ===\n";
echo "1. 確認 LOG_PATH 現在指向 htdocs/logs\n";
echo "2. 確認目錄可寫入且受 .htaccess 保護\n";
echo "3. 測試忘記密碼頁面是否正常工作\n";
echo "4. 檢查是否還有其他 open_basedir 相關錯誤\n";

echo "\n=== 修復總結 ===\n";
if (strpos(LOG_PATH, HTDOCS_PATH) === 0 && is_writable(LOG_PATH)) {
    echo "🎉 日誌路徑修復成功！\n";
    echo "✅ LOG_PATH 現在位於允許的目錄內\n";
    echo "✅ 目錄可寫入，日誌功能正常\n";
    echo "✅ .htaccess 保護已設置\n";
    echo "✅ 忘記密碼功能應該可以正常工作\n";
} else {
    echo "❌ 仍有問題需要解決\n";
}

echo "\n=== 測試完成 ===\n";
?>
