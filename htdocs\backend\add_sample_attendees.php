<?php
/**
 * 添加示例參與者資料
 */

// 載入配置
$config = require_once __DIR__ . '/config/database.php';

// 資料庫連接
try {
    $pdo = new PDO(
        "mysql:host=" . $config['host'] . ";dbname=" . $config['database'] . ";charset=utf8mb4",
        $config['user'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die('資料庫連接失敗: ' . $e->getMessage());
}

// 獲取所有會議
$stmt = $pdo->query("SELECT id, name FROM meetings ORDER BY created_at DESC");
$meetings = $stmt->fetchAll();

if (empty($meetings)) {
    echo "沒有找到任何會議，請先創建會議。\n";
    exit;
}

echo "找到 " . count($meetings) . " 個會議：\n";
foreach ($meetings as $meeting) {
    echo "- {$meeting['name']} (ID: {$meeting['id']})\n";
}

// 示例參與者資料
$attendeeTemplates = [
    [
        'name' => '謝倩如',
        'email' => '<EMAIL>',
        'organization' => '新閣公司',
        'checked_in' => false,
        'invitation_sent' => true
    ],
    [
        'name' => '林峰旭',
        'email' => '<EMAIL>',
        'organization' => '食品所',
        'checked_in' => true,
        'invitation_sent' => true
    ],
    [
        'name' => '郭曉婷',
        'email' => '<EMAIL>',
        'organization' => '食品所',
        'checked_in' => false,
        'invitation_sent' => true
    ],
    [
        'name' => '楊志凱',
        'email' => '<EMAIL>',
        'organization' => '食品所',
        'checked_in' => false,
        'invitation_sent' => true
    ],
    [
        'name' => '王小明',
        'email' => '<EMAIL>',
        'organization' => '科技公司',
        'checked_in' => true,
        'invitation_sent' => true
    ],
    [
        'name' => '李美華',
        'email' => '<EMAIL>',
        'organization' => '設計工作室',
        'checked_in' => false,
        'invitation_sent' => false
    ]
];

// 為每個會議添加參與者
foreach ($meetings as $meeting) {
    echo "\n為會議「{$meeting['name']}」添加參與者...\n";
    
    // 檢查是否已有參與者
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendees WHERE meeting_id = ?");
    $stmt->execute([$meeting['id']]);
    $existingCount = $stmt->fetchColumn();
    
    if ($existingCount > 0) {
        echo "  已有 {$existingCount} 位參與者，跳過\n";
        continue;
    }
    
    // 為每個會議隨機選擇 2-4 位參與者
    $meetingHash = crc32($meeting['id']);
    $attendeeCount = 2 + (abs($meetingHash) % 3); // 2-4 位參與者
    $selectedAttendees = array_slice($attendeeTemplates, 0, $attendeeCount);
    
    foreach ($selectedAttendees as $attendee) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO attendees (meeting_id, name, email, organization, checked_in, invitation_sent, invitation_sent_at, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $invitationDate = $attendee['invitation_sent'] ? date('Y-m-d H:i:s', strtotime('-' . rand(1, 7) . ' days')) : null;
            
            $stmt->execute([
                $meeting['id'],
                $attendee['name'],
                $attendee['email'],
                $attendee['organization'],
                $attendee['checked_in'] ? 1 : 0,
                $attendee['invitation_sent'] ? 1 : 0,
                $invitationDate
            ]);
            
            echo "  ✓ 添加參與者：{$attendee['name']} ({$attendee['email']})\n";
            
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) {
                echo "  - 跳過重複參與者：{$attendee['name']} ({$attendee['email']})\n";
            } else {
                echo "  ✗ 添加失敗：{$attendee['name']} - " . $e->getMessage() . "\n";
            }
        }
    }
}

echo "\n示例參與者資料添加完成！\n";
?>
