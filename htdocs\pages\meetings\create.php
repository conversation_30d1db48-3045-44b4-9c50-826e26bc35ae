<?php
/**
 * SignAttend PHP Frontend - 新增會議頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入配置檔案
require_once __DIR__ . '/../../config/config.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 要求使用者登入
$auth->requireAuth();

// 取得使用者資訊
$user = $auth->getCurrentUser();
$profile = $auth->getCurrentProfile();

// 處理表單提交
$error = '';
$success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $start_date = $_POST['start_date'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_date = $_POST['end_date'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $max_attendees = intval($_POST['max_attendees'] ?? 0);

    // 基本驗證
    if (empty($name)) {
        $error = '請輸入會議名稱';
    } elseif (empty($start_date) || empty($start_time)) {
        $error = '請選擇會議開始時間';
    } else {
        // 組合日期時間
        $start_datetime = $start_date . ' ' . $start_time;
        $end_datetime = '';
        if (!empty($end_date) && !empty($end_time)) {
            $end_datetime = $end_date . ' ' . $end_time;

            // 驗證結束時間晚於開始時間
            if (strtotime($end_datetime) <= strtotime($start_datetime)) {
                $error = '結束時間必須晚於開始時間';
            }
        }

        // 驗證開始時間不能是過去時間
        if (empty($error) && strtotime($start_datetime) < time()) {
            $error = '會議開始時間不能是過去時間';
        }

        try {
            // 調用 API 創建會議
            $requestData = [
                'name' => $name,
                'description' => $description,
                'location' => $location,
                'start_date' => $start_datetime,
                'end_date' => $end_datetime,
                'max_attendees' => $max_attendees,
                'created_by' => $user['id']
            ];

            // 調試信息
            error_log('Creating meeting with data: ' . json_encode($requestData));
            error_log('User ID: ' . ($user['id'] ?? 'NULL'));

            // 直接調用簡化的 API（繞過 ApiClient 的 baseUrl）
            $url = 'http://localhost/SignAttend/backend/simple_meeting_api.php';
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($requestData),
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30
            ]);

            $apiResponse = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                throw new Exception('cURL Error: ' . $curlError);
            }

            $response = [
                'success' => $httpCode < 400,
                'data' => json_decode($apiResponse, true),
                'http_code' => $httpCode
            ];

            if (!$response['success']) {
                $response['error'] = $response['data']['message'] ?? 'HTTP Error ' . $httpCode;
            }

            // 調試響應
            error_log('API Response: ' . json_encode($response));

            if ($response['success']) {
                $success = '會議創建成功！會議ID：' . ($response['data']['data']['id'] ?? 'N/A');
                // 可以重定向到會議詳情頁面
                // header('Location: ' . page_url('pages/meetings/view.php?id=' . $response['data']['data']['id']));
                // exit;
            } else {
                $error = '創建會議失敗：' . ($response['error'] ?? $response['message'] ?? '未知錯誤');
                $error .= ' (HTTP Code: ' . ($response['http_code'] ?? 'N/A') . ')';
            }
        } catch (Exception $e) {
            $error = '創建會議時發生錯誤：' . $e->getMessage();
            error_log('Meeting creation exception: ' . $e->getMessage());
        }
    }
}

// 頁面標題
$pageTitle = '新增會議';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - 會議報到管理系統</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gray-50">

<!-- Header -->
<header class="border-b bg-white shadow-sm">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center max-w-7xl">
        <h1 class="text-xl font-semibold">會議報到管理系統</h1>
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2 text-sm text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span>歡迎，<?= htmlspecialchars($user['email'] ?? '<EMAIL>') ?></span>
            </div>
            <a href="<?= page_url('pages/logout.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" x2="9" y1="12" y2="12"></line>
                </svg>
                登出
            </a>
        </div>
    </div>
</header>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?= page_url('pages/dashboard.php') ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                    儀表板
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">新增會議</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Page Title -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight">新增會議</h1>
        <p class="text-gray-600 mt-1">創建新的會議並設定相關資訊</p>
    </div>

    <!-- Error/Success Messages -->
    <?php if (!empty($error)): ?>
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <p class="text-sm text-red-800"><?= htmlspecialchars($error) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex">
                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <p class="text-sm text-green-800"><?= htmlspecialchars($success) ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Form -->
    <div class="bg-white rounded-lg border shadow-sm">
        <div class="p-6">
            <form method="POST" action="" class="space-y-6">
                <?php generate_csrf_token_field(); ?>

                <!-- 會議名稱 -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">會議名稱 *</label>
                    <input type="text" id="name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['name'] ?? '') ?>"
                           placeholder="請輸入會議名稱">
                </div>

                <!-- 會議描述 -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">會議描述</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="請輸入會議描述"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                </div>

                <!-- 會議地點 -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-2">會議地點</label>
                    <input type="text" id="location" name="location"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['location'] ?? '') ?>"
                           placeholder="請輸入會議地點">
                </div>

                <!-- 開始時間 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">開始日期 *</label>
                        <input type="date" id="start_date" name="start_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['start_date'] ?? '') ?>">
                        <!-- Tooltip for start date -->
                        <div id="start-date-tooltip" class="absolute z-10 invisible opacity-0 transition-opacity duration-200 bg-red-600 text-white text-xs rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
                            開始時間不能是過去時間
                            <div class="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-600"></div>
                        </div>
                    </div>
                    <div class="relative">
                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">開始時間 *</label>
                        <input type="time" id="start_time" name="start_time" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['start_time'] ?? '') ?>">
                        <!-- Tooltip for start time -->
                        <div id="start-time-tooltip" class="absolute z-10 invisible opacity-0 transition-opacity duration-200 bg-red-600 text-white text-xs rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
                            開始時間不能是過去時間
                            <div class="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-600"></div>
                        </div>
                    </div>
                </div>

                <!-- 結束時間 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                        <input type="date" id="end_date" name="end_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['end_date'] ?? '') ?>">
                        <!-- Tooltip for end date -->
                        <div id="end-date-tooltip" class="absolute z-10 invisible opacity-0 transition-opacity duration-200 bg-red-600 text-white text-xs rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
                            結束日期必須晚於開始日期
                            <div class="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-600"></div>
                        </div>
                    </div>
                    <div class="relative">
                        <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">結束時間</label>
                        <input type="time" id="end_time" name="end_time"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               value="<?= htmlspecialchars($_POST['end_time'] ?? '') ?>">
                        <!-- Tooltip for end time -->
                        <div id="end-time-tooltip" class="absolute z-10 invisible opacity-0 transition-opacity duration-200 bg-red-600 text-white text-xs rounded py-1 px-2 -top-8 left-0 whitespace-nowrap">
                            結束時間必須晚於開始時間
                            <div class="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-600"></div>
                        </div>
                    </div>
                </div>

                <!-- 最大參與者數 -->
                <div>
                    <label for="max_attendees" class="block text-sm font-medium text-gray-700 mb-2">最大參與者數</label>
                    <input type="number" id="max_attendees" name="max_attendees" min="1" max="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           value="<?= htmlspecialchars($_POST['max_attendees'] ?? '') ?>"
                           placeholder="請輸入最大參與者數（可選）">
                </div>

                <!-- 提交按鈕 -->
                <div class="flex gap-4 pt-4">
                    <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 rounded-md px-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                            <path d="M5 12h14"></path>
                            <path d="M12 5v14"></path>
                        </svg>
                        創建會議
                    </button>
                    <a href="<?= page_url('pages/dashboard.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md px-6 no-underline">
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const startDateInput = document.getElementById('start_date');
    const startTimeInput = document.getElementById('start_time');
    const endDateInput = document.getElementById('end_date');
    const endTimeInput = document.getElementById('end_time');

    // 設定最小日期為今天
    const today = new Date().toISOString().split('T')[0];
    startDateInput.min = today;
    endDateInput.min = today;

    // 顯示和隱藏 tooltip 的函數
    function showTooltip(tooltipId) {
        const tooltip = document.getElementById(tooltipId);
        if (tooltip) {
            tooltip.classList.remove('invisible', 'opacity-0');
            tooltip.classList.add('visible', 'opacity-100');
        }
    }

    function hideTooltip(tooltipId) {
        const tooltip = document.getElementById(tooltipId);
        if (tooltip) {
            tooltip.classList.remove('visible', 'opacity-100');
            tooltip.classList.add('invisible', 'opacity-0');
        }
    }

    // 即時驗證函數
    function validateDateTime() {
        const startDate = startDateInput.value;
        const startTime = startTimeInput.value;
        const endDate = endDateInput.value;
        const endTime = endTimeInput.value;

        // 清除之前的錯誤樣式和 tooltip
        endDateInput.classList.remove('border-red-500');
        endTimeInput.classList.remove('border-red-500');
        hideTooltip('end-date-tooltip');
        hideTooltip('end-time-tooltip');

        if (startDate && startTime && endDate && endTime) {
            const startDateTime = new Date(startDate + 'T' + startTime);
            const endDateTime = new Date(endDate + 'T' + endTime);

            if (endDateTime <= startDateTime) {
                endDateInput.classList.add('border-red-500');
                endTimeInput.classList.add('border-red-500');
                showTooltip('end-date-tooltip');
                showTooltip('end-time-tooltip');
                showError('結束時間必須晚於開始時間');
                return false;
            } else {
                hideError();
                return true;
            }
        }
        return true;
    }

    function validateStartDateTime() {
        const startDate = startDateInput.value;
        const startTime = startTimeInput.value;

        // 清除之前的錯誤樣式和 tooltip
        startDateInput.classList.remove('border-red-500');
        startTimeInput.classList.remove('border-red-500');
        hideTooltip('start-date-tooltip');
        hideTooltip('start-time-tooltip');

        if (startDate && startTime) {
            const startDateTime = new Date(startDate + 'T' + startTime);
            const now = new Date();

            if (startDateTime < now) {
                startDateInput.classList.add('border-red-500');
                startTimeInput.classList.add('border-red-500');
                showTooltip('start-date-tooltip');
                showTooltip('start-time-tooltip');
                showError('會議開始時間不能是過去時間');
                return false;
            } else {
                hideError();
                return true;
            }
        }
        return true;
    }

    function showError(message) {
        let errorDiv = document.getElementById('js-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'js-error';
            errorDiv.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-800';
            form.insertBefore(errorDiv, form.firstChild);
        }
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    function hideError() {
        const errorDiv = document.getElementById('js-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // 綁定事件監聽器
    startDateInput.addEventListener('change', function() {
        validateStartDateTime();
        validateDateTime();
    });

    startTimeInput.addEventListener('change', function() {
        validateStartDateTime();
        validateDateTime();
    });

    endDateInput.addEventListener('change', validateDateTime);
    endTimeInput.addEventListener('change', validateDateTime);

    // 添加滑鼠懸停事件來顯示 tooltip（當有錯誤時）
    startDateInput.addEventListener('mouseenter', function() {
        if (this.classList.contains('border-red-500')) {
            showTooltip('start-date-tooltip');
        }
    });

    startDateInput.addEventListener('mouseleave', function() {
        setTimeout(() => hideTooltip('start-date-tooltip'), 100);
    });

    startTimeInput.addEventListener('mouseenter', function() {
        if (this.classList.contains('border-red-500')) {
            showTooltip('start-time-tooltip');
        }
    });

    startTimeInput.addEventListener('mouseleave', function() {
        setTimeout(() => hideTooltip('start-time-tooltip'), 100);
    });

    endDateInput.addEventListener('mouseenter', function() {
        if (this.classList.contains('border-red-500')) {
            showTooltip('end-date-tooltip');
        }
    });

    endDateInput.addEventListener('mouseleave', function() {
        setTimeout(() => hideTooltip('end-date-tooltip'), 100);
    });

    endTimeInput.addEventListener('mouseenter', function() {
        if (this.classList.contains('border-red-500')) {
            showTooltip('end-time-tooltip');
        }
    });

    endTimeInput.addEventListener('mouseleave', function() {
        setTimeout(() => hideTooltip('end-time-tooltip'), 100);
    });

    // 表單提交驗證
    form.addEventListener('submit', function(e) {
        const isStartValid = validateStartDateTime();
        const isEndValid = validateDateTime();

        if (!isStartValid || !isEndValid) {
            e.preventDefault();
            return false;
        }
    });

    // 會議名稱重複檢查（可選，需要 AJAX）
    let nameCheckTimeout;
    nameInput.addEventListener('input', function() {
        clearTimeout(nameCheckTimeout);
        nameCheckTimeout = setTimeout(function() {
            // 這裡可以添加 AJAX 檢查會議名稱是否重複
            // 暫時跳過，因為後端已經有驗證
        }, 500);
    });
});
</script>

</body>
</html>
