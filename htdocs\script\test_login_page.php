<?php
/**
 * 測試登入頁面功能
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 處理登入表單提交
$error = '';
$success = '';
$logInfo = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
    } else {
        $result = $auth->login($email, $password, $rememberMe);

        if ($result['success']) {
            $success = '登入成功！';
        } else {
            $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
        }
        
        // 檢查日誌記錄
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $lines = explode("\n", trim($content));
            $lastLine = end($lines);
            
            if (!empty($lastLine)) {
                $data = json_decode($lastLine, true);
                if ($data && $data['user_email'] === $email) {
                    $logInfo = "✅ 日誌記錄成功 - 動作: " . $data['action'] . " 時間: " . $data['timestamp'];
                } else {
                    $logInfo = "❌ 沒有找到對應的日誌記錄";
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入測試頁面</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 500px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="email"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .error { color: red; margin: 10px 0; }
        .success { color: green; margin: 10px 0; }
        .log-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>登入測試頁面</h1>
    
    <?php if ($error): ?>
        <div class="error"><?= htmlspecialchars($error) ?></div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="success"><?= htmlspecialchars($success) ?></div>
    <?php endif; ?>
    
    <?php if ($logInfo): ?>
        <div class="log-info"><?= htmlspecialchars($logInfo) ?></div>
    <?php endif; ?>
    
    <form method="POST">
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="remember_me"> 記住我
            </label>
        </div>
        
        <button type="submit">登入</button>
    </form>
    
    <hr>
    <h3>測試說明</h3>
    <p>請嘗試使用錯誤的密碼登入，系統會記錄登入失敗的日誌。</p>
    <p>日誌位置: <code><?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log</code></p>
    
    <h3>最近的認證日誌</h3>
    <?php
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    if (file_exists($authLogFile)) {
        $content = file_get_contents($authLogFile);
        $lines = explode("\n", trim($content));
        $recentLines = array_slice($lines, -3);
        
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;'>";
        foreach ($recentLines as $line) {
            if (!empty($line)) {
                $data = json_decode($line, true);
                if ($data) {
                    echo "[" . $data['timestamp'] . "] " . $data['action'] . " - " . $data['user_email'] . "\n";
                    echo "  描述: " . $data['description'] . "\n";
                    echo "  IP: " . $data['ip_address'] . "\n\n";
                }
            }
        }
        echo "</pre>";
    } else {
        echo "<p>今日尚無認證日誌</p>";
    }
    ?>
</body>
</html>
