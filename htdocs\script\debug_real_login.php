<?php
/**
 * 調試真實登入請求
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄所有請求資訊到調試日誌
$debugLogFile = LOG_PATH . '/debug_login_' . date('Y-m-d') . '.log';

function writeDebugLog($message) {
    global $debugLogFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($debugLogFile, $logMessage, FILE_APPEND | LOCK_EX);
}

writeDebugLog("=== 新的登入請求開始 ===");
writeDebugLog("REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'undefined'));
writeDebugLog("HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'undefined'));
writeDebugLog("REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'undefined'));
writeDebugLog("USER_AGENT: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'undefined'));
writeDebugLog("REMOTE_ADDR: " . ($_SERVER['REMOTE_ADDR'] ?? 'undefined'));

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    writeDebugLog("用戶已登入，重定向到儀表板");
    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理登入表單提交
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    writeDebugLog("檢測到 POST 請求");
    
    // 記錄 POST 資料
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    writeDebugLog("POST 資料 - Email: $email");
    writeDebugLog("POST 資料 - Password: " . (empty($password) ? '空' : '[有值]'));
    writeDebugLog("POST 資料 - Remember Me: " . ($rememberMe ? 'true' : 'false'));
    writeDebugLog("POST 資料 - CSRF Token: " . (empty($csrfToken) ? '空' : '[有值]'));
    
    // 檢查 Session
    writeDebugLog("Session ID: " . session_id());
    writeDebugLog("Session 資料: " . json_encode($_SESSION));

    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
        writeDebugLog("驗證失敗: $error");
    } else {
        writeDebugLog("基本驗證通過，開始登入流程");
        
        // 記錄登入前的狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        $beforeLines = 0;
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $beforeLines = count(explode("\n", trim($content)));
        }
        
        writeDebugLog("登入前 - 認證日誌大小: $beforeSize bytes, 行數: $beforeLines");
        
        // 執行登入
        writeDebugLog("呼叫 auth->login()");
        $result = $auth->login($email, $password, $rememberMe);
        writeDebugLog("auth->login() 完成");
        
        // 記錄登入後的狀態
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        $afterLines = 0;
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $afterLines = count(explode("\n", trim($content)));
        }
        
        writeDebugLog("登入後 - 認證日誌大小: $afterSize bytes, 行數: $afterLines");
        writeDebugLog("日誌變化 - 大小: " . ($afterSize - $beforeSize) . " bytes, 行數: " . ($afterLines - $beforeLines));
        
        // 記錄登入結果
        writeDebugLog("登入結果: " . json_encode($result));

        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            writeDebugLog("登入成功: $success");
            
            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);
            writeDebugLog("重定向 URL: $redirectUrl");

            // 使用 JavaScript 延遲跳轉以顯示成功訊息
            echo "<script>
                setTimeout(function() {
                    window.location.href = '" . page_url($redirectUrl) . "';
                }, 1500);
            </script>";
        } else {
            $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
            writeDebugLog("登入失敗: $error");
        }
        
        // 檢查最新的認證日誌
        if (file_exists($authLogFile)) {
            $content = file_get_contents($authLogFile);
            $lines = explode("\n", trim($content));
            $lastLine = end($lines);
            
            if (!empty($lastLine)) {
                $data = json_decode($lastLine, true);
                if ($data && $data['user_email'] === $email) {
                    writeDebugLog("✅ 找到對應的認證日誌記錄");
                    writeDebugLog("日誌內容: $lastLine");
                } else {
                    writeDebugLog("❌ 沒有找到對應的認證日誌記錄");
                    writeDebugLog("最後一行: $lastLine");
                }
            } else {
                writeDebugLog("❌ 認證日誌文件為空");
            }
        } else {
            writeDebugLog("❌ 認證日誌文件不存在");
        }
    }
} else {
    writeDebugLog("不是 POST 請求，顯示登入表單");
}

writeDebugLog("=== 登入請求處理完成 ===\n");

// 頁面標題
$pageTitle = 'SignAttend - 調試登入頁面';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="email"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
        .alert { padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .alert-danger { background: #fee; color: #c33; border: 1px solid #fcc; }
        .alert-success { background: #efe; color: #3c3; border: 1px solid #cfc; }
        .debug-info { background: #f5f5f5; padding: 15px; border-radius: 4px; margin-top: 20px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 調試登入頁面</h1>
    
    <div class="debug-info">
        <strong>調試資訊：</strong><br>
        - 當前環境: <?= defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義' ?><br>
        - LOG_ERRORS: <?= defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義' ?><br>
        - Session ID: <?= session_id() ?><br>
        - 調試日誌: <?= $debugLogFile ?><br>
        - 認證日誌: <?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log
    </div>
    
    <?php if ($error): ?>
        <div class="alert alert-danger">
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="alert alert-success">
            <?= htmlspecialchars($success) ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <?php generate_csrf_token_field(); ?>
        
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="debug_real_<?= date('His') ?>@example.com" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" value="wrong_password_<?= date('His') ?>" required>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" name="remember_me"> 記住我
            </label>
        </div>
        
        <button type="submit">登入</button>
    </form>
    
    <div class="debug-info">
        <strong>使用說明：</strong><br>
        1. 點擊「登入」按鈕（已預填測試資料）<br>
        2. 檢查調試日誌文件了解詳細執行過程<br>
        3. 檢查認證日誌文件是否有新記錄
    </div>
</body>
</html>
