<?php
/**
 * SignAttend PHP Frontend - 管理者登入頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄每次頁面訪問
$accessLogFile = LOG_PATH . '/page_access_' . date('Y-m-d') . '.log';
$accessMessage = sprintf(
    "[%s] LOGIN PAGE ACCESS: Method=%s, Host=%s, IP=%s, UserAgent=%s\n",
    date('Y-m-d H:i:s'),
    $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    $_SERVER['HTTP_HOST'] ?? 'unknown',
    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100)
);
file_put_contents($accessLogFile, $accessMessage, FILE_APPEND | LOCK_EX);

// 處理 JavaScript 調試日誌
if (isset($_POST['debug_js_log'])) {
    $jsLogData = json_decode($_POST['debug_js_log'], true);
    if ($jsLogData) {
        $jsLogMessage = sprintf(
            "[%s] JS DEBUG: %s\n",
            date('Y-m-d H:i:s'),
            json_encode($jsLogData, JSON_UNESCAPED_UNICODE)
        );
        file_put_contents($accessLogFile, $jsLogMessage, FILE_APPEND | LOCK_EX);
    }
    // 如果只是調試日誌請求，直接返回
    if (!isset($_POST['email']) && !isset($_POST['password'])) {
        http_response_code(200);
        exit;
    }
}

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    $redirectMessage = sprintf(
        "[%s] ALREADY LOGGED IN: Redirecting to dashboard\n",
        date('Y-m-d H:i:s')
    );
    file_put_contents($accessLogFile, $redirectMessage, FILE_APPEND | LOCK_EX);

    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理登入表單提交
$error = '';
$success = '';

// 記錄 POST 請求檢測
$postDetectionMessage = sprintf(
    "[%s] POST DETECTION: Method=%s, PostData=%s\n",
    date('Y-m-d H:i:s'),
    $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    json_encode($_POST)
);
file_put_contents($accessLogFile, $postDetectionMessage, FILE_APPEND | LOCK_EX);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 記錄進入 POST 處理
    $postEntryMessage = sprintf(
        "[%s] ENTERING POST PROCESSING: Email=%s, HasPassword=%s\n",
        date('Y-m-d H:i:s'),
        $_POST['email'] ?? 'empty',
        !empty($_POST['password']) ? 'yes' : 'no'
    );
    file_put_contents($accessLogFile, $postEntryMessage, FILE_APPEND | LOCK_EX);
    // 記錄 POST 資料
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    $csrfToken = $_POST['csrf_token'] ?? '';

    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
    } else {
        // 記錄登入前的狀態（用於調試）
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;

        // 執行登入
        $result = $auth->login($email, $password, $rememberMe);

        // 記錄登入後的狀態（用於調試）
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;

        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);

            // 使用 JavaScript 延遲跳轉以顯示成功訊息
            echo "<script>
                setTimeout(function() {
                    window.location.href = '" . page_url($redirectUrl) . "';
                }, 1500);
            </script>";
        } else {
            $error = $result['message'] ?? '登入失敗，請檢查您的憑證';

            // 強制記錄登入失敗（無論如何都記錄）
            try {
                $logData = [
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => null,
                    'user_email' => $email,
                    'action' => 'login_failed_manual',
                    'description' => '手動記錄登入失敗：' . $error,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'unknown'),
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'before_size' => $beforeSize,
                    'after_size' => $afterSize,
                    'session_id' => session_id(),
                    'post_data' => [
                        'email' => $email,
                        'has_password' => !empty($password),
                        'remember_me' => $rememberMe
                    ]
                ];

                $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";

                // 嘗試多種寫入方式
                $writeSuccess = false;

                // 方式 1: 標準寫入
                if (file_put_contents($authLogFile, $logMessage, FILE_APPEND | LOCK_EX)) {
                    $writeSuccess = true;
                }

                // 方式 2: 如果標準寫入失敗，嘗試不加鎖
                if (!$writeSuccess) {
                    if (file_put_contents($authLogFile, $logMessage, FILE_APPEND)) {
                        $writeSuccess = true;
                    }
                }

                // 方式 3: 如果還是失敗，寫入到備用日誌
                if (!$writeSuccess) {
                    $backupLogFile = LOG_PATH . '/login_backup_' . date('Y-m-d') . '.log';
                    file_put_contents($backupLogFile, $logMessage, FILE_APPEND);
                }

                // 記錄調試信息到錯誤日誌
                $debugMessage = sprintf(
                    "[%s] Login Debug: Email=%s, Success=%s, LogWritten=%s, BeforeSize=%d, AfterSize=%d\n",
                    date('Y-m-d H:i:s'),
                    $email,
                    $result['success'] ? 'true' : 'false',
                    $writeSuccess ? 'true' : 'false',
                    $beforeSize,
                    $afterSize
                );

                $errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
                file_put_contents($errorLogFile, $debugMessage, FILE_APPEND | LOCK_EX);

            } catch (Exception $e) {
                // 如果所有方式都失敗，至少記錄到錯誤日誌
                $errorMessage = sprintf(
                    "[%s] Login Log Error: %s for email %s\n",
                    date('Y-m-d H:i:s'),
                    $e->getMessage(),
                    $email
                );

                $errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
                file_put_contents($errorLogFile, $errorMessage, FILE_APPEND | LOCK_EX);
            }
        }
    }
}

// 頁面標題
$pageTitle = 'SignAttend - 智慧簽到系統';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <meta name="description" content="SignAttend 智慧簽到管理系統">
    <meta name="author" content="SignAttend System">

    <meta property="og:title" content="SignAttend - 智慧簽到系統">
    <meta property="og:description" content="SignAttend 智慧簽到管理系統">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        h1 {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group input::placeholder {
            color: #aaa;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .forgot-password {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #00f2fe;
        }

        .alert {
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-danger {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .alert-success {
            background: #efe;
            color: #3c3;
            border: 1px solid #cfc;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 25px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            width: auto;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 24px;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="logo">
            <div class="logo-icon">📱</div>
            <h1>SignAttend</h1>
            <p class="subtitle">智慧簽到管理系統</p>
        </div>

        <!-- 錯誤訊息顯示區域 -->
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?= h($error) ?>
            </div>
        <?php endif; ?>

        <!-- 成功訊息顯示區域 -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?= h($success) ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="" id="loginForm">
            <?php generate_csrf_token_field(); ?>

            <div class="form-group">
                <label for="email">電子郵件</label>
                <input type="email" id="email" name="email" placeholder="請輸入電子郵件"
                       value="<?= h($_POST['email'] ?? '') ?>" required>
            </div>

            <div class="form-group">
                <label for="password">密碼</label>
                <input type="password" id="password" name="password" placeholder="請輸入密碼" required>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="remember_me" name="remember_me"
                       <?= isset($_POST['remember_me']) ? 'checked' : '' ?>>
                <label for="remember_me">記住我</label>
            </div>

            <button type="submit" class="login-btn" id="loginButton">
                <span id="loginText">登入</span>
                <span id="loginSpinner" style="display: none;">登入中...</span>
            </button>

            <div style="margin-top: 20px;">
                <a href="<?= page_url('pages/forgot_password.php') ?>" class="forgot-password">忘記密碼？</a>
                <span style="color: #ccc; margin: 0 10px;">|</span>
                <a href="<?= page_url('index.php') ?>" class="forgot-password">返回首頁</a>
            </div>
        </form>
    </div>

    <script>
        // 設定載入狀態
        function setLoading(isLoading) {
            const button = document.getElementById('loginButton');
            const text = document.getElementById('loginText');
            const spinner = document.getElementById('loginSpinner');

            if (isLoading) {
                button.disabled = true;
                text.style.display = 'none';
                spinner.style.display = 'inline';
                button.style.opacity = '0.7';
            } else {
                button.disabled = false;
                text.style.display = 'inline';
                spinner.style.display = 'none';
                button.style.opacity = '1';
            }
        }

        // 表單提交時顯示載入狀態
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const formData = {
                email: document.getElementById('email').value,
                password: document.getElementById('password').value ? '[有密碼]' : '[無密碼]',
                method: this.method,
                action: this.action,
                timestamp: new Date().toISOString()
            };

            console.log('Form submit event triggered');
            console.log('Form data:', formData);

            // 將調試信息發送到服務器保存
            fetch('<?= $_SERVER['PHP_SELF'] ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'debug_js_log=' + encodeURIComponent(JSON.stringify({
                    action: 'form_submit_triggered',
                    data: formData,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                }))
            }).catch(err => console.log('Debug log failed:', err));

            // 添加隱藏的調試字段
            const debugField = document.createElement('input');
            debugField.type = 'hidden';
            debugField.name = 'debug_submit';
            debugField.value = 'form_submitted_' + new Date().getTime();
            this.appendChild(debugField);

            setLoading(true);
        });

        // 添加輸入框動畫效果
        const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Enter 鍵快速登入
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('loginButton').disabled) {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // 如果有成功訊息，自動隱藏載入狀態
        <?php if (!empty($success)): ?>
            setTimeout(function() {
                setLoading(false);
            }, 100);
        <?php endif; ?>

        // 如果有錯誤訊息，重置載入狀態
        <?php if (!empty($error)): ?>
            setLoading(false);
        <?php endif; ?>
    </script>
</body>
</html>