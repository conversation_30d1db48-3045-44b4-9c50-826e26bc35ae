<?php
// 測試日誌路徑配置
require_once 'config/config.php';

echo "<h2>日誌路徑測試</h2>";
echo "<p><strong>ROOT_PATH:</strong> " . ROOT_PATH . "</p>";
echo "<p><strong>LOG_PATH:</strong> " . LOG_PATH . "</p>";
echo "<p><strong>LOG_PATH 是否存在:</strong> " . (is_dir(LOG_PATH) ? '是' : '否') . "</p>";
echo "<p><strong>LOG_PATH 是否可寫:</strong> " . (is_writable(LOG_PATH) ? '是' : '否') . "</p>";

// 檢查實際的日誌檔案位置
$errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
echo "<p><strong>錯誤日誌檔案路徑:</strong> " . $errorLogFile . "</p>";
echo "<p><strong>錯誤日誌檔案是否存在:</strong> " . (file_exists($errorLogFile) ? '是' : '否') . "</p>";

// 檢查 htdocs/logs 狀態（應該已移除）
$htdocsLogDir = __DIR__ . '/logs';
echo "<p><strong>htdocs/logs 目錄:</strong> " . $htdocsLogDir . "</p>";
echo "<p><strong>htdocs/logs 是否存在:</strong> " . (is_dir($htdocsLogDir) ? '是（需要移除）' : '否（已正確移除）') . "</p>";

if (is_dir($htdocsLogDir)) {
    echo "<p style='color: orange;'><strong>⚠️ 警告：</strong>htdocs/logs 目錄仍然存在，建議移除以避免路徑混亂</p>";
    $files = scandir($htdocsLogDir);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "<p><strong>htdocs/logs 中的舊日誌檔案:</strong></p>";
    echo "<ul>";
    foreach ($logFiles as $file) {
        echo "<li>" . $file . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: green;'><strong>✅</strong> htdocs/logs 目錄已正確移除</p>";
}

// 測試寫入日誌
echo "<h3>測試日誌寫入</h3>";
$testMessage = "[" . date('Y-m-d H:i:s') . "] 測試日誌寫入\n";
$testLogFile = LOG_PATH . '/test_' . date('Y-m-d') . '.log';

if (file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "<p style='color: green;'>✓ 成功寫入測試日誌到: " . $testLogFile . "</p>";
} else {
    echo "<p style='color: red;'>✗ 無法寫入測試日誌到: " . $testLogFile . "</p>";
}
?>
