<?php

namespace SignAttend\Models;

use PDO;
use PDOException;

class Attendee {
    private $db;
    private $table_name = "attendees";

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    // 創建參與者
    public function create($data) {
        try {
            $query = "INSERT INTO " . $this->table_name . "
                      (id, name, email, phone, organization, position, meeting_id, checked_in, check_in_time, signature_data, signature_timestamp)
                      VALUES (:id, :name, :email, :phone, :organization, :position, :meeting_id, :checked_in, :check_in_time, :signature_data, :signature_timestamp)";
            $stmt = $this->db->prepare($query);

            $id = $this->generateUUID();
            $phone = $data['phone'] ?? null;
            $organization = $data['organization'] ?? null;
            $position = $data['position'] ?? null;
            $checked_in = $data['checked_in'] ?? false;
            $check_in_time = $data['check_in_time'] ?? null;
            $signature_data = $data['signature_data'] ?? null;
            $signature_timestamp = $data['signature_timestamp'] ?? null;

            $stmt->bindParam(":id", $id);
            $stmt->bindParam(":name", $data['name']);
            $stmt->bindParam(":email", $data['email']);
            $stmt->bindParam(":phone", $phone);
            $stmt->bindParam(":organization", $organization);
            $stmt->bindParam(":position", $position);
            $stmt->bindParam(":meeting_id", $data['meeting_id']);
            $stmt->bindParam(":checked_in", $checked_in, PDO::PARAM_BOOL);
            $stmt->bindParam(":check_in_time", $check_in_time);
            $stmt->bindParam(":signature_data", $signature_data);
            $stmt->bindParam(":signature_timestamp", $signature_timestamp);

            if ($stmt->execute()) {
                return $this->readOne($id);
            }
            return false;
        } catch (PDOException $e) {
            error_log("Attendee create error: " . $e->getMessage());
            return false;
        }
    }

    // 讀取單一參與者
    public function readOne($id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Attendee readOne error: " . $e->getMessage());
            return null;
        }
    }

    // 讀取某會議的所有參與者
    public function readByMeetingId($meeting_id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE meeting_id = ? ORDER BY created_at DESC";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $meeting_id);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Attendee readByMeetingId error: " . $e->getMessage());
            return [];
        }
    }

    // 更新參與者
    public function update($id, $data) {
        try {
            $fields = [];
            $params = [':id' => $id];

            if (isset($data['name'])) {
                $fields[] = "name = :name";
                $params[':name'] = $data['name'];
            }
            if (isset($data['email'])) {
                $fields[] = "email = :email";
                $params[':email'] = $data['email'];
            }
            if (isset($data['phone'])) {
                $fields[] = "phone = :phone";
                $params[':phone'] = $data['phone'];
            }
            if (isset($data['organization'])) {
                $fields[] = "organization = :organization";
                $params[':organization'] = $data['organization'];
            }
            if (isset($data['position'])) {
                $fields[] = "position = :position";
                $params[':position'] = $data['position'];
            }
            if (isset($data['checked_in'])) {
                $fields[] = "checked_in = :checked_in";
                $params[':checked_in'] = $data['checked_in'];
            }
            if (isset($data['check_in_time'])) {
                $fields[] = "check_in_time = :check_in_time";
                $params[':check_in_time'] = $data['check_in_time'];
            }
            if (isset($data['signature_data'])) {
                $fields[] = "signature_data = :signature_data";
                $params[':signature_data'] = $data['signature_data'];
            }
            if (isset($data['signature_timestamp'])) {
                $fields[] = "signature_timestamp = :signature_timestamp";
                $params[':signature_timestamp'] = $data['signature_timestamp'];
            }

            if (empty($fields)) {
                return false;
            }

            $query = "UPDATE " . $this->table_name . " SET " . implode(', ', $fields) . " WHERE id = :id";
            $stmt = $this->db->prepare($query);

            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Attendee update error: " . $e->getMessage());
            return false;
        }
    }

    // 刪除參與者
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Attendee delete error: " . $e->getMessage());
            return false;
        }
    }

    // 簽到功能
    public function checkIn($id, $signature_data = null) {
        $data = [
            'checked_in' => true,
            'check_in_time' => date('Y-m-d H:i:s')
        ];

        if ($signature_data) {
            $data['signature_data'] = $signature_data;
            $data['signature_timestamp'] = date('Y-m-d H:i:s');
        }

        return $this->update($id, $data);
    }

    // 根據電子郵件和會議 ID 查找參與者
    public function findByEmailAndMeeting($email, $meeting_id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE email = ? AND meeting_id = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $email);
            $stmt->bindParam(2, $meeting_id);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Attendee findByEmailAndMeeting error: " . $e->getMessage());
            return null;
        }
    }

    // 生成 UUID
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
