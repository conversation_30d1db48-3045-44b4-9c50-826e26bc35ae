{
    "timestamp": "2025-07-09 13:12:48",
    "action": "form_submitted",
    "post_data": {
        "name": "root",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-09 13:12:48",
    "action": "validation_passed",
    "post_data": {
        "name": "root",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-09 13:12:48",
    "action": "register_result",
    "post_data": {
        "name": "root",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686dfa5030ef2_1752037968",
            "email": "<EMAIL>",
            "name": "root"
        }
    }
}
