<?php
// 檢查日誌路徑配置
define('SIGNATTEND_INIT', true);
require_once 'config/config.php';

echo "=== 日誌路徑檢查 ===\n";
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";
echo "LOG_PATH 是否存在: " . (is_dir(LOG_PATH) ? '是' : '否') . "\n";
echo "LOG_PATH 是否可寫: " . (is_writable(LOG_PATH) ? '是' : '否') . "\n";

// 檢查外層 logs 目錄
$outerLogsDir = dirname(dirname(__FILE__)) . '/logs';
echo "\n=== 外層 logs 目錄 ===\n";
echo "外層 logs 路徑: " . $outerLogsDir . "\n";
echo "外層 logs 是否存在: " . (is_dir($outerLogsDir) ? '是' : '否') . "\n";
echo "外層 logs 是否可寫: " . (is_writable($outerLogsDir) ? '是' : '否') . "\n";

// 檢查 htdocs/logs 目錄（已移除）
$htdocsLogsDir = dirname(__FILE__) . '/logs';
echo "\n=== htdocs/logs 目錄狀態 ===\n";
echo "htdocs/logs 路徑: " . $htdocsLogsDir . "\n";
echo "htdocs/logs 是否存在: " . (is_dir($htdocsLogsDir) ? '是（需要移除）' : '否（已正確移除）') . "\n";

if (is_dir($htdocsLogsDir)) {
    echo "⚠️ 警告：htdocs/logs 目錄仍然存在，建議移除以避免混亂\n";
    $files = scandir($htdocsLogsDir);
    $logFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'log';
    });
    echo "htdocs/logs 中的日誌文件數量: " . count($logFiles) . "\n";
} else {
    echo "✅ htdocs/logs 目錄已正確移除\n";
}

// 測試寫入到配置的 LOG_PATH
echo "\n=== 測試日誌寫入 ===\n";
$testMessage = "[" . date('Y-m-d H:i:s') . "] 測試日誌寫入\n";
$testLogFile = LOG_PATH . '/test_' . date('Y-m-d') . '.log';

if (file_put_contents($testLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "✓ 成功寫入測試日誌到: " . $testLogFile . "\n";
    echo "文件是否存在: " . (file_exists($testLogFile) ? '是' : '否') . "\n";
} else {
    echo "✗ 無法寫入測試日誌到: " . $testLogFile . "\n";
}
?>
