<?php
/**
 * 路徑調試工具
 */

define('SIGNATTEND_INIT', true);

echo "<h1>🔍 路徑調試</h1>";
echo "<div style='font-family: Arial; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 8px;'>";

echo "<h2>1. 當前文件路徑資訊</h2>";
echo "<p><strong>當前文件：</strong> " . __FILE__ . "</p>";
echo "<p><strong>當前目錄：</strong> " . __DIR__ . "</p>";
echo "<p><strong>上級目錄：</strong> " . dirname(__DIR__) . "</p>";
echo "<p><strong>上上級目錄：</strong> " . dirname(__DIR__, 2) . "</p>";

echo "<h2>2. 計算的敏感配置路徑</h2>";
$secretsFile = dirname(__DIR__, 2) . '/config/secrets.production.php';
echo "<p><strong>計算路徑：</strong> " . $secretsFile . "</p>";
echo "<p><strong>文件存在：</strong> " . (file_exists($secretsFile) ? '✅ 是' : '❌ 否') . "</p>";

if (file_exists($secretsFile)) {
    echo "<p><strong>文件大小：</strong> " . filesize($secretsFile) . " bytes</p>";
    echo "<p><strong>文件權限：</strong> " . substr(sprintf('%o', fileperms($secretsFile)), -4) . "</p>";
}

echo "<h2>3. 嘗試不同的路徑</h2>";
$possiblePaths = [
    dirname(__DIR__, 2) . '/config/secrets.production.php',
    dirname(__DIR__) . '/../config/secrets.production.php',
    '../config/secrets.production.php',
    '../../config/secrets.production.php',
    dirname($_SERVER['DOCUMENT_ROOT']) . '/config/secrets.production.php'
];

foreach ($possiblePaths as $i => $path) {
    $exists = file_exists($path);
    echo "<p><strong>路徑 " . ($i + 1) . "：</strong> " . $path . " - " . ($exists ? '✅ 存在' : '❌ 不存在') . "</p>";
}

echo "<h2>4. 伺服器環境資訊</h2>";
echo "<p><strong>DOCUMENT_ROOT：</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>SCRIPT_FILENAME：</strong> " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p><strong>工作目錄：</strong> " . getcwd() . "</p>";

echo "<h2>5. 手動載入測試</h2>";
try {
    $testPath = dirname(__DIR__, 2) . '/config/secrets.production.php';
    if (file_exists($testPath)) {
        require_once $testPath;
        echo "<p class='success' style='color: green;'>✅ 外部配置載入成功</p>";
        
        if (defined('DB_PASSWORD_PRODUCTION')) {
            echo "<p><strong>DB_PASSWORD_PRODUCTION：</strong> ***已定義***</p>";
        } else {
            echo "<p style='color: red;'>❌ DB_PASSWORD_PRODUCTION 未定義</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ 無法找到外部配置文件</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 載入錯誤：" . $e->getMessage() . "</p>";
}

echo "</div>";
?>
