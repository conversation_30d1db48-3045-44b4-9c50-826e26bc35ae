<?php
/**
 * 簡單的原生 PHP 登入頁面
 */

// 啟動 Session
session_start();

// 處理登出
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// 資料庫配置
$db_host = 'sql302.byetcluster.com';
$db_name = 'uoolo_38699510_signattend';
$db_user = 'uoolo_38699510';
$db_pass = 'kai@0932540826';

$message = '';
$success = false;

// 處理登入
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    
    if (empty($email) || empty($password)) {
        $message = '請輸入電子郵件和密碼';
    } else {
        try {
            // 連接資料庫
            $pdo = new PDO(
                "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4",
                $db_user,
                $db_pass,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // 查找用戶
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // 登入成功
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['logged_in'] = true;
                $_SESSION['login_time'] = time();
                
                $success = true;
                $message = '登入成功！';
                
                // 可以重定向到儀表板
                // header('Location: pages/dashboard.php');
                // exit;
            } else {
                $message = '電子郵件或密碼錯誤';
            }
            
        } catch (PDOException $e) {
            $message = '資料庫連接失敗：' . $e->getMessage();
        }
    }
}

// 檢查是否已登入
$isLoggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡單登入 - SignAttend</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .message {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .demo-accounts {
            margin-top: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .demo-accounts h3 {
            margin-bottom: 0.5rem;
            color: #333;
            font-size: 0.9rem;
        }
        
        .demo-btn {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0.25rem;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            font-size: 0.8rem;
            cursor: pointer;
            border: none;
        }
        
        .demo-btn:hover {
            background: #5a6268;
        }
        
        .status {
            margin-top: 1rem;
            padding: 0.5rem;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 SignAttend</h1>
            <p>簡單登入系統</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?= $success ? 'success' : 'error' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($isLoggedIn): ?>
            <div class="message success">
                ✅ 您已成功登入！<br>
                用戶：<?= htmlspecialchars($_SESSION['user_email']) ?><br>
                登入時間：<?= date('Y-m-d H:i:s', $_SESSION['login_time']) ?>
            </div>
            <a href="?logout=1" class="btn" style="text-decoration: none; display: block; text-align: center;">登出</a>
        <?php else: ?>
            <form method="POST" action="">
                <div class="form-group">
                    <label for="email">📧 電子郵件</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">🔒 密碼</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn">登入</button>
            </form>
            
            <div class="demo-accounts">
                <h3>🎯 測試帳號</h3>
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'aaaaaa')"><EMAIL></button>
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'demo123')"><EMAIL></button>
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'test123')"><EMAIL></button>
            </div>
        <?php endif; ?>
        
        <div class="status">
            <strong>系統狀態：</strong><br>
            Session ID: <?= session_id() ?><br>
            登入狀態: <?= $isLoggedIn ? '✅ 已登入' : '❌ 未登入' ?><br>
            時間: <?= date('Y-m-d H:i:s') ?>
        </div>
    </div>
    
    <script>
        function fillDemo(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>


