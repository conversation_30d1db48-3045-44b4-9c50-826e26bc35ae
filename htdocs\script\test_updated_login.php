<?php
// 測試修改後的 Auth 類登入功能
define('SIGNATTEND_INIT', true);

// 載入配置
require_once 'config/config.php';
require_once 'utils/Auth.php';

echo "=== 測試修改後的 Auth 類登入功能 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n\n";

// 創建 Auth 實例
$auth = new Auth();

// 模擬登入表單提交
$email = '<EMAIL>';
$password = 'wrong_password';
$rememberMe = false;

echo "測試登入失敗情況...\n";
echo "Email: $email\n";

if (empty($email) || empty($password)) {
    $error = '請輸入電子郵件和密碼';
    echo "驗證失敗: $error\n";
} else {
    echo "基本驗證通過，開始登入流程\n";
    
    // 記錄登入前的狀態（用於調試）
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "登入前日誌大小: $beforeSize bytes\n";
    
    // 執行登入
    echo "呼叫 auth->login()...\n";
    $result = $auth->login($email, $password, $rememberMe);
    echo "auth->login() 完成\n";
    
    // 記錄登入後的狀態（用於調試）
    $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
    echo "登入後日誌大小: $afterSize bytes\n";
    echo "日誌大小變化: " . ($afterSize - $beforeSize) . " bytes\n";
    
    if ($result['success']) {
        $success = '登入成功！正在跳轉...';
        echo "✅ 登入成功: $success\n";
    } else {
        $error = $result['message'] ?? '登入失敗，請檢查您的憑證';
        echo "❌ 登入失敗: $error\n";
        
        // 確保登入失敗有記錄到日誌（額外保障）
        if ($afterSize <= $beforeSize) {
            echo "日誌沒有增長，手動記錄登入失敗\n";
            
            // 如果日誌沒有增長，手動記錄
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'user_id' => null,
                'user_email' => $email,
                'action' => 'login_failed',
                'description' => '登入失敗：' . $error,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            if (file_put_contents($authLogFile, $logMessage, FILE_APPEND | LOCK_EX)) {
                echo "✅ 手動日誌記錄成功\n";
            } else {
                echo "❌ 手動日誌記錄失敗\n";
            }
        } else {
            echo "✅ 日誌已自動記錄\n";
        }
    }
}

// 檢查日誌文件
echo "\n=== 檢查認證日誌 ===\n";
if (file_exists($authLogFile)) {
    echo "認證日誌文件存在\n";
    echo "文件大小: " . filesize($authLogFile) . " bytes\n";
    
    // 讀取最後幾行
    $lines = file($authLogFile, FILE_IGNORE_NEW_LINES);
    $lastLines = array_slice($lines, -3);
    
    echo "\n最後 3 行日誌內容:\n";
    foreach ($lastLines as $i => $line) {
        $data = json_decode($line, true);
        if ($data) {
            echo ($i + 1) . ". [" . $data['timestamp'] . "] " . $data['action'] . " - " . $data['user_email'] . "\n";
            echo "   描述: " . $data['description'] . "\n";
        } else {
            echo ($i + 1) . ". " . $line . "\n";
        }
    }
} else {
    echo "認證日誌文件不存在\n";
}

echo "\n測試完成！\n";
?>




