<?php
/**
 * 測試修復後的 AJAX 登入功能
 */

echo "=== 測試修復後的 AJAX 登入功能 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 測試演示帳號
$testEmail = '<EMAIL>';
$testPassword = 'demo123';

echo "測試帳號: $testEmail\n";
echo "測試密碼: $testPassword\n\n";

// 準備 POST 數據
$postData = [
    'ajax_login' => '1',
    'email' => $testEmail,
    'password' => $testPassword,
    'remember_me' => '0'
];

echo "POST 數據: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 使用 cURL 發送 AJAX 請求
$ch = curl_init();

// 構建完整的 URL
$loginUrl = 'http://localhost/attendance.app.tn/htdocs/pages/login.php';

curl_setopt_array($ch, [
    CURLOPT_URL => $loginUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($postData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => false,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => false,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/x-www-form-urlencoded',
        'X-Requested-With: XMLHttpRequest',
        'User-Agent: Test Script'
    ]
]);

echo "發送 AJAX 請求到: $loginUrl\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ cURL 錯誤: $error\n";
    exit(1);
}

echo "HTTP 狀態碼: $httpCode\n";
echo "響應內容:\n";
echo $response . "\n\n";

// 嘗試解析 JSON 響應
$jsonData = json_decode($response, true);

if ($jsonData) {
    echo "=== JSON 解析成功 ===\n";
    echo "成功: " . ($jsonData['success'] ? '是' : '否') . "\n";
    echo "訊息: " . ($jsonData['message'] ?? 'N/A') . "\n";
    echo "重定向: " . ($jsonData['redirect'] ?? 'N/A') . "\n";
    
    if (isset($jsonData['debug_info'])) {
        echo "\n調試信息:\n";
        echo "- 儲存類型: " . ($jsonData['debug_info']['storage_type'] ?? 'N/A') . "\n";
        echo "- 資料表: " . ($jsonData['debug_info']['table_name'] ?? 'N/A') . "\n";
        echo "- 登入前記錄數: " . ($jsonData['debug_info']['before_count'] ?? 'N/A') . "\n";
        echo "- 登入後記錄數: " . ($jsonData['debug_info']['after_count'] ?? 'N/A') . "\n";
        echo "- 記錄變化: " . ($jsonData['debug_info']['record_change'] ?? 'N/A') . "\n";
        
        $recordChange = $jsonData['debug_info']['record_change'] ?? 0;
        
        if ($recordChange > 0) {
            echo "\n✅ 資料庫記錄寫入成功！\n";
        } else {
            echo "\n❌ 資料庫記錄寫入失敗！\n";
        }
    }
    
    if ($jsonData['success']) {
        echo "\n🎉 AJAX 登入測試成功！\n";
        echo "演示按鈕功能應該正常工作。\n";
    } else {
        echo "\n⚠️ 登入失敗，但這可能是正常的（如果是測試失敗的情況）\n";
    }
    
} else {
    echo "❌ JSON 解析失敗\n";
    echo "原始響應可能包含 HTML 或錯誤信息\n";
}

echo "\n=== 檢查調試日誌 ===\n";

// 檢查調試日誌
$debugLogFile = dirname(__DIR__) . '/logs/pages_login_debug_' . date('Y-m-d') . '.log';
echo "調試日誌檔案: $debugLogFile\n";

if (file_exists($debugLogFile)) {
    echo "調試日誌存在，大小: " . filesize($debugLogFile) . " bytes\n";
    
    // 讀取最後幾行
    $content = file_get_contents($debugLogFile);
    $lines = explode("\n", trim($content));
    $lastLines = array_slice($lines, -10); // 最後 10 行
    
    echo "\n最後 10 行調試日誌:\n";
    foreach ($lastLines as $line) {
        if (!empty($line)) {
            echo $line . "\n";
        }
    }
} else {
    echo "調試日誌檔案不存在\n";
}

echo "\n=== 測試完成 ===\n";
echo "如果看到 '✅ 資料庫記錄寫入成功' 和 '🎉 AJAX 登入測試成功'，\n";
echo "那麼演示按鈕功能應該正常工作。\n";
?>
