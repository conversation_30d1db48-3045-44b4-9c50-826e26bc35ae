<?php
/**
 * 測試路徑修正
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

echo "=== 路徑修正測試 ===\n";

// 測試不同的路徑載入方式
echo "當前文件: " . __FILE__ . "\n";
echo "__DIR__: " . __DIR__ . "\n";
echo "dirname(__DIR__): " . dirname(__DIR__) . "\n";

// 方式 1: 原來的方式（錯誤）
$oldPath = __DIR__ . '/../config/environment.php';
echo "\n舊路徑: " . $oldPath . "\n";
echo "舊路徑存在: " . (file_exists($oldPath) ? '是' : '否') . "\n";

// 方式 2: 修正後的方式
$newPath = dirname(__DIR__) . '/config/environment.php';
echo "新路徑: " . $newPath . "\n";
echo "新路徑存在: " . (file_exists($newPath) ? '是' : '否') . "\n";

// 載入環境配置檔案
require_once dirname(__DIR__) . '/config/environment.php';

echo "\n=== 載入後的配置 ===\n";
echo "LOG_PATH: " . (defined('LOG_PATH') ? LOG_PATH : '未定義') . "\n";
echo "LOG_ERRORS: " . (defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義') . "\n";

// 檢查認證日誌文件
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "認證日誌文件: " . $authLogFile . "\n";
echo "認證日誌存在: " . (file_exists($authLogFile) ? '是' : '否') . "\n";
if (file_exists($authLogFile)) {
    echo "認證日誌大小: " . filesize($authLogFile) . " bytes\n";
}

// 測試寫入
$testMessage = "[" . date('Y-m-d H:i:s') . "] 路徑修正測試\n";
if (file_put_contents($authLogFile, $testMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 測試寫入成功\n";
    echo "寫入後大小: " . filesize($authLogFile) . " bytes\n";
} else {
    echo "❌ 測試寫入失敗\n";
}

echo "\n測試完成！\n";
?>
