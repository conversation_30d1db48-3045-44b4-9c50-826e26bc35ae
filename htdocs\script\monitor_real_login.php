<?php
/**
 * 監控真實登入頁面的日誌寫入情況
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 處理 AJAX 請求
if (isset($_GET['action']) && $_GET['action'] === 'check_logs') {
    header('Content-Type: application/json');
    
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    $debugLogFile = LOG_PATH . '/pages_login_debug_' . date('Y-m-d') . '.log';
    
    $response = [
        'timestamp' => date('Y-m-d H:i:s'),
        'auth_log' => [
            'exists' => file_exists($authLogFile),
            'size' => file_exists($authLogFile) ? filesize($authLogFile) : 0,
            'modified' => file_exists($authLogFile) ? date('Y-m-d H:i:s', filemtime($authLogFile)) : 'N/A',
            'recent_lines' => []
        ],
        'debug_log' => [
            'exists' => file_exists($debugLogFile),
            'size' => file_exists($debugLogFile) ? filesize($debugLogFile) : 0,
            'modified' => file_exists($debugLogFile) ? date('Y-m-d H:i:s', filemtime($debugLogFile)) : 'N/A',
            'recent_lines' => []
        ]
    ];
    
    // 讀取認證日誌最後幾行
    if (file_exists($authLogFile)) {
        $content = file_get_contents($authLogFile);
        $lines = explode("\n", trim($content));
        $recentLines = array_slice($lines, -5);
        
        foreach ($recentLines as $line) {
            if (!empty($line)) {
                $response['auth_log']['recent_lines'][] = $line;
            }
        }
    }
    
    // 讀取調試日誌最後幾行
    if (file_exists($debugLogFile)) {
        $content = file_get_contents($debugLogFile);
        $lines = explode("\n", trim($content));
        $recentLines = array_slice($lines, -10);
        
        foreach ($recentLines as $line) {
            if (!empty($line)) {
                $response['debug_log']['recent_lines'][] = $line;
            }
        }
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>實時登入日誌監控</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .log-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .log-panel h3 {
            margin-top: 0;
            color: #495057;
        }
        .log-entry {
            background: white;
            padding: 8px;
            margin: 4px 0;
            border-left: 3px solid #007bff;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        <label>
            <input type="checkbox" id="autoRefresh" checked> 自動重新整理 (5秒)
        </label>
    </div>

    <div class="container">
        <h1>🔍 實時登入日誌監控</h1>
        
        <div class="status info">
            <strong>監控說明：</strong>
            這個頁面會實時監控登入頁面的日誌寫入情況。請在另一個分頁開啟登入頁面進行測試，然後回來查看日誌變化。
        </div>

        <div class="status warning">
            <strong>測試步驟：</strong><br>
            1. 在新分頁開啟：<a href="pages/login.php" target="_blank">pages/login.php</a><br>
            2. 嘗試使用任意郵箱和密碼登入（會失敗，但應該寫入日誌）<br>
            3. 回到這個頁面查看日誌是否有新記錄
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="refreshLogs()">🔄 立即重新整理</button>
            <button class="btn" onclick="clearDisplay()">🗑️ 清除顯示</button>
        </div>

        <div id="lastUpdate" class="status info">
            最後更新：載入中...
        </div>

        <div class="status-panel">
            <div class="log-panel">
                <h3>📋 認證日誌 (auth_*.log)</h3>
                <div id="authLogStatus">載入中...</div>
                <div id="authLogEntries"></div>
            </div>

            <div class="log-panel">
                <h3>🐛 調試日誌 (pages_login_debug_*.log)</h3>
                <div id="debugLogStatus">載入中...</div>
                <div id="debugLogEntries"></div>
            </div>
        </div>

        <div id="changeLog" style="margin-top: 20px;"></div>
    </div>

    <script>
        let lastAuthSize = 0;
        let lastDebugSize = 0;
        let refreshInterval;

        function refreshLogs() {
            fetch('?action=check_logs')
            .then(response => response.json())
            .then(data => {
                updateDisplay(data);
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('lastUpdate').innerHTML = 
                    '<div class="status error">更新失敗: ' + error.message + '</div>';
            });
        }

        function updateDisplay(data) {
            // 更新時間戳
            document.getElementById('lastUpdate').innerHTML = 
                '<div class="status info">最後更新：' + data.timestamp + '</div>';

            // 更新認證日誌狀態
            const authLog = data.auth_log;
            let authStatusHtml = '';
            authStatusHtml += '<strong>檔案狀態：</strong><br>';
            authStatusHtml += '存在：' + (authLog.exists ? '✅ 是' : '❌ 否') + '<br>';
            authStatusHtml += '大小：' + authLog.size + ' bytes<br>';
            authStatusHtml += '修改時間：' + authLog.modified + '<br>';
            
            document.getElementById('authLogStatus').innerHTML = authStatusHtml;

            // 更新認證日誌內容
            let authEntriesHtml = '';
            if (authLog.recent_lines.length > 0) {
                authLog.recent_lines.forEach(line => {
                    authEntriesHtml += '<div class="log-entry">' + escapeHtml(line) + '</div>';
                });
            } else {
                authEntriesHtml = '<div class="status warning">沒有日誌記錄</div>';
            }
            document.getElementById('authLogEntries').innerHTML = authEntriesHtml;

            // 更新調試日誌狀態
            const debugLog = data.debug_log;
            let debugStatusHtml = '';
            debugStatusHtml += '<strong>檔案狀態：</strong><br>';
            debugStatusHtml += '存在：' + (debugLog.exists ? '✅ 是' : '❌ 否') + '<br>';
            debugStatusHtml += '大小：' + debugLog.size + ' bytes<br>';
            debugStatusHtml += '修改時間：' + debugLog.modified + '<br>';
            
            document.getElementById('debugLogStatus').innerHTML = debugStatusHtml;

            // 更新調試日誌內容
            let debugEntriesHtml = '';
            if (debugLog.recent_lines.length > 0) {
                debugLog.recent_lines.forEach(line => {
                    debugEntriesHtml += '<div class="log-entry">' + escapeHtml(line) + '</div>';
                });
            } else {
                debugEntriesHtml = '<div class="status warning">沒有調試日誌記錄</div>';
            }
            document.getElementById('debugLogEntries').innerHTML = debugEntriesHtml;

            // 檢查變化
            checkChanges(authLog.size, debugLog.size);
        }

        function checkChanges(currentAuthSize, currentDebugSize) {
            const changeLogDiv = document.getElementById('changeLog');
            let changeHtml = '';

            if (lastAuthSize > 0 && currentAuthSize > lastAuthSize) {
                const change = currentAuthSize - lastAuthSize;
                changeHtml += '<div class="status success">✅ 認證日誌增加了 ' + change + ' bytes</div>';
            }

            if (lastDebugSize > 0 && currentDebugSize > lastDebugSize) {
                const change = currentDebugSize - lastDebugSize;
                changeHtml += '<div class="status success">✅ 調試日誌增加了 ' + change + ' bytes</div>';
            }

            if (changeHtml) {
                changeLogDiv.innerHTML = changeHtml + changeLogDiv.innerHTML;
            }

            lastAuthSize = currentAuthSize;
            lastDebugSize = currentDebugSize;
        }

        function clearDisplay() {
            document.getElementById('changeLog').innerHTML = '';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function startAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            if (document.getElementById('autoRefresh').checked) {
                refreshInterval = setInterval(refreshLogs, 5000);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshLogs();
            startAutoRefresh();
            
            document.getElementById('autoRefresh').addEventListener('change', startAutoRefresh);
        });
    </script>
</body>
</html>
