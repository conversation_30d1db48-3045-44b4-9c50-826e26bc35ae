<?php

namespace SignAttend\Models;

use PDO;
use PDOException;

class User {
    private $db;
    private $table_name = "users";

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    // 創建使用者
    public function create($data) {
        try {
            $query = "INSERT INTO " . $this->table_name . " (id, email, password_hash, created_at, updated_at) VALUES (:id, :email, :password_hash, :created_at, :updated_at)";
            $stmt = $this->db->prepare($query);

            $id = $this->generateUUID();
            $now = date('Y-m-d H:i:s'); // PHP 生成的台北時間

            $stmt->bindParam(":id", $id);
            $stmt->bindParam(":email", $data['email']);
            $stmt->bindParam(":password_hash", $data['password_hash']);
            $stmt->bindParam(":created_at", $now);
            $stmt->bindParam(":updated_at", $now);

            if ($stmt->execute()) {
                return $this->readOne($id);
            }
            return false;
        } catch (PDOException $e) {
            error_log("User create error: " . $e->getMessage());
            return false;
        }
    }

    // 讀取單一使用者
    public function readOne($id) {
        try {
            $query = "SELECT id, email, password_hash, created_at, updated_at FROM " . $this->table_name . " WHERE id = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("User readOne error: " . $e->getMessage());
            return null;
        }
    }

    // 讀取所有使用者
    public function readAll() {
        try {
            $query = "SELECT id, email, created_at, updated_at FROM " . $this->table_name . " ORDER BY created_at DESC";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("User readAll error: " . $e->getMessage());
            return [];
        }
    }

    // 更新使用者
    public function update($id, $data) {
        try {
            $query = "UPDATE " . $this->table_name . " SET email = :email WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(":email", $data['email']);
            $stmt->bindParam(":id", $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("User update error: " . $e->getMessage());
            return false;
        }
    }

    // 刪除使用者
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("User delete error: " . $e->getMessage());
            return false;
        }
    }

    // 根據電子郵件查找使用者
    public function findByEmail($email) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE email = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $email);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("User findByEmail error: " . $e->getMessage());
            return null;
        }
    }

    // 驗證密碼
    public function verifyPassword($email, $password) {
        $user = $this->findByEmail($email);
        if ($user && password_verify($password, $user['password_hash'])) {
            // 不返回密碼雜湊
            unset($user['password_hash']);
            return $user;
        }
        return false;
    }

    // 更新密碼
    public function updatePassword($userId, $hashedPassword) {
        try {
            $query = "UPDATE " . $this->table_name . " SET password_hash = :password_hash, updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(":password_hash", $hashedPassword);
            $stmt->bindParam(":id", $userId);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("User updatePassword error: " . $e->getMessage());
            return false;
        }
    }

    // 生成 UUID
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
