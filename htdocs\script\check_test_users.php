<?php
/**
 * 檢查測試用戶帳號
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 檢查測試用戶帳號 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n";
echo "資料庫: " . DB_NAME . "\n\n";

try {
    // 連接資料庫
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ 資料庫連接成功\n\n";
    
    // 檢查 users 表結構
    echo "=== users 表結構 ===\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    
    // 檢查現有用戶
    echo "\n=== 現有用戶列表 ===\n";
    $stmt = $pdo->query("
        SELECT id, email, created_at
        FROM users
        ORDER BY created_at DESC
    ");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "❌ 沒有找到任何用戶\n";
        echo "\n=== 創建測試用戶 ===\n";

        // 創建測試用戶
        $testUsers = [
            ['email' => '<EMAIL>', 'password' => 'demo123'],
            ['email' => '<EMAIL>', 'password' => 'test123'],
            ['email' => '<EMAIL>', 'password' => 'admin123']
        ];

        foreach ($testUsers as $user) {
            $userId = bin2hex(random_bytes(18)); // 生成 36 字符的 UUID
            $passwordHash = password_hash($user['password'], PASSWORD_DEFAULT);

            $stmt = $pdo->prepare("
                INSERT INTO users (id, email, password_hash, created_at, updated_at)
                VALUES (?, ?, ?, NOW(), NOW())
            ");

            try {
                $stmt->execute([$userId, $user['email'], $passwordHash]);
                echo "✅ 創建用戶: " . $user['email'] . " (密碼: " . $user['password'] . ")\n";
            } catch (PDOException $e) {
                echo "❌ 創建用戶失敗: " . $user['email'] . " - " . $e->getMessage() . "\n";
            }
        }

        // 重新查詢用戶
        $stmt = $pdo->query("SELECT id, email, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll();
    }

    if (!empty($users)) {
        echo "找到 " . count($users) . " 個用戶:\n\n";

        printf("%-8s %-25s %-20s\n", "ID前綴", "郵箱", "創建時間");
        echo str_repeat("-", 60) . "\n";

        foreach ($users as $user) {
            printf("%-8s %-25s %-20s\n",
                substr($user['id'], 0, 8),
                $user['email'],
                substr($user['created_at'], 0, 19)
            );
        }
        
    }

    echo "\n=== 測試帳號信息 ===\n";
    echo "以下是可用的測試帳號:\n\n";

    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'demo123', 'description' => '演示帳號'],
        ['email' => '<EMAIL>', 'password' => 'test123', 'description' => '測試帳號'],
        ['email' => '<EMAIL>', 'password' => 'admin123', 'description' => '管理員帳號']
    ];

    foreach ($testCredentials as $cred) {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$cred['email']]);
        $exists = $stmt->fetch();

        if ($exists) {
            echo "✅ " . $cred['description'] . "\n";
            echo "   郵箱: " . $cred['email'] . "\n";
            echo "   密碼: " . $cred['password'] . "\n\n";
        }
    }
    
    // 檢查密碼欄位
    echo "\n=== 密碼欄位檢查 ===\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password%'");
    $passwordColumns = $stmt->fetchAll();
    
    if (empty($passwordColumns)) {
        echo "❌ 未找到密碼欄位\n";
    } else {
        echo "找到密碼相關欄位:\n";
        foreach ($passwordColumns as $col) {
            echo "- " . $col['Field'] . " (" . $col['Type'] . ")\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 一般錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 檢查完成 ===\n";
?>
