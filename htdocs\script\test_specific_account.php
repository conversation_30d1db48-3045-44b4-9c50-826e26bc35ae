<?php
/**
 * 測試特定帳號登入功能
 */

define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/../config/environment.php';

echo "=== 測試特定帳號登入功能 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 連接資料庫
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ 資料庫連接成功\n\n";
    
    // 測試帳號
    $testAccount = [
        'email' => '<EMAIL>',
        'password' => 'aaaaaa'
    ];
    
    echo "測試帳號: " . $testAccount['email'] . "\n";
    echo "測試密碼: " . $testAccount['password'] . "\n\n";
    
    // 檢查帳號是否存在
    echo "--- 檢查帳號是否存在 ---\n";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$testAccount['email']]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ 帳號存在於資料庫\n";
        echo "用戶ID: " . substr($user['id'], 0, 8) . "...\n";
        
        // 驗證密碼
        if (password_verify($testAccount['password'], $user['password_hash'])) {
            echo "✅ 密碼驗證正確\n";
        } else {
            echo "❌ 密碼驗證失敗\n";
            echo "嘗試重新設置密碼...\n";
            
            // 重新設置密碼
            $newHash = password_hash($testAccount['password'], PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE email = ?");
            $updateResult = $updateStmt->execute([$newHash, $testAccount['email']]);
            
            if ($updateResult) {
                echo "✅ 密碼重新設置成功\n";
            } else {
                echo "❌ 密碼重新設置失敗\n";
            }
        }
    } else {
        echo "❌ 帳號不存在於資料庫\n";
        echo "嘗試創建帳號...\n";
        
        // 創建帳號
        $userId = bin2hex(random_bytes(18));
        $passwordHash = password_hash($testAccount['password'], PASSWORD_DEFAULT);
        
        $insertStmt = $pdo->prepare("
            INSERT INTO users (id, email, password_hash, created_at, updated_at) 
            VALUES (?, ?, ?, NOW(), NOW())
        ");
        
        $insertResult = $insertStmt->execute([$userId, $testAccount['email'], $passwordHash]);
        
        if ($insertResult) {
            echo "✅ 帳號創建成功\n";
        } else {
            echo "❌ 帳號創建失敗\n";
        }
    }
    
    // 測試 Auth 登入
    echo "\n--- 測試 Auth 登入 ---\n";
    
    // 載入 Auth 類
    require_once __DIR__ . '/../utils/Auth.php';
    $auth = new Auth();
    
    // 測試資料庫直接登入
    echo "測試 loginWithDatabase() 方法...\n";
    $result = $auth->loginWithDatabase($testAccount['email'], $testAccount['password'], false);
    
    echo "登入結果:\n";
    echo "成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "訊息: " . $result['message'] . "\n";
    
    if ($result['success']) {
        echo "用戶資料: " . json_encode($result['user'], JSON_UNESCAPED_UNICODE) . "\n";
        
        // 檢查 Session
        echo "\n--- Session 檢查 ---\n";
        echo "Session 用戶: " . (isset($_SESSION['user']) ? json_encode($_SESSION['user'], JSON_UNESCAPED_UNICODE) : '未設置') . "\n";
        echo "Session 認證 Token: " . (isset($_SESSION['auth_token']) ? '已設置' : '未設置') . "\n";
        
        // 測試 isLoggedIn() 方法
        echo "\n--- 測試 isLoggedIn() 方法 ---\n";
        $isLoggedIn = $auth->isLoggedIn();
        echo "登入狀態: " . ($isLoggedIn ? '已登入' : '未登入') . "\n";
    }
    
    echo "\n=== 測試完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 測試失敗: " . $e->getMessage() . "\n";
    echo "錯誤詳情: " . $e->getTraceAsString() . "\n";
}
?>
