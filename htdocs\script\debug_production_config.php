<?php
/**
 * 檢查生產環境配置
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

echo "=== 生產環境配置檢查 ===\n";

// 模擬生產環境域名
$_SERVER['HTTP_HOST'] = 'attendance.app.tn';

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "當前環境: " . (defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未定義') . "\n";
echo "DEBUG_MODE: " . (defined('DEBUG_MODE') ? (DEBUG_MODE ? 'true' : 'false') : '未定義') . "\n";
echo "LOG_ERRORS: " . (defined('LOG_ERRORS') ? (LOG_ERRORS ? 'true' : 'false') : '未定義') . "\n";
echo "LOG_PATH: " . (defined('LOG_PATH') ? LOG_PATH : '未定義') . "\n";

// 檢查 LOG_PATH 是否正確
if (defined('LOG_PATH')) {
    echo "LOG_PATH 是否存在: " . (is_dir(LOG_PATH) ? '是' : '否') . "\n";
    echo "LOG_PATH 是否可寫: " . (is_writable(LOG_PATH) ? '是' : '否') . "\n";
}

// 載入 Auth 類別並測試
echo "\n=== 載入 Auth 類別 ===\n";
require_once INCLUDES_PATH . '/functions.php';
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

$auth = new Auth();
echo "Auth 類別載入成功\n";

// 測試登入失敗
echo "\n=== 測試生產環境登入失敗 ===\n";
$result = $auth->login('<EMAIL>', 'wrong_password');
echo "登入結果: " . ($result['success'] ? '成功' : '失敗') . "\n";
echo "訊息: " . $result['message'] . "\n";

// 檢查日誌
echo "\n=== 檢查日誌記錄 ===\n";
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
if (file_exists($authLogFile)) {
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    
    // 查找最新的記錄
    $lastLine = end($lines);
    if (!empty($lastLine)) {
        $data = json_decode($lastLine, true);
        if ($data && $data['user_email'] === '<EMAIL>') {
            echo "✅ 找到生產環境登入失敗記錄\n";
            echo "時間: " . $data['timestamp'] . "\n";
            echo "動作: " . $data['action'] . "\n";
            echo "描述: " . $data['description'] . "\n";
        } else {
            echo "❌ 沒有找到對應的記錄\n";
            echo "最後一行: " . $lastLine . "\n";
        }
    }
} else {
    echo "❌ 日誌文件不存在: " . $authLogFile . "\n";
}

echo "\n測試完成！\n";
?>
