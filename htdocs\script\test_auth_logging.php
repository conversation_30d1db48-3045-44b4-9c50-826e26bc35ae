<?php
// 測試 Auth 類的日誌記錄功能
define('SIGNATTEND_INIT', true);

// 載入配置
require_once 'config/environment.php';
require_once 'includes/functions.php';
require_once 'utils/Auth.php';

echo "=== Auth 類日誌記錄測試 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 創建 Auth 實例
$auth = new Auth();

// 反射獲取 logActivity 方法
$reflectionClass = new ReflectionClass('Auth');
$logActivityMethod = $reflectionClass->getMethod('logActivity');
$logActivityMethod->setAccessible(true);

// 測試 logActivity 方法
echo "\n測試 logActivity 方法...\n";
try {
    $logActivityMethod->invoke($auth, 'test_action', '測試描述', '<EMAIL>');
    echo "✅ logActivity 方法調用成功\n";
} catch (Exception $e) {
    echo "❌ logActivity 方法調用失敗: " . $e->getMessage() . "\n";
}

// 檢查日誌文件
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "\n檢查認證日誌文件...\n";
if (file_exists($authLogFile)) {
    echo "✅ 認證日誌文件存在\n";
    echo "文件大小: " . filesize($authLogFile) . " bytes\n";
    
    // 讀取最後一行
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    $lastLine = end($lines);
    
    echo "最後一行內容: " . $lastLine . "\n";
    
    // 解析 JSON
    $data = json_decode($lastLine, true);
    if ($data && $data['user_email'] === '<EMAIL>') {
        echo "✅ 找到測試記錄\n";
    } else {
        echo "❌ 沒有找到測試記錄\n";
    }
} else {
    echo "❌ 認證日誌文件不存在\n";
}

// 測試直接寫入
echo "\n測試直接寫入認證日誌...\n";
$directLogData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_id' => null,
    'user_email' => '<EMAIL>',
    'action' => 'direct_test',
    'description' => '直接寫入測試',
    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Test Browser'
];

$directLogMessage = json_encode($directLogData, JSON_UNESCAPED_UNICODE) . "\n";

if (file_put_contents($authLogFile, $directLogMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 直接寫入成功\n";
} else {
    echo "❌ 直接寫入失敗\n";
    
    // 嘗試不加鎖寫入
    if (file_put_contents($authLogFile, $directLogMessage, FILE_APPEND)) {
        echo "✅ 不加鎖寫入成功\n";
    } else {
        echo "❌ 不加鎖寫入也失敗\n";
    }
}

echo "\n測試完成！\n";
?>