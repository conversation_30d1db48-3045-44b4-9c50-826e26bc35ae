<?php
/**
 * 測試 AJAX 登入日誌寫入功能
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

echo "=== AJAX 登入日誌測試 ===\n";
echo "LOG_PATH: " . LOG_PATH . "\n";
echo "當前時間: " . date('Y-m-d H:i:s') . "\n\n";

// 檢查認證日誌檔案
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
echo "認證日誌檔案: " . $authLogFile . "\n";
echo "檔案是否存在: " . (file_exists($authLogFile) ? '是' : '否') . "\n";

if (file_exists($authLogFile)) {
    $beforeSize = filesize($authLogFile);
    echo "登入前檔案大小: " . $beforeSize . " bytes\n";
} else {
    $beforeSize = 0;
    echo "檔案不存在，將會創建\n";
}

echo "\n=== 模擬 AJAX 登入請求 ===\n";

// 模擬 AJAX 登入請求
$testEmail = 'ajax_test_' . time() . '@example.com';
$testPassword = 'test123';

echo "測試郵箱: " . $testEmail . "\n";
echo "測試密碼: [已設定]\n";

// 執行登入（預期會失敗，因為用戶不存在）
echo "\n開始執行 auth->login()...\n";
$result = $auth->login($testEmail, $testPassword, false);
echo "登入結果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";

// 檢查日誌檔案變化
if (file_exists($authLogFile)) {
    $afterSize = filesize($authLogFile);
    echo "\n登入後檔案大小: " . $afterSize . " bytes\n";
    echo "檔案大小變化: " . ($afterSize - $beforeSize) . " bytes\n";
    
    if ($afterSize > $beforeSize) {
        echo "✅ 日誌寫入成功！\n";
        
        // 讀取最後一行日誌
        $logContent = file_get_contents($authLogFile);
        $lines = explode("\n", trim($logContent));
        $lastLine = end($lines);
        
        echo "\n最新日誌記錄:\n";
        echo $lastLine . "\n";
        
        // 解析 JSON
        $logData = json_decode($lastLine, true);
        if ($logData) {
            echo "\n解析後的日誌數據:\n";
            echo "- 時間戳: " . ($logData['timestamp'] ?? 'N/A') . "\n";
            echo "- 用戶郵箱: " . ($logData['user_email'] ?? 'N/A') . "\n";
            echo "- 動作: " . ($logData['action'] ?? 'N/A') . "\n";
            echo "- 描述: " . ($logData['description'] ?? 'N/A') . "\n";
            echo "- IP 地址: " . ($logData['ip_address'] ?? 'N/A') . "\n";
        }
    } else {
        echo "❌ 日誌寫入失敗！檔案大小沒有變化\n";
    }
} else {
    echo "❌ 日誌檔案仍然不存在\n";
}

echo "\n=== 測試 Auth::logActivity 方法 ===\n";

// 直接測試 logActivity 方法
$reflection = new ReflectionClass($auth);
$logActivityMethod = $reflection->getMethod('logActivity');
$logActivityMethod->setAccessible(true);

$beforeDirectSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
echo "直接測試前檔案大小: " . $beforeDirectSize . " bytes\n";

$directResult = $logActivityMethod->invoke($auth, 'test_direct', '直接測試日誌寫入', $testEmail);
echo "直接調用結果: " . ($directResult ? '成功' : '失敗') . "\n";

$afterDirectSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
echo "直接測試後檔案大小: " . $afterDirectSize . " bytes\n";
echo "直接測試大小變化: " . ($afterDirectSize - $beforeDirectSize) . " bytes\n";

if ($afterDirectSize > $beforeDirectSize) {
    echo "✅ 直接調用 logActivity 成功！\n";
} else {
    echo "❌ 直接調用 logActivity 失敗！\n";
}

echo "\n=== 檢查檔案權限 ===\n";
echo "LOG_PATH 是否可寫: " . (is_writable(LOG_PATH) ? '是' : '否') . "\n";
if (file_exists($authLogFile)) {
    echo "認證日誌檔案是否可寫: " . (is_writable($authLogFile) ? '是' : '否') . "\n";
}

echo "\n=== 測試完成 ===\n";
?>
