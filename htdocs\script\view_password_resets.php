<?php
/**
 * 查看密碼重設記錄
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入密碼重設類別
require_once UTILS_PATH . '/PasswordReset.php';

$passwordReset = new PasswordReset();

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密碼重設記錄</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert-info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 密碼重設記錄</h1>
        
        <?php
        // 獲取統計信息
        $stats = $passwordReset->getResetStats();
        
        if ($stats) {
            echo '<div class="stats-grid">';
            echo '<div class="stat-card">';
            echo '<div class="stat-number">' . $stats['total_resets'] . '</div>';
            echo '<div class="stat-label">總重設次數</div>';
            echo '</div>';
            
            echo '<div class="stat-card">';
            echo '<div class="stat-number">' . ($stats['unique_users'] ?? 0) . '</div>';
            echo '<div class="stat-label">不重複用戶</div>';
            echo '</div>';
            
            echo '<div class="stat-card">';
            echo '<div class="stat-number">' . ($stats['last_reset'] ? date('m-d H:i', strtotime($stats['last_reset'])) : 'N/A') . '</div>';
            echo '<div class="stat-label">最後重設</div>';
            echo '</div>';
            echo '</div>';
        }
        ?>
        
        <div class="alert alert-info">
            <strong>說明：</strong>此頁面顯示所有密碼重設活動記錄，包括成功和失敗的嘗試。
        </div>
    </div>

    <div class="container">
        <h2>📋 最近的密碼重設記錄</h2>
        
        <?php
        $recentResets = $passwordReset->getRecentResets(50);
        
        if (!empty($recentResets)) {
            echo '<table>';
            echo '<thead>';
            echo '<tr>';
            echo '<th>序號</th>';
            echo '<th>用戶郵箱</th>';
            echo '<th>IP 地址</th>';
            echo '<th>用戶代理</th>';
            echo '<th>重設時間</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($recentResets as $index => $reset) {
                echo '<tr>';
                echo '<td>' . ($index + 1) . '</td>';
                echo '<td>' . htmlspecialchars($reset['user_email']) . '</td>';
                echo '<td>' . htmlspecialchars($reset['ip_address']) . '</td>';
                echo '<td>' . htmlspecialchars(substr($reset['user_agent'] ?? '', 0, 50)) . '...</td>';
                echo '<td>' . $reset['created_at'] . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        } else {
            echo '<p>暫無密碼重設記錄。</p>';
        }
        ?>
    </div>

    <div class="container">
        <h2>🔍 按用戶查詢</h2>
        
        <form method="GET" style="margin-bottom: 20px;">
            <input type="email" name="email" placeholder="輸入用戶郵箱" 
                   value="<?= htmlspecialchars($_GET['email'] ?? '') ?>" 
                   style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 300px;">
            <button type="submit" class="btn">查詢</button>
            <?php if (isset($_GET['email'])): ?>
                <a href="?" class="btn" style="background: #6c757d;">清除</a>
            <?php endif; ?>
        </form>
        
        <?php
        if (isset($_GET['email']) && !empty($_GET['email'])) {
            $email = $_GET['email'];
            $emailStats = $passwordReset->getResetStats($email);
            
            if ($emailStats && $emailStats['total_resets'] > 0) {
                echo '<div class="alert alert-info">';
                echo '<strong>' . htmlspecialchars($email) . '</strong> 的重設記錄：<br>';
                echo '重設次數：' . $emailStats['total_resets'] . '<br>';
                echo '最後重設：' . ($emailStats['last_reset'] ?? 'N/A');
                echo '</div>';
            } else {
                echo '<div class="alert alert-info">';
                echo '找不到 <strong>' . htmlspecialchars($email) . '</strong> 的重設記錄。';
                echo '</div>';
            }
        }
        ?>
    </div>

    <div class="container">
        <h2>🛠️ 管理工具</h2>
        
        <div style="text-align: center;">
            <a href="pages/forgot-password.php" class="btn success">🔐 忘記密碼頁面</a>
            <a href="view_login_logs.php" class="btn">📊 登入記錄</a>
            <a href="demo_buttons_verification.php" class="btn">🎯 演示功能</a>
            <a href="test_forgot_password.php" class="btn" style="background: #ffc107; color: #000;">🧪 測試功能</a>
        </div>
    </div>

    <div class="container">
        <h2>📈 使用統計</h2>
        
        <?php
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // 按日期統計
            $stmt = $pdo->query("
                SELECT DATE(created_at) as reset_date, COUNT(*) as count
                FROM password_reset_logs 
                GROUP BY DATE(created_at) 
                ORDER BY reset_date DESC 
                LIMIT 7
            ");
            $dailyStats = $stmt->fetchAll();
            
            if (!empty($dailyStats)) {
                echo '<h3>最近 7 天的重設次數</h3>';
                echo '<table style="width: auto;">';
                echo '<tr><th>日期</th><th>重設次數</th></tr>';
                
                foreach ($dailyStats as $stat) {
                    echo '<tr>';
                    echo '<td>' . $stat['reset_date'] . '</td>';
                    echo '<td>' . $stat['count'] . '</td>';
                    echo '</tr>';
                }
                
                echo '</table>';
            }
            
        } catch (PDOException $e) {
            echo '<p>無法載入統計信息。</p>';
        }
        ?>
    </div>
</body>
</html>
