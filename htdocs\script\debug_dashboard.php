<?php
/**
 * Dashboard調試頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

// 檢查登入狀態（但不強制要求）
$auth = new Auth();
$isLoggedIn = $auth->isLoggedIn();
$user = $auth->getCurrentUser();

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard調試頁面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Tailwind配置
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .status-ok { color: #059669; }
        .status-error { color: #dc2626; }
        .status-warning { color: #d97706; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center mb-8 text-gray-800">Dashboard調試頁面</h1>
        
        <!-- 環境信息 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">環境信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <strong>伺服器名稱:</strong> <?= $_SERVER['SERVER_NAME'] ?? '未知' ?>
                </div>
                <div>
                    <strong>HTTP主機:</strong> <?= $_SERVER['HTTP_HOST'] ?? '未知' ?>
                </div>
                <div>
                    <strong>是否正式環境:</strong> 
                    <span class="<?= strpos($_SERVER['HTTP_HOST'] ?? '', 'attendance.app.tn') !== false ? 'status-ok' : 'status-warning' ?>">
                        <?= strpos($_SERVER['HTTP_HOST'] ?? '', 'attendance.app.tn') !== false ? '是' : '否' ?>
                    </span>
                </div>
                <div>
                    <strong>調試模式:</strong> 
                    <span class="<?= defined('DEBUG_MODE') && DEBUG_MODE ? 'status-warning' : 'status-ok' ?>">
                        <?= defined('DEBUG_MODE') && DEBUG_MODE ? '開啟' : '關閉' ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- 登入狀態 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">登入狀態</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <strong>登入狀態:</strong> 
                    <span class="<?= $isLoggedIn ? 'status-ok' : 'status-error' ?>">
                        <?= $isLoggedIn ? '已登入' : '未登入' ?>
                    </span>
                </div>
                <?php if ($isLoggedIn && $user): ?>
                <div>
                    <strong>用戶ID:</strong> <?= substr($user['id'], 0, 8) ?>...
                </div>
                <div>
                    <strong>用戶郵箱:</strong> <?= $user['email'] ?>
                </div>
                <?php endif; ?>
            </div>
            
            <?php if (!$isLoggedIn): ?>
            <div class="mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded">
                <p class="text-yellow-800">
                    <strong>注意:</strong> 您尚未登入。
                    <a href="pages/login.php" class="text-blue-600 underline">點擊這裡登入</a>
                </p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- CDN測試 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">CDN和資源測試</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-white rounded border">
                    <h3 class="font-bold mb-2">Tailwind CSS</h3>
                    <p id="tailwind-status" class="status-warning">檢測中...</p>
                </div>
                
                <div class="p-4 bg-white rounded border">
                    <h3 class="font-bold mb-2">QRCode庫</h3>
                    <p id="qrcode-status" class="status-warning">檢測中...</p>
                </div>
                
                <div class="p-4 bg-white rounded border">
                    <h3 class="font-bold mb-2">Html5Qrcode庫</h3>
                    <p id="html5qrcode-status" class="status-warning">檢測中...</p>
                </div>
            </div>
        </div>
        
        <!-- 功能測試 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">功能測試</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-white rounded border">
                    <h3 class="font-bold mb-2">QR Code生成測試</h3>
                    <div id="qr-test-area">
                        <button id="generate-qr" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600" disabled>
                            生成測試QR Code
                        </button>
                        <div id="qr-result" class="mt-2"></div>
                    </div>
                </div>
                
                <div class="p-4 bg-white rounded border">
                    <h3 class="font-bold mb-2">樣式測試</h3>
                    <div class="space-y-2">
                        <div class="bg-blue-500 text-white p-2 rounded">藍色背景</div>
                        <div class="bg-green-500 text-white p-2 rounded">綠色背景</div>
                        <div class="bg-red-500 text-white p-2 rounded">紅色背景</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 錯誤日誌 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">瀏覽器錯誤日誌</h2>
            <div id="error-log" class="bg-white p-4 rounded border min-h-[100px] font-mono text-sm">
                <p class="text-gray-500">等待錯誤信息...</p>
            </div>
        </div>
        
        <!-- 操作按鈕 -->
        <div class="debug-section">
            <h2 class="text-2xl font-bold mb-4">操作</h2>
            <div class="space-x-4">
                <a href="pages/dashboard.php" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600 inline-block">
                    訪問實際Dashboard
                </a>
                <a href="pages/login.php" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 inline-block">
                    登入頁面
                </a>
                <button onclick="location.reload()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600">
                    重新載入
                </button>
            </div>
        </div>
    </div>

    <!-- 載入外部庫 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"></script>
    
    <script>
        // 錯誤收集
        const errorLog = document.getElementById('error-log');
        const errors = [];
        
        // 捕獲所有錯誤
        window.addEventListener('error', function(e) {
            const error = `[${new Date().toLocaleTimeString()}] ${e.message} (${e.filename}:${e.lineno})`;
            errors.push(error);
            updateErrorLog();
        });
        
        // 捕獲Promise錯誤
        window.addEventListener('unhandledrejection', function(e) {
            const error = `[${new Date().toLocaleTimeString()}] Promise rejected: ${e.reason}`;
            errors.push(error);
            updateErrorLog();
        });
        
        function updateErrorLog() {
            if (errors.length === 0) {
                errorLog.innerHTML = '<p class="text-gray-500">沒有錯誤</p>';
            } else {
                errorLog.innerHTML = errors.map(error => `<div class="text-red-600">${error}</div>`).join('');
            }
        }
        
        // 檢測庫加載狀態
        function checkLibraries() {
            // 檢測Tailwind
            const tailwindStatus = document.getElementById('tailwind-status');
            if (typeof tailwind !== 'undefined') {
                tailwindStatus.textContent = '✅ 加載成功';
                tailwindStatus.className = 'status-ok';
            } else {
                tailwindStatus.textContent = '❌ 加載失敗';
                tailwindStatus.className = 'status-error';
            }
            
            // 檢測QRCode
            const qrcodeStatus = document.getElementById('qrcode-status');
            if (typeof QRCode !== 'undefined') {
                qrcodeStatus.textContent = '✅ 加載成功';
                qrcodeStatus.className = 'status-ok';
                document.getElementById('generate-qr').disabled = false;
            } else {
                qrcodeStatus.textContent = '❌ 加載失敗';
                qrcodeStatus.className = 'status-error';
            }
            
            // 檢測Html5Qrcode
            const html5qrcodeStatus = document.getElementById('html5qrcode-status');
            if (typeof Html5Qrcode !== 'undefined') {
                html5qrcodeStatus.textContent = '✅ 加載成功';
                html5qrcodeStatus.className = 'status-ok';
            } else {
                html5qrcodeStatus.textContent = '❌ 加載失敗';
                html5qrcodeStatus.className = 'status-error';
            }
        }
        
        // QR Code生成測試
        document.getElementById('generate-qr').addEventListener('click', function() {
            const resultDiv = document.getElementById('qr-result');
            
            if (typeof QRCode !== 'undefined') {
                resultDiv.innerHTML = '<div id="qrcode"></div>';
                const qr = new QRCode(document.getElementById('qrcode'), {
                    text: 'Dashboard測試QR Code - ' + new Date().toLocaleString(),
                    width: 128,
                    height: 128
                });
                resultDiv.innerHTML += '<p class="text-green-600 mt-2">✅ QR Code生成成功</p>';
            } else {
                resultDiv.innerHTML = '<p class="text-red-600">❌ QRCode庫未加載</p>';
            }
        });
        
        // 延遲檢測以確保所有庫都有時間加載
        setTimeout(checkLibraries, 1000);
        
        // 定期更新錯誤日誌
        setInterval(updateErrorLog, 1000);
    </script>
</body>
</html>
