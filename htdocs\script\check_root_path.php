<?php
echo "檢查 ROOT_PATH 定義...\n";

// 直接檢查文件內容
$configFile = __DIR__ . '/config/config.php';
echo "配置文件路徑: " . $configFile . "\n";

if (file_exists($configFile)) {
    $content = file_get_contents($configFile);
    
    // 查找 ROOT_PATH 定義
    if (preg_match("/define\s*\(\s*['\"]ROOT_PATH['\"],\s*(.+?)\s*\)/", $content, $matches)) {
        echo "找到 ROOT_PATH 定義: " . $matches[1] . "\n";
        
        // 評估表達式
        $expression = $matches[1];
        echo "表達式: " . $expression . "\n";
        
        // 手動計算
        if (strpos($expression, 'dirname(dirname(__FILE__))') !== false) {
            $calculatedPath = dirname(dirname($configFile));
            echo "計算結果: " . $calculatedPath . "\n";
        }
    } else {
        echo "未找到 ROOT_PATH 定義\n";
    }
    
    // 檢查是否有其他 ROOT_PATH 定義
    $lines = explode("\n", $content);
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'ROOT_PATH') !== false && strpos($line, 'define') !== false) {
            echo "第 " . ($lineNum + 1) . " 行: " . trim($line) . "\n";
        }
    }
} else {
    echo "配置文件不存在\n";
}

// 測試實際的 PHP 計算
echo "\n實際 PHP 計算:\n";
echo "__FILE__: " . __FILE__ . "\n";
echo "dirname(__FILE__): " . dirname(__FILE__) . "\n";
echo "dirname(dirname(__FILE__)): " . dirname(dirname(__FILE__)) . "\n";

// 模擬配置文件中的計算
$configFileDir = dirname(__FILE__) . '/config';
echo "\n模擬配置文件目錄: " . $configFileDir . "\n";
echo "dirname(config目錄): " . dirname($configFileDir) . "\n";
echo "dirname(dirname(config目錄)): " . dirname(dirname($configFileDir)) . "\n";
?>
