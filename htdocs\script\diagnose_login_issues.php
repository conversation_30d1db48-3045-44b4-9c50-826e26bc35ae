<?php
/**
 * 登入問題全面診斷工具
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';
require_once UTILS_PATH . '/PasswordReset.php';

echo "=== 登入問題全面診斷 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 1. 檢查環境配置
echo "1. 環境配置檢查\n";
echo "-------------------\n";
echo "資料庫主機: " . (defined('DB_HOST') ? DB_HOST : '未定義') . "\n";
echo "資料庫名稱: " . (defined('DB_NAME') ? DB_NAME : '未定義') . "\n";
echo "資料庫用戶: " . (defined('DB_USER') ? DB_USER : '未定義') . "\n";
echo "資料庫密碼: " . (defined('DB_PASS') ? '已設置' : '未設置') . "\n";
echo "調試模式: " . (defined('DEBUG_MODE') && DEBUG_MODE ? '開啟' : '關閉') . "\n\n";

// 2. 檢查資料庫連接
echo "2. 資料庫連接檢查\n";
echo "-------------------\n";
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ 資料庫連接成功\n";
    
    // 檢查用戶表
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "✅ 用戶表存在，共有 $userCount 個用戶\n";
    
} catch (Exception $e) {
    echo "❌ 資料庫連接失敗: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. 檢查測試用戶
echo "3. 測試用戶檢查\n";
echo "-------------------\n";
$testUsers = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
];

foreach ($testUsers as $email) {
    try {
        $stmt = $pdo->prepare("SELECT id, email, created_at FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ $email - 存在 (ID: " . substr($user['id'], 0, 8) . "...)\n";
        } else {
            echo "❌ $email - 不存在\n";
        }
    } catch (Exception $e) {
        echo "❌ $email - 檢查失敗: " . $e->getMessage() . "\n";
    }
}
echo "\n";

// 4. 測試忘記密碼功能
echo "4. 忘記密碼功能測試\n";
echo "-------------------\n";
try {
    $passwordReset = new PasswordReset();
    $testEmail = '<EMAIL>';
    
    $resetResult = $passwordReset->handleForgotPassword($testEmail);
    
    if ($resetResult['success']) {
        echo "✅ 忘記密碼功能正常\n";
        echo "   臨時密碼: " . $resetResult['temp_password'] . "\n";
        $tempPassword = $resetResult['temp_password'];
    } else {
        echo "❌ 忘記密碼功能失敗: " . $resetResult['message'] . "\n";
        $tempPassword = null;
    }
} catch (Exception $e) {
    echo "❌ 忘記密碼功能異常: " . $e->getMessage() . "\n";
    $tempPassword = null;
}
echo "\n";

// 5. 測試登入功能
echo "5. 登入功能測試\n";
echo "-------------------\n";
try {
    $auth = new Auth();
    
    // 清除現有 Session
    session_unset();
    
    if ($tempPassword) {
        echo "測試臨時密碼登入...\n";
        $loginResult = $auth->login('<EMAIL>', $tempPassword);
        
        if ($loginResult['success']) {
            echo "✅ 臨時密碼登入成功\n";
            
            // 檢查登入狀態
            $isLoggedIn = $auth->isLoggedIn();
            echo "   登入狀態檢查: " . ($isLoggedIn ? '✅ 正常' : '❌ 失敗') . "\n";
            
            // 檢查 Session
            echo "   Session 檢查:\n";
            echo "     - user: " . (isset($_SESSION['user']) ? '✅' : '❌') . "\n";
            echo "     - auth_token: " . (isset($_SESSION['auth_token']) ? '✅' : '❌') . "\n";
            echo "     - profile: " . (isset($_SESSION['profile']) ? '✅' : '❌') . "\n";
            
        } else {
            echo "❌ 臨時密碼登入失敗: " . $loginResult['message'] . "\n";
        }
    }
    
    // 測試訪客登入
    echo "\n測試訪客登入...\n";
    session_unset();
    
    // 嘗試一些常見的測試密碼
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'demo123'],
        ['email' => '<EMAIL>', 'password' => 'test123'],
        ['email' => '<EMAIL>', 'password' => 'guest123'],
        ['email' => '<EMAIL>', 'password' => 'admin123']
    ];
    
    foreach ($testCredentials as $cred) {
        $loginResult = $auth->login($cred['email'], $cred['password']);
        
        if ($loginResult['success']) {
            echo "✅ " . $cred['email'] . " (密碼: " . $cred['password'] . ") - 登入成功\n";
            
            $isLoggedIn = $auth->isLoggedIn();
            echo "   登入狀態: " . ($isLoggedIn ? '✅ 正常' : '❌ 失敗') . "\n";
            break;
        } else {
            echo "❌ " . $cred['email'] . " (密碼: " . $cred['password'] . ") - " . $loginResult['message'] . "\n";
        }
        
        session_unset();
    }
    
} catch (Exception $e) {
    echo "❌ 登入功能測試異常: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 檢查日誌文件
echo "6. 日誌文件檢查\n";
echo "-------------------\n";
$logPath = LOG_PATH;
echo "日誌路徑: $logPath\n";

if (is_dir($logPath)) {
    echo "✅ 日誌目錄存在\n";
    
    $logFiles = [
        'auth_' . date('Y-m-d') . '.log',
        'api_' . date('Y-m-d') . '.log',
        'error_' . date('Y-m-d') . '.log'
    ];
    
    foreach ($logFiles as $logFile) {
        $fullPath = $logPath . '/' . $logFile;
        if (file_exists($fullPath)) {
            $size = filesize($fullPath);
            echo "✅ $logFile - 存在 ($size bytes)\n";
        } else {
            echo "❌ $logFile - 不存在\n";
        }
    }
} else {
    echo "❌ 日誌目錄不存在: $logPath\n";
}
echo "\n";

// 7. 檢查頁面文件
echo "7. 頁面文件檢查\n";
echo "-------------------\n";
$pageFiles = [
    'pages/login.php',
    'pages/forgot-password.php',
    'pages/dashboard.php'
];

foreach ($pageFiles as $pageFile) {
    $fullPath = __DIR__ . '/' . $pageFile;
    if (file_exists($fullPath)) {
        echo "✅ $pageFile - 存在\n";
    } else {
        echo "❌ $pageFile - 不存在\n";
    }
}
echo "\n";

echo "=== 診斷完成 ===\n";
echo "如果發現問題，請根據上述結果進行修復。\n";
?>
