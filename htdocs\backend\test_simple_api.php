<?php
// 測試簡化的會議 API

$url = 'http://localhost/SignAttend/backend/simple_meeting_api.php';

// 測試數據
$testData = [
    'name' => '測試會議 - ' . date('Y-m-d H:i:s'),
    'description' => '這是一個測試會議',
    'location' => '會議室A',
    'start_date' => '2025-06-20 10:00:00',
    'end_date' => '2025-06-20 12:00:00',
    'created_by' => '9fda4357-f5bf-4ac1-a502-3dddad8dc8d3'
];

echo "=== 測試簡化會議 API ===\n";
echo "URL: $url\n";
echo "測試數據: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

// 測試 POST 請求
echo "=== POST 請求測試 ===\n";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "cURL Error: " . ($curlError ?: 'None') . "\n";
echo "Response: $response\n\n";

// 解析響應
$responseData = json_decode($response, true);
if ($responseData) {
    echo "解析後的響應:\n";
    echo json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
    
    if ($responseData['status'] === 'success') {
        echo "✅ 會議創建成功！\n";
        $meetingId = $responseData['data']['id'] ?? null;
        if ($meetingId) {
            echo "會議 ID: $meetingId\n";
        }
    } else {
        echo "❌ 會議創建失敗: " . ($responseData['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ 無法解析響應\n";
}

// 測試 GET 請求
echo "\n=== GET 請求測試 ===\n";
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30
]);

$getResponse = curl_exec($ch);
$getHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "GET HTTP Code: $getHttpCode\n";
echo "GET Response: $getResponse\n";

$getResponseData = json_decode($getResponse, true);
if ($getResponseData && $getResponseData['status'] === 'success') {
    echo "✅ 會議列表獲取成功！\n";
    echo "會議數量: " . ($getResponseData['count'] ?? 0) . "\n";
} else {
    echo "❌ 會議列表獲取失敗\n";
}
?>
