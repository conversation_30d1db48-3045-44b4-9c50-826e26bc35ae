<?php
/**
 * 簡化的首頁測試 - 逐步載入組件
 */

// 步驟 1: 基本設定
define('SIGNATTEND_INIT', true);

// 步驟 2: 啟動 Session (在載入配置前)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 步驟 3: 載入環境配置
require_once __DIR__ . '/config/environment.php';

// 步驟 4: 載入共用函數
require_once __DIR__ . '/includes/functions.php';

// 步驟 5: 初始化變數
$recentMeetings = [];
$auth = null;
$apiClient = null;

// 步驟 6: 嘗試載入 Auth 類別
try {
    require_once __DIR__ . '/utils/ApiClient.php';
    require_once __DIR__ . '/utils/Auth.php';
    $auth = new Auth();
} catch (Exception $e) {
    // 如果載入失敗，創建一個簡單的 mock 對象
    $auth = new class {
        public function isLoggedIn() { return false; }
        public function getCurrentUser() { return null; }
        public function getCurrentProfile() { return null; }
    };
}

// 步驟 7: 獲取會議資料
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 簡化的查詢
    $stmt = $pdo->prepare("SELECT id, name, description, created_at FROM meetings ORDER BY created_at DESC LIMIT 3");
    $stmt->execute();
    $recentMeetings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // 使用預設資料
    $recentMeetings = [
        [
            'id' => 1,
            'name' => '數位轉型研討會',
            'description' => '探討企業數位轉型的策略與實務',
            'created_at' => date('Y-m-d H:i:s')
        ]
    ];
}

// 步驟 8: 設定頁面變數
$pageTitle = APP_NAME . ' - 智慧簽到系統';
$pageDescription = '快速、便捷的研討會報到體驗，自動產生會議QR Code和報到流程';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    <title><?= htmlspecialchars($pageTitle) ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- 導航列 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-qr-code-scan me-2"></i>
                <?= htmlspecialchars(APP_NAME) ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <?php if ($auth->isLoggedIn()): ?>
                    <a class="nav-link" href="pages/dashboard.php">
                        <i class="bi bi-speedometer2 me-1"></i>儀表板
                    </a>
                    <a class="nav-link" href="pages/logout.php">
                        <i class="bi bi-box-arrow-right me-1"></i>登出
                    </a>
                <?php else: ?>
                    <a class="nav-link" href="pages/login.php">
                        <i class="bi bi-box-arrow-in-right me-1"></i>登入
                    </a>
                    <a class="nav-link" href="pages/register.php">
                        <i class="bi bi-person-plus me-1"></i>註冊
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        快速、便捷的<br>研討會報到體驗
                    </h1>
                    <p class="lead mb-4">
                        自動產生會議QR Code和報到流程，參與者只需掃描QR碼或輸入報到碼，讓會議管理更輕鬆。
                    </p>
                    <div class="d-flex gap-3">
                        <a href="pages/register.php" class="btn btn-light btn-lg">
                            <i class="bi bi-rocket-takeoff me-2"></i>立即開始
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <i class="bi bi-qr-code display-1"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">功能特色</h2>
                <p class="text-muted fs-5">簡單、快速、可靠的簽到解決方案</p>
            </div>

            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm feature-card">
                        <div class="card-body text-center p-4">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                <i class="bi bi-qr-code fs-1 text-primary"></i>
                            </div>
                            <h5 class="fw-semibold mb-2">QR Code 簽到</h5>
                            <p class="text-muted small">掃描即可完成簽到，無需排隊等候</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm feature-card">
                        <div class="card-body text-center p-4">
                            <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                <i class="bi bi-lightning-charge fs-1 text-success"></i>
                            </div>
                            <h5 class="fw-semibold mb-2">快速建立</h5>
                            <p class="text-muted small">幾分鐘內建立會議和簽到系統</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm feature-card">
                        <div class="card-body text-center p-4">
                            <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                <i class="bi bi-graph-up fs-1 text-info"></i>
                            </div>
                            <h5 class="fw-semibold mb-2">即時統計</h5>
                            <p class="text-muted small">即時查看簽到狀況和統計資料</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm feature-card">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex p-3 mb-3">
                                <i class="bi bi-download fs-1 text-warning"></i>
                            </div>
                            <h5 class="fw-semibold mb-2">資料匯出</h5>
                            <p class="text-muted small">支援 Excel、PDF 等格式匯出</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 最近會議 -->
    <?php if (!empty($recentMeetings)): ?>
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">最近會議</h2>
                <p class="text-muted">查看最新的會議活動</p>
            </div>
            
            <div class="row g-4">
                <?php foreach ($recentMeetings as $meeting): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title"><?= htmlspecialchars($meeting['name']) ?></h5>
                            <p class="card-text text-muted"><?= htmlspecialchars($meeting['description'] ?? '') ?></p>
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                <?= date('Y-m-d H:i', strtotime($meeting['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- 頁尾 -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-qr-code-scan me-2 text-primary"></i>
                        <span class="fw-semibold"><?= htmlspecialchars(APP_NAME) ?></span>
                        <span class="ms-2 text-muted">v<?= htmlspecialchars(APP_VERSION) ?></span>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <span class="text-muted">
                        &copy; <?= date('Y') ?> SignAttend Team
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
