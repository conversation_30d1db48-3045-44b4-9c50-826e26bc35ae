<?php
/**
 * 最終錯誤診斷 - 模擬首頁載入過程
 */

echo "<h1>🔍 最終錯誤診斷</h1>";
echo "<div style='font-family: Arial; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 8px;'>";

// 清除之前的錯誤
error_reporting(E_ALL);
ini_set('display_errors', 1);

$step = 1;

try {
    echo "<h2>步驟 $step: 定義常數</h2>";
    define('SIGNATTEND_INIT', true);
    echo "✅ 常數定義成功<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 啟動 Session</h2>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session 啟動成功<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 載入環境配置</h2>";
    require_once __DIR__ . '/config/environment.php';
    echo "✅ 環境配置載入成功<br>";
    echo "環境: " . APP_ENVIRONMENT . "<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 載入共用函數</h2>";
    require_once __DIR__ . '/includes/functions.php';
    echo "✅ 共用函數載入成功<br>";
    echo "h() 函數測試: " . h('<test>') . "<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 資料庫連接和查詢</h2>";
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 資料庫連接成功<br>";
    
    // 檢查 meetings 表結構
    $stmt = $pdo->query("DESCRIBE meetings");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "meetings 表欄位: " . implode(', ', $columns) . "<br>";
    
    // 測試查詢
    $selectFields = ['m.id', 'm.name'];
    if (in_array('description', $columns)) $selectFields[] = 'm.description';
    if (in_array('start_time', $columns)) $selectFields[] = 'm.start_time';
    if (in_array('end_time', $columns)) $selectFields[] = 'm.end_time';
    if (in_array('location', $columns)) $selectFields[] = 'm.location';
    if (in_array('created_at', $columns)) $selectFields[] = 'm.created_at';
    
    $sql = "SELECT " . implode(', ', $selectFields) . ", 
            COUNT(a.id) as total_attendees,
            COUNT(CASE WHEN a.checked_in = 1 THEN 1 END) as checked_in_count
            FROM meetings m
            LEFT JOIN attendees a ON m.id = a.meeting_id
            GROUP BY " . implode(', ', $selectFields) . "
            ORDER BY " . (in_array('created_at', $columns) ? 'm.created_at' : 'm.id') . " DESC
            LIMIT 3";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $recentMeetings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ 會議資料查詢成功，找到 " . count($recentMeetings) . " 個會議<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 載入 ApiClient</h2>";
    require_once __DIR__ . '/utils/ApiClient.php';
    $apiClient = new ApiClient();
    echo "✅ ApiClient 載入成功<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 載入 Auth</h2>";
    require_once __DIR__ . '/utils/Auth.php';
    $auth = new Auth();
    echo "✅ Auth 載入成功<br>";
    echo "登入狀態: " . ($auth->isLoggedIn() ? '已登入' : '未登入') . "<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 測試記住我功能</h2>";
    if (!$auth->isLoggedIn()) {
        $auth->checkRememberMe();
    }
    echo "✅ 記住我功能測試成功<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 測試 API 連線</h2>";
    $apiStatus = $apiClient->checkConnection();
    echo "✅ API 連線測試完成，狀態: " . ($apiStatus ? '正常' : '異常') . "<br><br>";
    $step++;
    
    echo "<h2>步驟 $step: 測試頁面變數</h2>";
    $pageTitle = APP_NAME . ' - 智慧簽到系統';
    $pageDescription = APP_DESCRIPTION;
    echo "✅ 頁面變數設定成功<br>";
    echo "標題: " . h($pageTitle) . "<br>";
    echo "描述: " . h($pageDescription) . "<br><br>";
    $step++;
    
    echo "<h2>🎉 所有步驟完成！</h2>";
    echo "<p style='color: green; font-weight: bold;'>首頁載入過程沒有發現錯誤。</p>";
    echo "<p>如果原始首頁仍有問題，可能是 HTML 輸出部分的問題。</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ 步驟 $step 發生錯誤</h2>";
    echo "<p style='color: red; font-weight: bold;'>錯誤訊息: " . $e->getMessage() . "</p>";
    echo "<p>錯誤文件: " . $e->getFile() . "</p>";
    echo "<p>錯誤行號: " . $e->getLine() . "</p>";
    echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 3px;'>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>❌ 步驟 $step 發生致命錯誤</h2>";
    echo "<p style='color: red; font-weight: bold;'>錯誤訊息: " . $e->getMessage() . "</p>";
    echo "<p>錯誤文件: " . $e->getFile() . "</p>";
    echo "<p>錯誤行號: " . $e->getLine() . "</p>";
    echo "<pre style='background: #f1f1f1; padding: 10px; border-radius: 3px;'>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><h2>🔧 測試連結</h2>";
echo "<p><a href='index.php' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>測試原始首頁</a></p>";
echo "<p><a href='test_homepage.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>測試簡化首頁</a></p>";

echo "</div>";
?>
