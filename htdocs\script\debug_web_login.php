<?php
/**
 * 調試網頁登入問題
 */

// 模擬網頁環境
define('SIGNATTEND_INIT', true);

// 設置錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 載入配置
require_once __DIR__ . '/../config/environment.php';

echo "<h1>🔍 調試網頁登入問題</h1>";
echo "<p>時間: " . date('Y-m-d H:i:s') . "</p>";

// 檢查基本配置
echo "<h2>1. 基本配置檢查</h2>";
echo "<p>LOG_PATH: " . (defined('LOG_PATH') ? LOG_PATH : '未定義') . "</p>";
echo "<p>Session 狀態: " . (session_status() === PHP_SESSION_ACTIVE ? '已啟動' : '未啟動') . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// 載入必要文件
require_once INCLUDES_PATH . '/functions.php';
require_once UTILS_PATH . '/Auth.php';

// 創建 Auth 實例
$auth = new Auth();

echo "<h2>2. 測試資料庫連接</h2>";
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "<p>✅ 資料庫連接成功</p>";
    
    // 檢查測試帳號
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ 測試帳號存在</p>";
        echo "<p>用戶ID: " . substr($user['id'], 0, 8) . "...</p>";
        
        // 驗證密碼
        if (password_verify('aaaaaa', $user['password_hash'])) {
            echo "<p>✅ 密碼驗證正確</p>";
        } else {
            echo "<p>❌ 密碼驗證失敗</p>";
        }
    } else {
        echo "<p>❌ 測試帳號不存在</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ 資料庫連接失敗: " . $e->getMessage() . "</p>";
}

echo "<h2>3. 測試 CSRF Token</h2>";
$csrfToken = $auth->generateCSRFToken();
echo "<p>CSRF Token: " . substr($csrfToken, 0, 10) . "...</p>";
$csrfValid = $auth->validateCSRFToken($csrfToken);
echo "<p>CSRF 驗證: " . ($csrfValid ? '✅ 通過' : '❌ 失敗') . "</p>";

echo "<h2>4. 測試登入方法</h2>";

// 測試 loginWithDatabase
echo "<h3>4.1 測試 loginWithDatabase()</h3>";
$result1 = $auth->loginWithDatabase('<EMAIL>', 'aaaaaa', false);
echo "<p>結果: " . json_encode($result1, JSON_UNESCAPED_UNICODE) . "</p>";

// 清除 Session 以便重新測試
session_unset();

// 測試 login (主方法)
echo "<h3>4.2 測試 login()</h3>";
$result2 = $auth->login('<EMAIL>', 'aaaaaa', false);
echo "<p>結果: " . json_encode($result2, JSON_UNESCAPED_UNICODE) . "</p>";

echo "<h2>5. 檢查 Session 狀態</h2>";
echo "<p>Session 用戶: " . (isset($_SESSION['user']) ? json_encode($_SESSION['user'], JSON_UNESCAPED_UNICODE) : '未設置') . "</p>";
echo "<p>Session 認證 Token: " . (isset($_SESSION['auth_token']) ? '已設置' : '未設置') . "</p>";
echo "<p>登入狀態: " . ($auth->isLoggedIn() ? '✅ 已登入' : '❌ 未登入') . "</p>";

echo "<h2>6. 檢查日誌目錄權限</h2>";
$logDir = LOG_PATH;
if (is_dir($logDir)) {
    echo "<p>✅ 日誌目錄存在: $logDir</p>";
    echo "<p>目錄權限: " . substr(sprintf('%o', fileperms($logDir)), -4) . "</p>";
    echo "<p>可寫入: " . (is_writable($logDir) ? '✅ 是' : '❌ 否') . "</p>";
} else {
    echo "<p>❌ 日誌目錄不存在: $logDir</p>";
}

echo "<h2>7. 模擬 AJAX 請求</h2>";
// 模擬 AJAX 請求環境
$_POST = [
    'ajax_login' => '1',
    'email' => '<EMAIL>',
    'password' => 'aaaaaa',
    'csrf_token' => $csrfToken
];

echo "<p>模擬 POST 數據: " . json_encode($_POST, JSON_UNESCAPED_UNICODE) . "</p>";

// 清除 Session 以便重新測試
session_unset();

// 執行登入
$result3 = $auth->login($_POST['email'], $_POST['password'], false);
echo "<p>AJAX 模擬結果: " . json_encode($result3, JSON_UNESCAPED_UNICODE) . "</p>";

echo "<h2>8. 檢查 API 客戶端狀態</h2>";
// 檢查 API 客戶端
$reflection = new ReflectionClass($auth);
$method = $reflection->getMethod('getApiClient');
$method->setAccessible(true);
$apiClient = $method->invoke($auth);

echo "<p>API 客戶端: " . ($apiClient ? '可用' : '不可用') . "</p>";

if ($apiClient) {
    echo "<p>API 基礎 URL: " . $apiClient->getBaseUrl() . "</p>";
}

echo "<p><strong>調試完成</strong></p>";
?>
