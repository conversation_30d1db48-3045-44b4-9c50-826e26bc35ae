<?php
/**
 * 測試臨時密碼登入問題
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

echo "=== 測試臨時密碼登入問題 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $auth = new Auth();
    
    // 測試用戶和臨時密碼
    $testEmail = '<EMAIL>';
    $tempPassword = 'VyfX8VDN'; // 從上面的測試獲得
    
    echo "=== 測試登入 ===\n";
    echo "郵箱: $testEmail\n";
    echo "臨時密碼: $tempPassword\n\n";
    
    // 清除現有 Session
    session_unset();
    
    echo "1. 執行登入...\n";
    $result = $auth->login($testEmail, $tempPassword);
    
    echo "登入結果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    if ($result['success']) {
        echo "2. 檢查登入狀態...\n";
        
        // 檢查 Session 內容
        echo "Session 內容:\n";
        echo "- user: " . (isset($_SESSION['user']) ? json_encode($_SESSION['user']) : '未設置') . "\n";
        echo "- auth_token: " . (isset($_SESSION['auth_token']) ? '已設置' : '未設置') . "\n";
        echo "- profile: " . (isset($_SESSION['profile']) ? json_encode($_SESSION['profile']) : '未設置') . "\n";
        echo "- login_time: " . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : '未設置') . "\n";
        echo "- last_activity: " . (isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : '未設置') . "\n\n";
        
        // 檢查 isLoggedIn() 方法
        echo "3. 檢查 isLoggedIn() 方法...\n";
        $isLoggedIn = $auth->isLoggedIn();
        echo "isLoggedIn() 結果: " . ($isLoggedIn ? '✅ true' : '❌ false') . "\n\n";
        
        if (!$isLoggedIn) {
            echo "❌ 問題確認：登入成功但 isLoggedIn() 返回 false\n";
            echo "原因：loginWithDatabase() 方法沒有設置 auth_token\n";
            echo "但 isLoggedIn() 方法需要檢查 auth_token 的存在\n\n";
        } else {
            echo "✅ 登入狀態正常\n\n";
        }
        
        // 測試獲取用戶信息
        echo "4. 測試獲取用戶信息...\n";
        $currentUser = $auth->getCurrentUser();
        echo "getCurrentUser(): " . ($currentUser ? json_encode($currentUser) : '無') . "\n";
        
        $currentProfile = $auth->getCurrentProfile();
        echo "getCurrentProfile(): " . ($currentProfile ? json_encode($currentProfile) : '無') . "\n\n";
        
    } else {
        echo "❌ 登入失敗: " . $result['message'] . "\n";
    }
    
    echo "=== 測試完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 測試過程發生錯誤: " . $e->getMessage() . "\n";
}
?>
