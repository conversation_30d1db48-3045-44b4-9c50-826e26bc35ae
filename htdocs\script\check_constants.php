<?php
echo "=== 檢查常數定義狀態 ===\n";

// 檢查常數是否已經定義
$constants = ['SIGNATTEND_INIT', 'ROOT_PATH', 'LOG_PATH', 'APP_ENVIRONMENT'];

foreach ($constants as $const) {
    if (defined($const)) {
        echo $const . " 已定義: " . constant($const) . "\n";
    } else {
        echo $const . " 未定義\n";
    }
}

echo "\n=== 定義 SIGNATTEND_INIT 並載入配置 ===\n";

if (!defined('SIGNATTEND_INIT')) {
    define('SIGNATTEND_INIT', true);
    echo "✓ 定義 SIGNATTEND_INIT\n";
}

// 直接載入 config.php
echo "載入 config.php...\n";
require_once 'config/config.php';

echo "\n=== 載入後的常數值 ===\n";
foreach ($constants as $const) {
    if (defined($const)) {
        echo $const . ": " . constant($const) . "\n";
    } else {
        echo $const . ": 未定義\n";
    }
}

// 測試寫入
echo "\n=== 測試寫入 ===\n";
$testFile = LOG_PATH . '/constants_test_' . date('Y-m-d_H-i-s') . '.log';
$content = date('Y-m-d H:i:s') . " - 常數測試\n";

echo "嘗試寫入: " . $testFile . "\n";
if (file_put_contents($testFile, $content)) {
    echo "✓ 寫入成功\n";
} else {
    echo "✗ 寫入失敗\n";
}
?>
