/**
 * SignAttend 自定義樣式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

/* 全域樣式 */
:root {
    --primary-color: #4f7cff;
    --primary-light: #6b8fff;
    --primary-dark: #3d5fcc;
    --primary-gradient: linear-gradient(135deg, #4f7cff 0%, #6b8fff 100%);
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #ffffff;
    --dark-color: #0f172a;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition: all 0.2s ease-in-out;

    /* Ring 和 Focus 變數 */
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #ffffff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
}

/* 基礎樣式 */
html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--light-color);
    margin: 0 !important;
    padding: 0 !important;
    padding-top: 0 !important;
}

/* 覆蓋 Bootstrap 的預設樣式 */
.container, .container-fluid {
    padding-top: 0 !important;
}

section {
    margin-top: 0 !important;
}

/* 重置樣式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    margin: 0 !important;
    padding: 0 !important;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 2rem;
    padding-top: 40px; /* 增加上邊距 */
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 640px) {
    .container {
        padding: 0 1.5rem;
    }
    .hero-container {
        padding: 0 2.5rem;
        padding-top: 40px; /* 響應式調整 */
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
    .hero-container {
        padding: 0 3rem;
        padding-top: 40px; /* 確保一致性 */
    }
}

@media (min-width: 1400px) {
    .hero-container {
        padding: 0 4rem;
        padding-top: 40px; /* 更大螢幕下保持空間 */
    }
}

/* Hero Section 樣式 */
.hero-section {
    background: var(--primary-gradient);
    color: white;
    min-height: 50vh;
    position: relative;
    overflow: hidden;
    margin: 0 !important;
    padding: 0 !important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding-top: 2rem;
}

.hero-card {
    max-width: 100%;
    width: 100%;
    height: 232px;
    padding: 1.5rem !important;
    aspect-ratio: 635/232;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

@media (min-width: 768px) {
    .hero-card {
        padding: 2rem !important;
    }
}

.hero-mockup {
    position: relative;
    max-width: 635px;
    margin: 0 auto;
}

.hero-card .bg-blue-100 {
    padding: 1.25rem;
    margin-bottom: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hero-card .w-16 {
    width: 3.5rem;
    height: 3.5rem;
}

.hero-card .h-4 {
    height: 0.75rem;
}

.hero-card .h-3 {
    height: 0.625rem;
}

/* Tailwind-like 工具類別 */
.bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.1);
}

.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

.border-white\/20 {
    border-color: rgba(255, 255, 255, 0.2);
}

.text-blue-100 {
    color: #dbeafe;
}

.bg-blue-200 {
    background-color: #bfdbfe;
}

.bg-blue-300 {
    background-color: #93c5fd;
}

.bg-blue-400 {
    background-color: #60a5fa;
}

.bg-blue-500 {
    background-color: #3b82f6;
}

.bg-blue-600 {
    background-color: #2563eb;
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8;
}

.hover\:bg-white\/20:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.hover\:bg-blue-200:hover {
    background-color: #bfdbfe;
}

.text-blue-600 {
    color: #2563eb;
}

.rounded-2xl {
    border-radius: 1rem;
}

.shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.gap-2 {
    gap: 0.5rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-12 {
    gap: 3rem;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: 1.2fr 0.8fr;
    }
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
    margin-top: 40px; /* 添加上邊距 */
}

@media (min-width: 768px) {
    .hero-grid {
        grid-template-columns: 1.2fr 0.8fr;
        gap: 2.5rem;
        margin-top: 40px; /* 響應式下保持上邊距 */
    }
}

@media (min-width: 1024px) {
    .hero-grid {
        grid-template-columns: 1.1fr 0.9fr;
        gap: 3rem;
        margin-top: 40px; /* 確保在更大螢幕上也有空間 */
    }
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.flex {
    display: flex;
}

.flex-1 {
    flex: 1 1 0%;
}

.w-2\/3 {
    width: 66.666667%;
}

.w-12 {
    width: 3rem;
}

.w-16 {
    width: 4rem;
}

.h-3 {
    height: 0.75rem;
}

.h-4 {
    height: 1rem;
}

.h-8 {
    height: 2rem;
}

.h-16 {
    height: 4rem;
}

.w-8 {
    width: 2rem;
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.text-5xl {
    font-size: 2.5rem;
    line-height: 1.1;
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 1.1;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.font-bold {
    font-weight: 700;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.h-10 {
    height: 2.5rem;
}

.h-11 {
    height: 2.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* 按鈕樣式覆蓋 */
.inline-flex {
    display: inline-flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.transition-colors {
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.font-medium {
    font-weight: 500;
}

.border {
    border-width: 1px;
}

.border-white {
    border-color: #ffffff;
}

.border-gray-200 {
    border-color: #e5e7eb;
}

.border-gray-100 {
    border-color: #f3f4f6;
}

.border-2 {
    border-width: 2px;
}

.bg-transparent {
    background-color: transparent;
}

.hover\:bg-blue-50:hover {
    background-color: #eff6ff;
}

.hover\:text-blue-600:hover {
    color: #2563eb;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.font-medium {
    font-weight: 500;
}

/* 導航列樣式 */
.navbar-hero {
    background: transparent !important;
    padding: 1.5rem 0 2rem 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.navbar-hero .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
}

.navbar-hero .navbar-brand:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-hero .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    transition: var(--transition);
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
}

.navbar-hero .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-hero .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-hero .btn-outline-light:hover {
    background-color: white;
    border-color: white;
    color: var(--primary-color);
}

.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* 按鈕樣式 */
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn-primary:focus {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.btn-primary:active {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
}

.btn-outline-primary {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn-outline-primary:focus {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* 工具類別 */
.min-vh-75 {
    min-height: 75vh;
}

.text-gray-900 {
    color: var(--gray-900) !important;
}

.text-gray-800 {
    color: var(--gray-800) !important;
}

.text-gray-700 {
    color: var(--gray-700) !important;
}

.text-gray-600 {
    color: var(--gray-600) !important;
}

.text-gray-500 {
    color: var(--gray-500) !important;
}

.text-gray-400 {
    color: var(--gray-400) !important;
}

.bg-gray-50 {
    background-color: var(--gray-50) !important;
}

.bg-gray-100 {
    background-color: var(--gray-100) !important;
}

.bg-gray-900 {
    background-color: var(--gray-900) !important;
}

.rounded-4 {
    border-radius: var(--border-radius-xl) !important;
}

/* 響應式調整 */
@media (max-width: 768px) {
    .hero-section {
        min-height: 40vh; /* 減少手機版高度 */
        padding-bottom: 1rem; /* 減少底部空白 */
    }

    .hero-content {
        padding-top: 1rem;
    }

    .hero-container {
        padding-top: 20px; /* 減少手機版上邊距 */
    }

    .text-4xl {
        font-size: 2rem;
    }

    .text-5xl {
        font-size: 2rem;
    }

    .py-12 {
        padding-top: 1.5rem; /* 減少手機版間距 */
        padding-bottom: 1.5rem;
    }

    .gap-12 {
        gap: 1.5rem; /* 減少手機版間距 */
    }

    /* 手機版導航列調整 */
    .hero-container .flex.gap-4 {
        gap: 0.5rem; /* 減少按鈕間距 */
        flex-wrap: wrap; /* 允許換行 */
    }

    .nav-button {
        font-size: 0.75rem; /* 減小字體 */
        padding: 0.5rem 0.75rem; /* 減小內邊距 */
        height: 2rem; /* 減小高度 */
        min-width: auto; /* 移除最小寬度 */
    }

    /* 手機版 Hero 按鈕調整 */
    .hero-button {
        font-size: 0.875rem;
        padding: 0.75rem 1.5rem;
        height: 2.5rem;
    }

    /* 手機版 Hero grid 調整 */
    .hero-grid {
        margin-top: 20px; /* 減少手機版上邊距 */
        gap: 1.5rem;
    }
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    animation: fadeInUp 0.8s ease-out;
}

.hero-content h1 {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-content p {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-content .d-flex {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

/* 卡片樣式 */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-radius: var(--border-radius-lg);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

/* 區塊樣式 */
section {
    position: relative;
}

.bg-light {
    background-color: var(--gray-50) !important;
}

/* 手風琴樣式 */
.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: var(--border-radius-lg) !important;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.accordion-button {
    background-color: white;
    border: none;
    font-weight: 500;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 1.5rem;
    background-color: var(--gray-50);
}

/* 按鈕樣式 */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

/* 移除重複定義，使用上方統一的 btn-primary 樣式 */

/* 表單樣式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* 表格樣式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* 分頁樣式 */
.pagination .page-link {
    color: var(--primary-color);
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 警告框樣式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 徽章樣式 */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

/* 載入動畫 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* QR Code 相關樣式 */
.qr-code-container {
    text-align: center;
    padding: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.qr-code-scanner {
    max-width: 400px;
    margin: 0 auto;
}

.qr-code-scanner video {
    width: 100%;
    border-radius: var(--border-radius);
}

/* 簽名板樣式 */
.signature-pad {
    border: 2px solid #dee2e6;
    border-radius: var(--border-radius);
    background-color: white;
    cursor: crosshair;
}

.signature-pad:focus {
    border-color: var(--primary-color);
    outline: none;
}

.signature-controls {
    margin-top: 1rem;
    text-align: center;
}

/* 檔案上傳樣式 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.05);
}

/* 統計卡片樣式 */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.25rem;
    }

    .jumbotron {
        padding: 2rem 1rem !important;
    }

    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }

    /* 超小螢幕導航調整 */
    .hero-container .flex.gap-4 {
        gap: 0.25rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .nav-button {
        font-size: 0.7rem;
        padding: 0.375rem 0.5rem;
        height: 1.75rem;
        border-width: 1px;
    }

    /* 超小螢幕 Hero 調整 */
    .hero-section {
        min-height: 35vh;
        padding-bottom: 0.5rem;
    }

    .hero-container {
        padding-top: 15px;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .hero-grid {
        margin-top: 15px;
        gap: 1rem;
    }

    .hero-button {
        font-size: 0.8rem;
        padding: 0.625rem 1.25rem;
        height: 2.25rem;
    }

    /* 品牌名稱調整 */
    .text-xl.font-bold {
        font-size: 1rem;
    }
}

/* 極小螢幕調整 (320px 以下) */
@media (max-width: 320px) {
    .hero-container .flex.gap-4 {
        gap: 0.125rem;
    }

    .nav-button {
        font-size: 0.65rem;
        padding: 0.25rem 0.375rem;
        height: 1.5rem;
        min-width: auto;
    }

    .text-xl.font-bold {
        font-size: 0.9rem;
    }

    .hero-container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* 隱藏部分按鈕文字，只保留圖標或縮寫 */
    .nav-button {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 4rem;
    }
}

/* 響應式文字顯示 */
.hidden {
    display: none !important;
}

.sm\:inline {
    display: none !important;
}

.sm\:hidden {
    display: inline !important;
}

@media (min-width: 640px) {
    .sm\:inline {
        display: inline !important;
    }

    .sm\:hidden {
        display: none !important;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #121212;
        color: var(--dark-color);
    }
    
    .card {
        background-color: #1e1e1e;
        color: var(--dark-color);
    }
    
    .table {
        color: var(--dark-color);
    }
    
    .table thead th {
        background-color: #2d3748;
        color: var(--dark-color);
    }
}

/* 動畫效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

/* 工具提示樣式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}

/* 進度條樣式 */
.progress {
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 列印樣式 */
@media print {
    .navbar,
    .btn,
    .pagination,
    .card-header .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* Flexbox 工具類別 */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.justify-center {
    justify-content: center;
}

.flex-1 {
    flex: 1;
}

/* 移除舊的 Hero 按鈕樣式，使用統一的內聯樣式 */

/* 工具類別 */
.inline-flex {
    display: inline-flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.gap-2 {
    gap: 0.5rem;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.text-sm {
    font-size: 14px;
}

/* 按鈕狀態和可訪問性樣式 */
.ring-offset-background {
    --tw-ring-offset-color: #ffffff;
}

.transition-colors {
    transition-property: color, background-color, border-color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.focus-visible\:outline-none:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus-visible\:ring-2:focus-visible {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: #2563eb;
}

.focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
    pointer-events: none;
}

.disabled\:opacity-50:disabled {
    opacity: 0.5;
}

.\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
    width: 1rem;
    height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
}

.h-11 {
    height: 2.75rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.bg-white {
    background-color: #ffffff;
}

.text-blue-600 {
    color: #2563eb;
}

.hover\:bg-blue-200:hover {
    background-color: #dbeafe;
}

.bg-transparent {
    background-color: transparent;
}

.text-white {
    color: #ffffff;
}

.hover\:bg-white:hover {
    background-color: #ffffff;
}

.hover\:text-blue-600:hover {
    color: #2563eb;
}

.gap-4 {
    gap: 1rem;
}

.hover\:bg-blue-600:hover {
    background-color: #2563eb !important;
}

.hover\:border-blue-600:hover {
    border-color: #2563eb !important;
}

/* 確保 Tailwind 樣式的 hover 效果正常工作 */
.hover\:bg-blue-600:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

.hover\:border-blue-600:hover {
    border-color: #2563eb !important;
}

/* 確保 hover 時文字保持可見 */
.hover\:bg-blue-600:hover {
    background-color: #2563eb !important;
    color: #ffffff !important;
}

/* 額外的 hover 效果 */
button:hover, a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* 移除重複的樣式定義 */

.no-underline {
    text-decoration: none;
}

.h-10 {
    height: 2.5rem;
}

.transition-all {
    transition: all 0.3s ease;
}

/* 強化按鈕 hover 效果 */
.nav-button, .hero-button {
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-button:hover, .hero-button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
}

/* 確保所有按鈕都有基本的 hover 效果 */
button:hover, a.nav-button:hover, a.hero-button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

/* 特別針對 Hero Section 按鈕的 hover 效果 */
button.hero-button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 特別針對導航列按鈕的 hover 效果 */
button.nav-button:hover, a.nav-button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 使用更高優先級的選擇器來確保 hover 效果 */
.hero-section button.hero-button:hover,
.hero-section .hero-button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
    transition: all 0.3s ease !important;
}

/* 覆蓋任何可能的內聯樣式 */
button[class*="hero-button"]:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 最高優先級的 hover 效果 - 調試用 */
.hero-section button:hover,
.hero-section .flex button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
    transition: all 0.3s ease !important;
}

/* 確保所有 Hero 區域的按鈕都有 hover 效果 */
section.hero-section button:hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 通用按鈕 hover 效果 - 但不覆蓋特定按鈕類別 */
button:not(.btn-primary):not(.btn-outline-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-warning):not(.btn-info):not(.btn-light):not(.btn-dark):hover {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 確保 btn-primary 樣式具有最高優先級 */
button.btn-primary,
.btn.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

button.btn-primary:hover,
.btn.btn-primary:hover {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--box-shadow-lg) !important;
}

button.btn-primary:focus,
.btn.btn-primary:focus {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25) !important;
}

button.btn-primary:active,
.btn.btn-primary:active {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

/* 確保 btn-outline-primary 樣式具有最高優先級 */
button.btn-outline-primary,
.btn.btn-outline-primary {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

button.btn-outline-primary:hover,
.btn.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--box-shadow-lg) !important;
}

button.btn-outline-primary:focus,
.btn.btn-outline-primary:focus {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25) !important;
}

button.btn-outline-primary:active,
.btn.btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* 確保小按鈕 (btn-sm) 也有正確的樣式 */
.btn-sm.btn-outline-primary {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-sm.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--box-shadow-lg) !important;
}

.btn-sm.btn-outline-primary:focus {
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25) !important;
}

.btn-sm.btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

/* 確保 btn-success 樣式正確 */
.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
}

.btn-success:hover {
    background-color: #157347 !important;
    border-color: #146c43 !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3) !important;
}

.btn-success:focus {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25) !important;
}

.btn-success:active {
    background-color: #157347 !important;
    border-color: #146c43 !important;
    color: white !important;
}