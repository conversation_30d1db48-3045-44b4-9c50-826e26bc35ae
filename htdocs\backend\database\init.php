<?php
/**
 * 資料庫初始化腳本
 * 執行此腳本來建立 SignAttend 所需的資料庫表格
 */

$dbConfig = require __DIR__ . '/../config/database.php';

try {
    // 連接到 MySQL 伺服器（不指定資料庫）
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    echo "正在建立資料庫和表格...\n";

    // 讀取並執行 SQL 腳本
    $sql = file_get_contents(__DIR__ . '/schema.sql');

    // 移除註解並分割 SQL 語句
    $sql = preg_replace('/--.*$/m', '', $sql); // 移除單行註解
    $statements = array_filter(array_map('trim', preg_split('/;[\r\n]+/', $sql)));

    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*$/', $statement)) {
            try {
                $pdo->exec($statement);
                $firstLine = strtok($statement, "\n");
                echo "✓ 執行成功: " . substr($firstLine, 0, 60) . "...\n";
            } catch (PDOException $e) {
                echo "✗ 執行失敗: " . substr($statement, 0, 60) . "...\n";
                echo "  錯誤: " . $e->getMessage() . "\n";
            }
        }
    }

    // 驗證表格是否建立成功
    echo "\n驗證資料庫表格...\n";
    $tables = ['users', 'profiles', 'meetings', 'attendees'];

    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✓ 表格 '$table' 建立成功\n";
            } else {
                echo "✗ 表格 '$table' 建立失敗\n";
            }
        } catch (PDOException $e) {
            echo "✗ 無法檢查表格 '$table': " . $e->getMessage() . "\n";
        }
    }

    echo "\n資料庫初始化完成！\n";
    echo "資料庫名稱: " . $dbConfig['database'] . "\n";
    echo "您現在可以開始使用 SignAttend API 了。\n";

} catch (PDOException $e) {
    echo "資料庫初始化失敗: " . $e->getMessage() . "\n";
    echo "請確認：\n";
    echo "1. XAMPP MySQL 服務已啟動\n";
    echo "2. 資料庫配置正確 (config/database.php)\n";
    echo "3. MySQL 使用者權限足夠\n";
    exit(1);
}
?>
