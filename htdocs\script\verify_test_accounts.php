<?php
/**
 * 驗證測試帳號密碼
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

// 載入核心類別
require_once UTILS_PATH . '/Auth.php';

echo "=== 驗證測試帳號密碼 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 初始化 Auth 物件
$auth = new Auth();

// 測試帳號列表
$testAccounts = [
    ['email' => '<EMAIL>', 'password' => 'test123', 'description' => '測試帳號'],
    ['email' => '<EMAIL>', 'password' => 'admin123', 'description' => '管理員帳號'],
    ['email' => '<EMAIL>', 'password' => 'user123', 'description' => '一般用戶'],
    ['email' => '<EMAIL>', 'password' => 'demo123', 'description' => '演示帳號']
];

echo "測試以下帳號的登入功能:\n\n";

foreach ($testAccounts as $account) {
    echo "=== " . $account['description'] . " ===\n";
    echo "郵箱: " . $account['email'] . "\n";
    echo "密碼: " . $account['password'] . "\n";
    
    // 嘗試登入
    $result = $auth->login($account['email'], $account['password'], false);
    
    if ($result['success']) {
        echo "✅ 登入成功\n";
        echo "訊息: " . ($result['message'] ?? '登入成功') . "\n";
        
        // 登出以便測試下一個帳號
        $auth->logout();
    } else {
        echo "❌ 登入失敗\n";
        echo "訊息: " . ($result['message'] ?? '未知錯誤') . "\n";
    }
    
    echo "\n";
}

// 檢查資料庫中的實際用戶
echo "=== 資料庫中的實際用戶 ===\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $pdo->query("SELECT email, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "- " . $user['email'] . " (創建於: " . substr($user['created_at'], 0, 19) . ")\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 建議的演示帳號 ===\n";
echo "根據測試結果，建議在登入頁面使用以下帳號:\n\n";

// 重新測試並給出建議
$workingAccounts = [];

foreach ($testAccounts as $account) {
    $result = $auth->login($account['email'], $account['password'], false);
    
    if ($result['success']) {
        $workingAccounts[] = $account;
        echo "✅ " . $account['description'] . "\n";
        echo "   郵箱: " . $account['email'] . "\n";
        echo "   密碼: " . $account['password'] . "\n\n";
        
        $auth->logout();
    }
}

if (empty($workingAccounts)) {
    echo "❌ 沒有找到可用的測試帳號\n";
    echo "建議執行: php check_test_users.php 來創建測試帳號\n";
} else {
    echo "共找到 " . count($workingAccounts) . " 個可用的測試帳號\n";
}

echo "\n=== 驗證完成 ===\n";
?>
