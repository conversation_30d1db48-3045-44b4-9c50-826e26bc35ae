<?php
echo "PHP 正常運作！<br>";
echo "PHP 版本: " . phpversion() . "<br>";

// 測試 autoload
require 'vendor/autoload.php';
echo "Composer autoload 載入成功！<br>";

// 測試 Flight 框架
try {
    echo "Flight 類別存在: " . (class_exists('Flight') ? '是' : '否') . "<br>";
} catch (Exception $e) {
    echo "Flight 測試錯誤: " . $e->getMessage() . "<br>";
}

// 測試資料庫連接
try {
    $dbConfig = require 'config/database.php';
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'],
        $dbConfig['user'],
        $dbConfig['password']
    );
    echo "資料庫連接成功！<br>";
} catch (PDOException $e) {
    echo "資料庫連接失敗: " . $e->getMessage() . "<br>";
}

phpinfo();
?>
