<?php
/**
 * 檢查密碼重設表結構
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "=== 檢查密碼重設表結構 ===\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 檢查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'password_reset_logs'");
    
    if ($stmt->rowCount() > 0) {
        echo "✅ password_reset_logs 表存在\n\n";
        
        // 顯示表結構
        echo "=== 表結構 ===\n";
        $stmt = $pdo->query("DESCRIBE password_reset_logs");
        $columns = $stmt->fetchAll();
        
        printf("%-20s %-20s %-6s %-4s %-10s\n", "欄位名稱", "資料類型", "NULL", "鍵", "預設值");
        echo str_repeat("-", 70) . "\n";
        
        foreach ($columns as $column) {
            printf("%-20s %-20s %-6s %-4s %-10s\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key'],
                $column['Default'] ?? 'NULL'
            );
        }
        
        // 檢查記錄數量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM password_reset_logs");
        $count = $stmt->fetch()['count'];
        echo "\n記錄數量: $count\n";
        
        // 如果缺少 user_email 欄位，添加它
        $hasUserEmail = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'user_email') {
                $hasUserEmail = true;
                break;
            }
        }
        
        if (!$hasUserEmail) {
            echo "\n❌ 缺少 user_email 欄位，正在添加...\n";
            $pdo->exec("ALTER TABLE password_reset_logs ADD COLUMN user_email VARCHAR(255) NOT NULL AFTER user_id");
            $pdo->exec("ALTER TABLE password_reset_logs ADD INDEX idx_user_email (user_email)");
            echo "✅ user_email 欄位已添加\n";
        } else {
            echo "\n✅ user_email 欄位存在\n";
        }
        
    } else {
        echo "❌ password_reset_logs 表不存在，正在創建...\n";
        
        $sql = "
            CREATE TABLE password_reset_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                user_email VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_user_email (user_email),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($sql);
        echo "✅ password_reset_logs 表已創建\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 檢查完成 ===\n";
?>
