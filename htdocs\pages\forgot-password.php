<?php
/**
 * SignAttend PHP Frontend - 忘記密碼頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';
require_once UTILS_PATH . '/PasswordReset.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();
$passwordReset = new PasswordReset();

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理忘記密碼表單提交
$error = '';
$success = '';
$tempPassword = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');

    if (empty($email)) {
        $error = '請輸入電子郵件';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = '請輸入有效的電子郵件格式';
    } else {
        try {
            // 使用本地密碼重設處理
            $result = $passwordReset->handleForgotPassword($email);

            if ($result['success']) {
                $success = $result['message'];

                // 顯示臨時密碼
                if (isset($result['temp_password'])) {
                    $tempPassword = $result['temp_password'];
                    $success .= '<br><br><div class="temp-password-info">';
                    $success .= '<strong>臨時密碼：</strong><span class="temp-password">' . h($tempPassword) . '</span><br>';
                    $success .= '<strong>電子郵件：</strong>' . h($result['email']) . '<br>';
                    $success .= '<small class="note">' . h($result['note'] ?? '') . '</small>';
                    $success .= '</div>';
                }
            } else {
                $error = $result['message'] ?? '密碼重設失敗';
            }
        } catch (Exception $e) {
            // 記錄錯誤
            error_log("Forgot password error: " . $e->getMessage());

            // 顯示用戶友好的錯誤信息
            $error = '系統暫時無法處理密碼重設請求，請稍後再試';

            // 在開發環境顯示詳細錯誤
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $error .= '<br><small>調試信息: ' . h($e->getMessage()) . '</small>';
            }
        }
    }
}

// 頁面標題
$pageTitle = 'SignAttend - 忘記密碼';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <meta name="description" content="SignAttend 智慧簽到系統 - 忘記密碼">
    <meta name="author" content="SignAttend System">

    <meta property="og:title" content="SignAttend - 忘記密碼">
    <meta property="og:description" content="SignAttend 智慧簽到系統 - 忘記密碼">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        h1 {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group input::placeholder {
            color: #aaa;
        }

        .reset-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.4);
        }

        .reset-btn:active {
            transform: translateY(0);
        }

        .reset-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .back-link {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #00f2fe;
        }

        .alert {
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: left;
        }

        .alert-danger {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .alert-success {
            background: #efe;
            color: #3c3;
            border: 1px solid #cfc;
        }

        .temp-password-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .temp-password {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            background: #e7f3ff;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
        }

        .note {
            color: #6c757d;
            font-style: italic;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 24px;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <div class="logo-icon">🔑</div>
            <h1>忘記密碼</h1>
            <p class="subtitle">SignAttend 智慧簽到系統</p>
        </div>

        <!-- 錯誤訊息顯示區域 -->
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?= h($error) ?>
            </div>
        <?php endif; ?>

        <!-- 成功訊息顯示區域 -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?= $success ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="" id="forgotPasswordForm">
            <?php generate_csrf_token_field(); ?>

            <div class="form-group">
                <label for="email">電子郵件</label>
                <input type="email" id="email" name="email" placeholder="請輸入您的電子郵件"
                       value="<?= h($_POST['email'] ?? '') ?>" required>
            </div>

            <button type="submit" class="reset-btn" id="resetButton">
                <span id="resetText">重設密碼</span>
                <span id="resetSpinner" style="display: none;">處理中...</span>
            </button>

            <div style="margin-top: 20px;">
                <a href="<?= page_url('pages/login.php') ?>" class="back-link">返回登入</a>
                <span style="color: #ccc; margin: 0 10px;">|</span>
                <a href="<?= page_url('index.php') ?>" class="back-link">返回首頁</a>
            </div>
        </form>

        <!-- 開發環境提示 -->
        <?php if (DEBUG_MODE): ?>
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; font-size: 13px; color: #6c757d;">
            <strong>開發環境提示：</strong><br>
            在開發環境中，重設的臨時密碼會直接顯示在頁面上。<br>
            在生產環境中，密碼會通過電子郵件發送給使用者。<br>
            <br>
            <strong>測試用電子郵件：</strong><br>
            • <EMAIL><br>
            • <EMAIL><br>
            • <EMAIL>
        </div>
        <?php endif; ?>
    </div>
    <script>
        // 設定載入狀態
        function setLoading(isLoading) {
            const button = document.getElementById('resetButton');
            const text = document.getElementById('resetText');
            const spinner = document.getElementById('resetSpinner');

            if (isLoading) {
                button.disabled = true;
                text.style.display = 'none';
                spinner.style.display = 'inline';
                button.style.opacity = '0.7';
            } else {
                button.disabled = false;
                text.style.display = 'inline';
                spinner.style.display = 'none';
                button.style.opacity = '1';
            }
        }

        // 表單提交時顯示載入狀態
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            setLoading(true);
        });

        // 添加輸入框動畫效果
        const emailInput = document.getElementById('email');
        emailInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });

        emailInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Enter 鍵快速提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('resetButton').disabled) {
                document.getElementById('forgotPasswordForm').dispatchEvent(new Event('submit'));
            }
        });

        // 如果有成功訊息，自動隱藏載入狀態
        <?php if (!empty($success)): ?>
            setTimeout(function() {
                setLoading(false);
            }, 100);
        <?php endif; ?>

        // 如果有錯誤訊息，重置載入狀態
        <?php if (!empty($error)): ?>
            setLoading(false);
        <?php endif; ?>

        // 如果有臨時密碼，添加複製功能
        <?php if (!empty($tempPassword)): ?>
            const tempPasswordElement = document.querySelector('.temp-password');
            if (tempPasswordElement) {
                tempPasswordElement.style.cursor = 'pointer';
                tempPasswordElement.title = '點擊複製';
                tempPasswordElement.addEventListener('click', function() {
                    navigator.clipboard.writeText('<?= $tempPassword ?>').then(function() {
                        const originalText = tempPasswordElement.textContent;
                        tempPasswordElement.textContent = '已複製!';
                        tempPasswordElement.style.background = '#d4edda';
                        tempPasswordElement.style.color = '#155724';

                        setTimeout(function() {
                            tempPasswordElement.textContent = originalText;
                            tempPasswordElement.style.background = '#e7f3ff';
                            tempPasswordElement.style.color = '#007bff';
                        }, 2000);
                    }).catch(function() {
                        alert('複製失敗，請手動複製密碼');
                    });
                });
            }
        <?php endif; ?>
    </script>
</body>
</html>
