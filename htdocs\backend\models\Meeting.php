<?php

namespace SignAttend\Models;

use PDO;
use PDOException;

class Meeting {
    private $db;
    private $table_name = "meetings";

    public function __construct(PDO $db) {
        $this->db = $db;
    }

    // 創建會議
    public function create($data) {
        try {
            $query = "INSERT INTO " . $this->table_name . " (id, name, description, start_date, end_date, location, created_by)
                      VALUES (:id, :name, :description, :start_date, :end_date, :location, :created_by)";
            $stmt = $this->db->prepare($query);

            $id = $this->generateUUID();

            $stmt->bindParam(":id", $id);
            $stmt->bindParam(":name", $data['name']);
            $stmt->bindParam(":description", $data['description']);
            $stmt->bindParam(":start_date", $data['start_date']);
            $stmt->bindParam(":end_date", $data['end_date']);
            $stmt->bindParam(":location", $data['location']);
            $stmt->bindParam(":created_by", $data['created_by']);

            if ($stmt->execute()) {
                return $this->readOne($id);
            }
            return false;
        } catch (PDOException $e) {
            error_log("Meeting create error: " . $e->getMessage());
            return false;
        }
    }

    // 讀取單一會議
    public function readOne($id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Meeting readOne error: " . $e->getMessage());
            return null;
        }
    }

    // 讀取所有會議
    public function readAll($created_by = null) {
        try {
            if ($created_by) {
                $query = "SELECT * FROM " . $this->table_name . " WHERE created_by = ? ORDER BY created_at DESC";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(1, $created_by);
            } else {
                $query = "SELECT * FROM " . $this->table_name . " ORDER BY created_at DESC";
                $stmt = $this->db->prepare($query);
            }
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Meeting readAll error: " . $e->getMessage());
            return [];
        }
    }

    // 更新會議
    public function update($id, $data) {
        try {
            $query = "UPDATE " . $this->table_name . "
                      SET name = :name, description = :description, start_date = :start_date,
                          end_date = :end_date, location = :location
                      WHERE id = :id";
            $stmt = $this->db->prepare($query);

            $stmt->bindParam(":name", $data['name']);
            $stmt->bindParam(":description", $data['description']);
            $stmt->bindParam(":start_date", $data['start_date']);
            $stmt->bindParam(":end_date", $data['end_date']);
            $stmt->bindParam(":location", $data['location']);
            $stmt->bindParam(":id", $id);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Meeting update error: " . $e->getMessage());
            return false;
        }
    }

    // 刪除會議
    public function delete($id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Meeting delete error: " . $e->getMessage());
            return false;
        }
    }

    // 根據創建者取得會議
    public function getByCreator($created_by) {
        return $this->readAll($created_by);
    }

    // 生成 UUID
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
