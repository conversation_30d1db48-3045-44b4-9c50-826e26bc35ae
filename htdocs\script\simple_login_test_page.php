<?php
/**
 * 簡化的登入測試頁面
 */

define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/../config/environment.php';

// 載入必要文件
require_once INCLUDES_PATH . '/functions.php';
require_once UTILS_PATH . '/Auth.php';

// 創建 Auth 實例
$auth = new Auth();

$result = null;
$error = '';
$success = '';

// 處理登入請求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    // 驗證 CSRF Token
    $csrfToken = $_POST['csrf_token'] ?? '';
    if (!$auth->validateCSRFToken($csrfToken)) {
        $error = 'CSRF token 驗證失敗';
    } else {
        // 執行登入
        $result = $auth->login($email, $password, false);
        
        if ($result['success']) {
            $success = '登入成功！';
            // 檢查登入狀態
            if ($auth->isLoggedIn()) {
                $success .= ' 登入狀態確認成功。';
                // 可以重定向到儀表板
                // header('Location: ../pages/dashboard.php');
                // exit;
            } else {
                $error = '登入成功但狀態檢查失敗';
            }
        } else {
            $error = $result['message'] ?? '登入失敗';
        }
    }
}

// 生成 CSRF Token
$csrfToken = $auth->generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡化登入測試</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="email"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .debug { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔐 簡化登入測試</h1>
    
    <?php if ($success): ?>
        <div class="success">✅ <?= htmlspecialchars($success) ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="error">❌ <?= htmlspecialchars($error) ?></div>
    <?php endif; ?>
    
    <?php if ($result): ?>
        <div class="debug">
            <strong>登入結果詳情：</strong><br>
            <?= htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrfToken) ?>">
        
        <div class="form-group">
            <label for="email">電子郵件:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">密碼:</label>
            <input type="password" id="password" name="password" value="aaaaaa" required>
        </div>
        
        <button type="submit">登入測試</button>
    </form>
    
    <div class="debug">
        <strong>調試信息：</strong><br>
        Session 狀態: <?= session_status() === PHP_SESSION_ACTIVE ? '已啟動' : '未啟動' ?><br>
        Session ID: <?= session_id() ?><br>
        CSRF Token: <?= substr($csrfToken, 0, 10) ?>...<br>
        當前登入狀態: <?= $auth->isLoggedIn() ? '已登入' : '未登入' ?><br>
        <?php if ($auth->isLoggedIn()): ?>
            當前用戶: <?= json_encode($auth->getCurrentUser(), JSON_UNESCAPED_UNICODE) ?><br>
        <?php endif; ?>
    </div>
</body>
</html>
