# 註冊功能時區問題修復報告

## 📋 問題概述

在註冊功能開發過程中，發現資料庫中的 `created_at` 和 `updated_at` 時間欄位顯示的不是台北時間，而是其他時區的時間（相差約15小時）。

## 🔍 問題分析

### 初始問題
- **現象**：用戶註冊時間顯示為 `06:19:20`，但實際台北時間應為 `21:19:20`
- **影響範圍**：`users` 表和 `profiles` 表的時間記錄
- **根本原因**：資料庫欄位類型和時區設定問題

### 診斷過程

#### 1. 時區測試
創建測試頁面 `test_timezone.php` 進行診斷：
- **PHP 時區**：正確顯示台北時間 `21:01:01`
- **資料庫預設時區**：顯示錯誤時間 `06:01:01`（UTC-7）
- **設定時區後**：正確顯示台北時間 `21:01:01`

#### 2. 直接插入測試
創建測試頁面 `test_insert_time.php` 驗證：
- **PHP 計算時間**：`2025-07-08 21:17:31`
- **資料庫返回時間**：`2025-07-08 21:17:31`
- **結果**：時間完全一致 ✅

#### 3. 表結構檢查
檢查 `users` 表結構發現關鍵問題：
```sql
created_at    timestamp    NO        current_timestamp()
updated_at    timestamp    NO        current_timestamp()    on update current_timestamp()
```

**問題根源**：`TIMESTAMP` 類型會根據資料庫伺服器時區自動轉換時間

## 🛠️ 解決方案

### 方案選擇
經過測試，決定採用修改資料庫欄位類型的方案：
- 將 `TIMESTAMP` 改為 `DATETIME`
- `DATETIME` 類型不會自動轉換時區，保持原始輸入值

### 實施步驟

#### 1. 資料庫結構修改
執行以下 SQL 語句：
```sql
-- 修改 users 表的時間欄位從 TIMESTAMP 改為 DATETIME
ALTER TABLE users 
MODIFY COLUMN created_at DATETIME NOT NULL,
MODIFY COLUMN updated_at DATETIME NOT NULL;

-- 修改 profiles 表的時間欄位從 TIMESTAMP 改為 DATETIME  
ALTER TABLE profiles 
MODIFY COLUMN created_at DATETIME NOT NULL,
MODIFY COLUMN updated_at DATETIME NOT NULL;
```

#### 2. 程式碼優化
在 `Auth.php` 中實現台北時間處理：
```php
// 計算台北時間
$taipeiTime = new DateTime('now', new DateTimeZone('Asia/Taipei'));
$taipeiTimeString = $taipeiTime->format('Y-m-d H:i:s');

// 直接存儲台北時間字串
$userStmt->bindParam(':created_at', $taipeiTimeString);
$userStmt->bindParam(':updated_at', $taipeiTimeString);
```

#### 3. 容錯處理
針對共享主機限制，添加容錯機制：
```php
// 嘗試設定時區（如果共享主機允許的話）
try {
    $pdo->exec("SET time_zone = '+08:00'");
} catch (PDOException $e) {
    // 如果時區設定失敗，記錄但不中斷程序
    error_log('Cannot set database timezone: ' . $e->getMessage());
}
```

## ✅ 修復結果

### 修復前
- 註冊時間：`2025-07-08 06:19:20`（錯誤時區）
- 欄位類型：`TIMESTAMP`（自動轉換時區）

### 修復後
- 註冊時間：`2025-07-08 21:19:20`（正確台北時間）
- 欄位類型：`DATETIME`（保持原始值）

### 驗證結果
除錯日誌顯示完整的時間處理過程：
```json
{
  "action": "timezone_info",
  "php_timezone": "Asia/Taipei",
  "taipei_time": "2025-07-08 21:19:20",
  "utc_time": "2025-07-08 13:19:20",
  "storing_time": "2025-07-08 21:19:20"
}
```

## 📚 技術要點

### TIMESTAMP vs DATETIME
| 類型 | 時區轉換 | 範圍 | 適用場景 |
|------|----------|------|----------|
| `TIMESTAMP` | 自動轉換 | 1970-2038 | 需要時區轉換的場景 |
| `DATETIME` | 不轉換 | 1000-9999 | 保持原始時間值 |

### 共享主機注意事項
1. **時區設定限制**：共享主機通常不允許修改全域時區
2. **權限限制**：`SET time_zone` 可能受到限制
3. **解決策略**：在應用層處理時區，使用 `DATETIME` 類型

## 🎯 最佳實踐

### 時間處理建議
1. **統一時區**：在應用層統一使用台北時間
2. **欄位類型**：使用 `DATETIME` 避免自動時區轉換
3. **容錯處理**：添加時區設定的容錯機制
4. **詳細日誌**：記錄時間處理的每個步驟

### 程式碼範例
```php
// 正確的時間處理方式
$taipeiTime = new DateTime('now', new DateTimeZone('Asia/Taipei'));
$timeString = $taipeiTime->format('Y-m-d H:i:s');

// 使用 DATETIME 欄位直接存儲
$stmt = $pdo->prepare("INSERT INTO users (created_at) VALUES (?)");
$stmt->execute([$timeString]);
```

## 📊 影響評估

### 正面影響
- ✅ 時間顯示正確（台北時間）
- ✅ 用戶體驗改善
- ✅ 資料一致性提升
- ✅ 除錯能力增強

### 注意事項
- 🔄 現有資料的時間可能需要手動調整
- 📝 需要更新相關文檔
- 🧪 建議進行全面測試

## 🏁 總結

通過修改資料庫欄位類型從 `TIMESTAMP` 到 `DATETIME`，並在應用層正確處理台北時間，成功解決了註冊功能的時區問題。此修復確保了：

1. **時間準確性**：註冊時間正確顯示為台北時間
2. **系統穩定性**：添加了容錯處理機制
3. **可維護性**：提供詳細的除錯日誌
4. **擴展性**：為其他時間相關功能提供了參考

---

**修復完成時間**：2025-07-08 21:22  
**開發人員**：AI Assistant  
**測試狀態**：✅ 驗證通過
