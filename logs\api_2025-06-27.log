{"timestamp":"2025-06-27 05:45:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 05:45:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 05:45:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 05:46:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 05:46:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 05:46:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 06:15:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 06:15:04","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 06:15:04","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 10:56:27","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 10:56:27","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 10:56:27","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 11:38:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 11:38:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 11:38:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:24:07","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:24:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:24:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:37:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:37:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:37:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:12","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:16","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:26","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 13:50:29","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:e00f:56fa:4592:7a1e","user_id":"b390e61459cf0c7e9a5623cb8bebda8a1081","error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 22:58:05","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 22:58:05","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-06-27 22:58:06","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2001:b400:e309:18a:5f4a:76f8:d1ce:8f80","user_id":null,"error":"Invalid JSON response: Syntax error"}
