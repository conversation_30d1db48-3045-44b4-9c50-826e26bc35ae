<?php
/**
 * 直接測試 AJAX 登入請求
 */

echo "=== 直接 AJAX 登入測試 ===\n";
echo "當前時間: " . date('Y-m-d H:i:s') . "\n\n";

// 檢查日誌檔案狀態
define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/config/environment.php';

$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
$debugLogFile = LOG_PATH . '/pages_login_debug_' . date('Y-m-d') . '.log';

echo "認證日誌檔案: " . $authLogFile . "\n";
echo "調試日誌檔案: " . $debugLogFile . "\n\n";

// 記錄測試前狀態
$beforeAuthSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
$beforeDebugSize = file_exists($debugLogFile) ? filesize($debugLogFile) : 0;

echo "測試前狀態:\n";
echo "- 認證日誌大小: " . $beforeAuthSize . " bytes\n";
echo "- 調試日誌大小: " . $beforeDebugSize . " bytes\n\n";

// 準備 POST 數據
$postData = [
    'ajax_login' => '1',
    'email' => 'direct_test_' . time() . '@example.com',
    'password' => 'test123',
    'remember_me' => '0'
];

echo "POST 數據: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";

// 模擬 POST 請求環境
$_POST = $postData;
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/test';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'Direct Test Script';

echo "開始執行 login.php 的 AJAX 處理邏輯...\n";

// 啟動輸出緩衝
ob_start();

// 包含 login.php 的 AJAX 處理部分
if (isset($_POST['ajax_login']) && $_POST['ajax_login'] === '1') {
    // 啟動 Session
    session_start();
    
    // 載入共用函數
    require_once INCLUDES_PATH . '/functions.php';
    
    // 載入核心類別
    require_once UTILS_PATH . '/ApiClient.php';
    require_once UTILS_PATH . '/Auth.php';
    
    // 初始化核心物件
    $apiClient = new ApiClient();
    $auth = new Auth();
    
    header('Content-Type: application/json');

    // 記錄調試信息到專用文件
    $debugLogFile = LOG_PATH . '/pages_login_debug_' . date('Y-m-d') . '.log';

    function writeDebugLog($message) {
        global $debugLogFile;
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($debugLogFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    writeDebugLog("=== DIRECT TEST AJAX 開始 ===");
    writeDebugLog("POST 數據: " . json_encode($_POST));

    $response = [
        'success' => false,
        'message' => '',
        'redirect' => '',
        'debug_info' => []
    ];

    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    writeDebugLog("Email: $email, Password: " . (!empty($password) ? '[有值]' : '[空]'));

    if (empty($email) || empty($password)) {
        $response['message'] = '請輸入電子郵件和密碼';
        writeDebugLog("驗證失敗: 空的 email 或密碼");
    } else {
        // 記錄登入前的認證日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        // 清除檔案狀態緩存以確保獲取準確的檔案大小
        clearstatcache(true, $authLogFile);
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;

        writeDebugLog("認證日誌文件: $authLogFile");
        writeDebugLog("登入前大小: $beforeSize bytes");

        // 執行登入
        writeDebugLog("開始執行 auth->login()");
        $result = $auth->login($email, $password, $rememberMe);
        writeDebugLog("auth->login() 完成，結果: " . json_encode($result));

        // 記錄登入後的認證日誌狀態
        // 清除檔案狀態緩存以確保獲取最新的檔案大小
        clearstatcache(true, $authLogFile);
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        writeDebugLog("登入後大小: $afterSize bytes");

        if ($result['success']) {
            $response['success'] = true;
            $response['message'] = '登入成功！正在跳轉...';
            writeDebugLog("登入成功");
        } else {
            $response['message'] = $result['message'] ?? '登入失敗';
            writeDebugLog("登入失敗: " . $response['message']);
        }

        $response['debug_info'] = [
            'before_size' => $beforeSize,
            'after_size' => $afterSize,
            'auth_log_file' => $authLogFile,
            'log_path' => LOG_PATH
        ];
    }

    writeDebugLog("返回響應: " . json_encode($response));
    writeDebugLog("=== DIRECT TEST AJAX 結束 ===");

    echo json_encode($response);
}

// 獲取輸出
$output = ob_get_clean();

echo "AJAX 處理完成\n\n";

// 檢查測試後狀態
clearstatcache();
$afterAuthSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
$afterDebugSize = file_exists($debugLogFile) ? filesize($debugLogFile) : 0;

echo "測試後狀態:\n";
echo "- 認證日誌大小: " . $afterAuthSize . " bytes (變化: " . ($afterAuthSize - $beforeAuthSize) . ")\n";
echo "- 調試日誌大小: " . $afterDebugSize . " bytes (變化: " . ($afterDebugSize - $beforeDebugSize) . ")\n\n";

echo "AJAX 響應:\n";
echo $output . "\n\n";

// 檢查日誌內容
if ($afterDebugSize > $beforeDebugSize) {
    echo "=== 新的調試日誌內容 ===\n";
    $debugContent = file_get_contents($debugLogFile);
    $newDebugContent = substr($debugContent, $beforeDebugSize);
    echo $newDebugContent . "\n";
}

if ($afterAuthSize > $beforeAuthSize) {
    echo "=== 新的認證日誌內容 ===\n";
    $authContent = file_get_contents($authLogFile);
    $newAuthContent = substr($authContent, $beforeAuthSize);
    echo $newAuthContent . "\n";
}

echo "=== 測試完成 ===\n";
?>
