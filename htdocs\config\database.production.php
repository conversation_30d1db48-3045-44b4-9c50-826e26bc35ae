<?php
/**
 * SignAttend 正式環境資料庫配置
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 防止直接訪問
if (!defined('SIGNATTEND_INIT')) {
    die('Direct access not allowed');
}

return [
    'host' => 'sql302.byetcluster.com',
    'database' => 'uoolo_38699510_signattend',
    'user' => 'uoolo_38699510',
    'password' => 'kai@0932540826', // 請修改為實際密碼
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]
];
