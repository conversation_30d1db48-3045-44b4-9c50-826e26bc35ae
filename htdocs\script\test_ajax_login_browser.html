<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX 登入測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AJAX 登入功能測試</h1>
        
        <div class="result info">
            <strong>測試說明：</strong>
            這個頁面用來測試 AJAX 登入功能是否正常寫入日誌。
            請輸入任意郵箱和密碼（會登入失敗，但應該會寫入日誌）。
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">電子郵件：</label>
                <input type="email" id="email" name="email" value="test_browser_<?= time() ?>@example.com" required>
            </div>

            <div class="form-group">
                <label for="password">密碼：</label>
                <input type="password" id="password" name="password" value="test123" required>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="remember_me" name="remember_me"> 記住我
                </label>
            </div>

            <button type="submit" class="btn" id="submitBtn">🚀 測試 AJAX 登入</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用按鈕
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 測試中...';
            
            // 清除之前的結果
            resultDiv.innerHTML = '';
            
            // 準備表單數據
            const formData = new FormData();
            formData.append('ajax_login', '1');
            formData.append('email', document.getElementById('email').value);
            formData.append('password', document.getElementById('password').value);
            if (document.getElementById('remember_me').checked) {
                formData.append('remember_me', '1');
            }
            
            const startTime = new Date();
            
            // 發送 AJAX 請求
            fetch('pages/login.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                const endTime = new Date();
                const duration = endTime - startTime;
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers.get('content-type'));
                
                return response.text().then(text => {
                    return {
                        status: response.status,
                        contentType: response.headers.get('content-type'),
                        text: text,
                        duration: duration
                    };
                });
            })
            .then(data => {
                let resultHtml = '';
                
                if (data.status === 200) {
                    resultHtml += '<div class="result success">✅ AJAX 請求成功</div>';
                } else {
                    resultHtml += '<div class="result error">❌ AJAX 請求失敗 (HTTP ' + data.status + ')</div>';
                }
                
                resultHtml += '<div class="debug-info">';
                resultHtml += '<strong>請求詳情：</strong>\n';
                resultHtml += '狀態碼: ' + data.status + '\n';
                resultHtml += '內容類型: ' + data.contentType + '\n';
                resultHtml += '響應時間: ' + data.duration + 'ms\n';
                resultHtml += '時間戳: ' + new Date().toISOString() + '\n';
                resultHtml += '</div>';
                
                // 嘗試解析 JSON 響應
                try {
                    const jsonData = JSON.parse(data.text);
                    
                    if (jsonData.success) {
                        resultHtml += '<div class="result success">✅ 登入成功</div>';
                    } else {
                        resultHtml += '<div class="result error">❌ 登入失敗: ' + jsonData.message + '</div>';
                    }
                    
                    if (jsonData.debug_info) {
                        const debugInfo = jsonData.debug_info;
                        const sizeChange = debugInfo.after_size - debugInfo.before_size;
                        
                        resultHtml += '<div class="debug-info">';
                        resultHtml += '<strong>日誌寫入檢查：</strong>\n';
                        resultHtml += '認證日誌檔案: ' + debugInfo.auth_log_file + '\n';
                        resultHtml += '登入前大小: ' + debugInfo.before_size + ' bytes\n';
                        resultHtml += '登入後大小: ' + debugInfo.after_size + ' bytes\n';
                        resultHtml += '大小變化: ' + sizeChange + ' bytes\n';
                        resultHtml += '日誌路徑: ' + debugInfo.log_path + '\n';
                        resultHtml += '</div>';
                        
                        if (sizeChange > 0) {
                            resultHtml += '<div class="result success">✅ 日誌寫入成功！檔案大小增加了 ' + sizeChange + ' bytes</div>';
                        } else {
                            resultHtml += '<div class="result error">❌ 日誌寫入失敗！檔案大小沒有變化</div>';
                        }
                    }
                    
                    resultHtml += '<div class="debug-info">';
                    resultHtml += '<strong>完整響應數據：</strong>\n';
                    resultHtml += JSON.stringify(jsonData, null, 2);
                    resultHtml += '</div>';
                    
                } catch (e) {
                    resultHtml += '<div class="result error">❌ JSON 解析失敗</div>';
                    resultHtml += '<div class="debug-info">';
                    resultHtml += '<strong>原始響應：</strong>\n';
                    resultHtml += data.text;
                    resultHtml += '</div>';
                }
                
                resultDiv.innerHTML = resultHtml;
            })
            .catch(error => {
                console.error('AJAX error:', error);
                resultDiv.innerHTML = '<div class="result error">❌ 網路錯誤: ' + error.message + '</div>';
            })
            .finally(() => {
                // 重新啟用按鈕
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 測試 AJAX 登入';
            });
        });
        
        // 自動生成新的測試郵箱
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('email');
            const timestamp = Math.floor(Date.now() / 1000);
            emailInput.value = 'test_browser_' + timestamp + '@example.com';
        });
    </script>
</body>
</html>
