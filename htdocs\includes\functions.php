<?php
/**
 * 共用函數庫
 * 包含整個應用程式中常用的輔助函數
 */

/**
 * 安全輸出 HTML 內容
 */
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 獲取客戶端 IP 地址
 */
if (!function_exists('get_client_ip')) {
function get_client_ip() {
    // 檢查是否有代理伺服器
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // 可能有多個 IP，取第一個
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]);
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        return $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }

    return 'unknown';
}
}

/**
 * 安全輸出 HTML 屬性
 */
function attr($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 格式化日期時間
 */
function format_datetime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    
    try {
        $date = new DateTime($datetime);
        return $date->format($format);
    } catch (Exception $e) {
        return $datetime;
    }
}

/**
 * 格式化日期
 */
function format_date($date, $format = 'Y-m-d') {
    return format_datetime($date, $format);
}

/**
 * 格式化時間
 */
function format_time($time, $format = 'H:i') {
    return format_datetime($time, $format);
}

/**
 * 相對時間顯示 (例如：2小時前)
 */
function time_ago($datetime) {
    if (empty($datetime)) return '';
    
    try {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return '剛剛';
        if ($time < 3600) return floor($time/60) . '分鐘前';
        if ($time < 86400) return floor($time/3600) . '小時前';
        if ($time < 2592000) return floor($time/86400) . '天前';
        if ($time < 31536000) return floor($time/2592000) . '個月前';
        
        return floor($time/31536000) . '年前';
    } catch (Exception $e) {
        return $datetime;
    }
}

/**
 * 格式化檔案大小
 */
function format_file_size($bytes) {
    if ($bytes == 0) return '0 B';
    
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $factor = floor(log($bytes, 1024));
    
    return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
}

/**
 * 產生隨機字串
 */
function generate_random_string($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * 驗證電子郵件格式
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 驗證手機號碼格式 (台灣)
 */
function is_valid_phone($phone) {
    $pattern = '/^09\d{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * 清理輸入資料
 */
function clean_input($data) {
    if (is_array($data)) {
        return array_map('clean_input', $data);
    }
    return trim(strip_tags($data));
}

/**
 * 產生 CSRF Token 隱藏欄位
 */
function generate_csrf_token_field() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    $token = $_SESSION[CSRF_TOKEN_NAME];
    echo '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . htmlspecialchars($token) . '">';
}

/**
 * 驗證 CSRF Token
 */
function verify_csrf_token() {
    global $auth;

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST[CSRF_TOKEN_NAME] ?? '';
        if (!$auth->validateCSRFToken($token)) {
            die('CSRF token validation failed');
        }
    }
}

/**
 * 重導向到指定頁面
 */
function redirect($url, $permanent = false) {
    if ($permanent) {
        header('HTTP/1.1 301 Moved Permanently');
    }
    header('Location: ' . $url);
    exit;
}

/**
 * 顯示成功訊息
 */
function show_success($message) {
    $_SESSION['flash_success'] = $message;
}

/**
 * 顯示錯誤訊息
 */
function show_error($message) {
    $_SESSION['flash_error'] = $message;
}

/**
 * 顯示警告訊息
 */
function show_warning($message) {
    $_SESSION['flash_warning'] = $message;
}

/**
 * 顯示資訊訊息
 */
function show_info($message) {
    $_SESSION['flash_info'] = $message;
}

/**
 * 取得並清除 Flash 訊息
 */
function get_flash_messages() {
    $messages = [];
    
    $types = ['success', 'error', 'warning', 'info'];
    foreach ($types as $type) {
        $key = 'flash_' . $type;
        if (isset($_SESSION[$key])) {
            $messages[$type] = $_SESSION[$key];
            unset($_SESSION[$key]);
        }
    }
    
    return $messages;
}

/**
 * 顯示 Flash 訊息 HTML
 */
function display_flash_messages() {
    $messages = get_flash_messages();
    
    foreach ($messages as $type => $message) {
        $alertClass = [
            'success' => 'alert-success',
            'error' => 'alert-danger',
            'warning' => 'alert-warning',
            'info' => 'alert-info'
        ][$type] ?? 'alert-info';
        
        echo '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">';
        echo h($message);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
    }
}

/**
 * 分頁計算
 */
function calculate_pagination($total, $page = 1, $perPage = null) {
    $perPage = $perPage ?: DEFAULT_PAGE_SIZE;
    $page = max(1, intval($page));
    
    $totalPages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    
    return [
        'total' => $total,
        'page' => $page,
        'per_page' => $perPage,
        'total_pages' => $totalPages,
        'offset' => $offset,
        'has_prev' => $page > 1,
        'has_next' => $page < $totalPages,
        'prev_page' => $page > 1 ? $page - 1 : null,
        'next_page' => $page < $totalPages ? $page + 1 : null
    ];
}

/**
 * 產生分頁 HTML
 */
function render_pagination($pagination, $baseUrl) {
    if ($pagination['total_pages'] <= 1) return '';
    
    $html = '<nav aria-label="分頁導航">';
    $html .= '<ul class="pagination justify-content-center">';
    
    // 上一頁
    if ($pagination['has_prev']) {
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $pagination['prev_page'] . '">上一頁</a>';
        $html .= '</li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link">上一頁</span>';
        $html .= '</li>';
    }
    
    // 頁碼
    $start = max(1, $pagination['page'] - 2);
    $end = min($pagination['total_pages'], $pagination['page'] + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $pagination['page']) {
            $html .= '<li class="page-item active">';
            $html .= '<span class="page-link">' . $i . '</span>';
            $html .= '</li>';
        } else {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a>';
            $html .= '</li>';
        }
    }
    
    // 下一頁
    if ($pagination['has_next']) {
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $pagination['next_page'] . '">下一頁</a>';
        $html .= '</li>';
    } else {
        $html .= '<li class="page-item disabled">';
        $html .= '<span class="page-link">下一頁</span>';
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * 檢查檔案類型是否允許
 */
function is_allowed_file_type($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_FILE_TYPES);
}

/**
 * 檢查檔案 MIME 類型是否允許
 */
function is_allowed_mime_type($mimeType) {
    return in_array($mimeType, ALLOWED_MIME_TYPES);
}

/**
 * 產生安全的檔案名稱
 */
function generate_safe_filename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    
    // 清理檔案名稱
    $basename = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $basename);
    $basename = substr($basename, 0, 50); // 限制長度
    
    // 添加時間戳記避免重複
    $timestamp = date('YmdHis');
    
    return $basename . '_' . $timestamp . '.' . $extension;
}

/**
 * 記錄除錯訊息
 */
function debug_log($message, $data = null) {
    if (!DEBUG_MODE) return;
    
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message;
    if ($data !== null) {
        $logMessage .= ' | Data: ' . json_encode($data);
    }
    $logMessage .= "\n";
    
    $logFile = LOG_PATH . '/debug_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 檢查是否為 AJAX 請求
 */
function is_ajax() {
    return is_ajax_request();
}

/**
 * 回傳 JSON 回應
 */
function json_response($data, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 回傳成功的 JSON 回應
 */
function json_success($message, $data = null) {
    $response = ['success' => true, 'message' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    json_response($response);
}

/**
 * 回傳錯誤的 JSON 回應
 */
function json_error($message, $httpCode = 400) {
    json_response(['success' => false, 'message' => $message], $httpCode);
}
?>
