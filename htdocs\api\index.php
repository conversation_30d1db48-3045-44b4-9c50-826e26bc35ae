<?php
/**
 * API 路由器
 * 簡單的 API 端點路由
 */

// 設定 JSON 回應標頭
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置
require_once __DIR__ . '/../config/environment.php';

// 獲取請求路徑
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$path = str_replace('/api', '', $path);
$path = trim($path, '/');

// 簡單的路由
switch ($path) {
    case 'test':
        include __DIR__ . '/test.php';
        break;
        
    case 'status':
        // API 狀態檢查
        echo json_encode([
            'success' => true,
            'message' => 'SignAttend API v' . APP_VERSION,
            'endpoints' => [
                '/api/test' => 'API 連接測試',
                '/api/status' => 'API 狀態檢查'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        break;
        
    case '':
    case 'index':
        // API 根目錄
        echo json_encode([
            'success' => true,
            'message' => 'SignAttend API',
            'version' => APP_VERSION,
            'documentation' => '/api/status',
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        break;
        
    case 'register':
        // 註冊請求 - 記錄詳細資訊用於除錯
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'],
            'request_uri' => $_SERVER['REQUEST_URI'],
            'path' => $path,
            'post_data' => file_get_contents('php://input'),
            'headers' => getallheaders()
        ];

        $logFile = __DIR__ . '/../logs/register_debug_' . date('Y-m-d') . '.log';
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        file_put_contents($logFile, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

        // 重定向到後端處理
        include __DIR__ . '/../backend/index.php';
        break;

    default:
        // 404 錯誤
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'API 端點不存在',
            'path' => $path,
            'available_endpoints' => [
                '/api/test',
                '/api/status',
                '/api/register'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        break;
}
?>
