# SignAttend 安全目錄結構

## 📁 建議的目錄結構

```
v:\attendance.app.tn\
├── config\                           ← 敏感配置（htdocs 外面）
│   ├── secrets.production.php        ← 密碼、API 金鑰
│   ├── database.backup.php           ← 資料庫備份配置
│   └── mail.secrets.php              ← 郵件服務密碼
├── logs\                             ← 日誌文件（htdocs 外面）
│   ├── error_YYYY-MM-DD.log          ← 錯誤日誌
│   ├── api_YYYY-MM-DD.log            ← API 日誌
│   ├── security_YYYY-MM-DD.log       ← 安全日誌
│   └── access_YYYY-MM-DD.log         ← 訪問日誌
├── backups\                          ← 備份文件（htdocs 外面）
│   ├── database\
│   └── files\
├── temp\                             ← 臨時文件（htdocs 外面）
└── htdocs\                           ← 公開網站目錄
    ├── backend\                      ← 後端 API
    │   ├── config\                   ← 非敏感後端配置
    │   ├── controllers\
    │   ├── models\
    │   └── services\
    ├── config\                       ← 非敏感前端配置
    │   ├── config.production.php     ← 引用外部敏感配置
    │   └── environment.php
    ├── assets\                       ← 前端資源
    ├── pages\                        ← 前端頁面
    └── uploads\                      ← 用戶上傳文件
```

## 🔐 敏感文件清單

### 必須放在 htdocs 外面：
1. **密碼和金鑰**
   - 資料庫密碼
   - JWT 密鑰
   - API 金鑰
   - 加密密鑰

2. **日誌文件**
   - 錯誤日誌
   - 安全日誌
   - API 訪問日誌

3. **備份文件**
   - 資料庫備份
   - 文件備份

4. **配置文件**
   - 郵件服務密碼
   - 第三方服務金鑰

### 可以放在 htdocs 內（但要保護）：
1. **非敏感配置**
   - 應用程式名稱
   - 版本號
   - 功能開關

2. **公開資源**
   - CSS、JS 文件
   - 圖片文件
   - 字體文件

## 🛡️ 安全措施

### 1. .htaccess 保護
```apache
# 保護敏感文件
<Files "secrets.*.php">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>
```

### 2. 檔案權限
```bash
# 敏感配置文件
chmod 600 ../config/secrets.production.php

# 日誌目錄
chmod 755 ../logs/
chmod 644 ../logs/*.log

# 上傳目錄
chmod 755 htdocs/uploads/
```

### 3. 環境變數檢查
- 確保敏感配置文件存在
- 檢查預設密碼是否已修改
- 驗證檔案權限設定

## 📋 部署檢查清單

- [ ] 敏感配置文件已移到 htdocs 外面
- [ ] 預設密碼已修改
- [ ] 檔案權限已正確設定
- [ ] .htaccess 保護已啟用
- [ ] 日誌目錄已創建
- [ ] 備份目錄已創建
- [ ] 測試配置載入正常

## ⚠️ 重要提醒

1. **絕對不要**將密碼提交到版本控制系統
2. **定期更換**所有密鑰和密碼
3. **監控**日誌文件中的異常活動
4. **備份**敏感配置文件
5. **限制**敏感文件的訪問權限