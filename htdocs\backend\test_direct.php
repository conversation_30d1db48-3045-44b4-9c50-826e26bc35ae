<?php
// 直接測試 API 功能
require 'vendor/autoload.php';

use SignAttend\Controllers\AuthController;
use SignAttend\Controllers\MeetingController;
use SignAttend\Controllers\AttendeeController;

// 載入資料庫配置
$dbConfig = require 'config/database.php';

try {
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    echo "✓ 資料庫連接成功\n";

    // 測試 Meeting 控制器
    $meetingController = new MeetingController($pdo);
    echo "✓ MeetingController 初始化成功\n";

    // 測試 Attendee 控制器
    $attendeeController = new AttendeeController($pdo);
    echo "✓ AttendeeController 初始化成功\n";

    // 測試 Auth 控制器
    $authController = new AuthController($pdo);
    echo "✓ AuthController 初始化成功\n";

    echo "\n所有控制器初始化成功！\n";

} catch (Exception $e) {
    echo "✗ 錯誤: " . $e->getMessage() . "\n";
}
?>
