<?php
/**
 * SignAttend 環境配置切換器
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 防止直接訪問
if (!defined('SIGNATTEND_INIT')) {
    die('Direct access not allowed');
}

// 環境檢測
function detectEnvironment() {
    // 檢查是否為正式環境
    $isProduction = false;
    
    // 方法1: 檢查域名
    if (isset($_SERVER['HTTP_HOST'])) {
        $host = $_SERVER['HTTP_HOST'];
        if (strpos($host, 'attendance.app.tn') !== false) {
            $isProduction = true;
        }
    }
    
    // 方法2: 檢查環境變數
    if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'production') {
        $isProduction = true;
    }
    
    // 方法3: 檢查配置文件
    if (file_exists(__DIR__ . '/.env.production')) {
        $isProduction = true;
    }
    
    return $isProduction ? 'production' : 'development';
}

// 載入對應環境的配置
$environment = detectEnvironment();

switch ($environment) {
    case 'production':
        require_once __DIR__ . '/config.production.php';
        break;
    
    case 'development':
    default:
        require_once __DIR__ . '/config.php';
        break;
}

// 定義當前環境常數
define('APP_ENVIRONMENT', $environment);
define('IS_PRODUCTION', $environment === 'production');
define('IS_DEVELOPMENT', $environment === 'development');

// 環境特定的初始化
if (IS_PRODUCTION) {
    // 正式環境特定設定
    ini_set('expose_php', 0);
    ini_set('allow_url_fopen', 0);
    ini_set('allow_url_include', 0);
    
    // 移除 X-Powered-By 標頭
    if (function_exists('header_remove')) {
        header_remove('X-Powered-By');
    }
    
    // 設定更嚴格的 session 配置 (只在 session 未啟動時)
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.name', 'SIGNATTEND_PROD_SESSION');
        ini_set('session.gc_maxlifetime', 7200); // 2 小時
    }
    
} else {
    // 開發環境特定設定 (只在 session 未啟動時)
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.name', 'SIGNATTEND_DEV_SESSION');
        ini_set('session.gc_maxlifetime', 86400); // 24 小時
    }
}

// 輔助函數
function getEnvironment() {
    return APP_ENVIRONMENT;
}

function isProduction() {
    return IS_PRODUCTION;
}

function isDevelopment() {
    return IS_DEVELOPMENT;
}

function getConfigValue($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

// 環境資訊記錄
if (LOG_ERRORS) {
    $logMessage = sprintf(
        "[%s] Environment: %s | Host: %s | IP: %s\n",
        date('Y-m-d H:i:s'),
        $environment,
        $_SERVER['HTTP_HOST'] ?? 'unknown',
        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    );
    
    $logFile = LOG_PATH . '/environment_' . date('Y-m-d') . '.log';
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}
