<?php
/**
 * 模擬 Web 登入測試
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

echo "=== Web 登入測試 ===\n";

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "環境載入完成\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

echo "函數載入完成\n";

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

echo "類別載入完成\n";

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

echo "物件初始化完成\n";

// 模擬 POST 請求
$_POST['email'] = '<EMAIL>';
$_POST['password'] = 'wrong_password';

echo "\n=== 模擬登入失敗 ===\n";
echo "嘗試登入: " . $_POST['email'] . "\n";

$result = $auth->login($_POST['email'], $_POST['password'], false);

echo "登入結果: " . ($result['success'] ? '成功' : '失敗') . "\n";
echo "訊息: " . $result['message'] . "\n";

// 檢查日誌
echo "\n=== 檢查日誌記錄 ===\n";
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';

if (file_exists($authLogFile)) {
    $content = file_get_contents($authLogFile);
    $lines = explode("\n", trim($content));
    
    echo "日誌文件存在，共 " . count($lines) . " 行\n";
    
    // 查找最新的記錄
    $lastLine = end($lines);
    if (!empty($lastLine)) {
        $data = json_decode($lastLine, true);
        if ($data && $data['user_email'] === $_POST['email']) {
            echo "✅ 找到對應的登入失敗記錄\n";
            echo "時間: " . $data['timestamp'] . "\n";
            echo "動作: " . $data['action'] . "\n";
            echo "描述: " . $data['description'] . "\n";
        } else {
            echo "❌ 沒有找到對應的登入失敗記錄\n";
            echo "最後一行: " . $lastLine . "\n";
        }
    }
} else {
    echo "❌ 日誌文件不存在\n";
}

echo "\n測試完成！\n";
?>
