<?php
/**
 * 測試 AJAX 登入請求
 */

define('SIGNATTEND_INIT', true);
require_once __DIR__ . '/../config/environment.php';

echo "=== 測試 AJAX 登入請求 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

// 啟動 Session
session_start();

// 載入必要的文件
require_once INCLUDES_PATH . '/functions.php';
require_once UTILS_PATH . '/Auth.php';

// 創建 Auth 實例
$auth = new Auth();

// 生成 CSRF Token
$csrfToken = $auth->generateCSRFToken();
echo "CSRF Token: " . substr($csrfToken, 0, 10) . "...\n\n";

// 模擬 AJAX 登入請求
$_POST = [
    'ajax_login' => '1',
    'email' => '<EMAIL>',
    'password' => 'aaaaaa',
    'csrf_token' => $csrfToken
];

echo "模擬 POST 數據:\n";
echo json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 測試 CSRF 驗證
echo "--- CSRF 驗證測試 ---\n";
$csrfValid = $auth->validateCSRFToken($_POST['csrf_token']);
echo "CSRF 驗證結果: " . ($csrfValid ? '通過' : '失敗') . "\n\n";

// 測試登入
echo "--- 登入測試 ---\n";
$result = $auth->login($_POST['email'], $_POST['password'], false);
echo "登入結果:\n";
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 檢查 Session
echo "--- Session 檢查 ---\n";
echo "Session 用戶: " . (isset($_SESSION['user']) ? json_encode($_SESSION['user'], JSON_UNESCAPED_UNICODE) : '未設置') . "\n";
echo "Session 認證 Token: " . (isset($_SESSION['auth_token']) ? '已設置' : '未設置') . "\n";
echo "登入狀態: " . ($auth->isLoggedIn() ? '已登入' : '未登入') . "\n\n";

echo "=== 測試完成 ===\n";
?>
