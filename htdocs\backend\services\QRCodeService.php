<?php

namespace SignAttend\Services;

use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\LabelAlignment;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;

class QRCodeService {
    
    /**
     * 為會議生成 QR Code
     */
    public function generateMeetingQRCode($meetingId, $meetingName = null) {
        // 構建簽到 URL
        $checkinUrl = $this->buildCheckinUrl($meetingId);
        
        // 生成 QR Code
        $result = Builder::create()
            ->writer(new PngWriter())
            ->data($checkinUrl)
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
            ->size(300)
            ->margin(10)
            ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
            ->build();
        
        return [
            'qr_code_data' => base64_encode($result->getString()),
            'qr_code_url' => $checkinUrl,
            'mime_type' => $result->getMimeType()
        ];
    }
    
    /**
     * 為參與者生成個人 QR Code
     */
    public function generateAttendeeQRCode($attendeeId, $meetingId, $attendeeName = null) {
        // 構建個人簽到 URL
        $checkinUrl = $this->buildAttendeeCheckinUrl($attendeeId, $meetingId);
        
        // 生成 QR Code
        $result = Builder::create()
            ->writer(new PngWriter())
            ->data($checkinUrl)
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
            ->size(250)
            ->margin(10)
            ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
            ->build();
        
        return [
            'qr_code_data' => base64_encode($result->getString()),
            'qr_code_url' => $checkinUrl,
            'mime_type' => $result->getMimeType()
        ];
    }
    
    /**
     * 生成帶有 Logo 的 QR Code
     */
    public function generateQRCodeWithLogo($data, $logoPath = null, $size = 300) {
        $builder = Builder::create()
            ->writer(new PngWriter())
            ->data($data)
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(ErrorCorrectionLevel::High) // 使用高錯誤修正等級以支援 Logo
            ->size($size)
            ->margin(10)
            ->roundBlockSizeMode(RoundBlockSizeMode::Margin);
        
        // 如果提供了 Logo 路徑且檔案存在
        if ($logoPath && file_exists($logoPath)) {
            $logo = Logo::create($logoPath)
                ->setResizeToWidth(50)
                ->setPunchoutBackground(true);
            $builder->logo($logo);
        }
        
        $result = $builder->build();
        
        return [
            'qr_code_data' => base64_encode($result->getString()),
            'qr_code_url' => $data,
            'mime_type' => $result->getMimeType()
        ];
    }
    
    /**
     * 構建會議簽到 URL
     */
    private function buildCheckinUrl($meetingId) {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . "/checkin?meeting_id=" . urlencode($meetingId);
    }
    
    /**
     * 構建參與者個人簽到 URL
     */
    private function buildAttendeeCheckinUrl($attendeeId, $meetingId) {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . "/checkin?attendee_id=" . urlencode($attendeeId) . "&meeting_id=" . urlencode($meetingId);
    }
    
    /**
     * 取得基礎 URL
     */
    private function getBaseUrl() {
        // 可以從配置檔案或環境變數中取得
        return 'http://localhost/SignAttend/frontend';
    }
    
    /**
     * 儲存 QR Code 到檔案
     */
    public function saveQRCodeToFile($qrCodeData, $filename, $directory = null) {
        $directory = $directory ?: dirname(__DIR__) . '/uploads/qrcodes';
        
        // 確保目錄存在
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $filepath = $directory . '/' . $filename;
        $imageData = base64_decode($qrCodeData);
        
        if (file_put_contents($filepath, $imageData)) {
            return $filepath;
        }
        
        return false;
    }
    
    /**
     * 生成批次 QR Code（為會議的所有參與者）
     */
    public function generateBatchQRCodes($meetingId, $attendees) {
        $qrCodes = [];
        
        foreach ($attendees as $attendee) {
            $qrCode = $this->generateAttendeeQRCode(
                $attendee['id'], 
                $meetingId, 
                $attendee['name']
            );
            
            $qrCodes[$attendee['id']] = [
                'attendee' => $attendee,
                'qr_code' => $qrCode
            ];
        }
        
        return $qrCodes;
    }
}
?>
