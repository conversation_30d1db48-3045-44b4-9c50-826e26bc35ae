<?php
/**
 * 登入頁面 - 簡化版本
 * 版本: 3.0.0 - 原生 PHP 登入系統 (2025-07-09)
 */

// 啟動 Session
session_start();

// 處理登出
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 資料庫配置
$db_host = 'sql302.byetcluster.com';
$db_name = 'uoolo_38699510_signattend';
$db_user = 'uoolo_38699510';
$db_pass = 'kai@0932540826';

$message = '';
$success = false;

// 處理登入
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    
    if (empty($email) || empty($password)) {
        $message = '請輸入電子郵件和密碼';
    } else {
        try {
            // 連接資料庫
            $pdo = new PDO(
                "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4",
                $db_user,
                $db_pass,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // 查找用戶
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // 登入成功 - 設置完整的 Session 資料
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['logged_in'] = true;
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();

                // 設置與原系統兼容的 Session 資料
                $_SESSION['user'] = [
                    'id' => $user['id'],
                    'email' => $user['email']
                ];
                $_SESSION['profile'] = [
                    'id' => $user['id'],
                    'name' => $user['name'] ?? $user['email'],
                    'email' => $user['email']
                ];
                $_SESSION['auth_token'] = 'simple_' . bin2hex(random_bytes(16));

                $success = true;
                $message = '登入成功！正在跳轉到儀表板...';

                // 使用 JavaScript 重定向，避免 header 重定向問題
                echo '<script>setTimeout(function(){ window.location.href = "dashboard.php"; }, 1500);</script>';
            } else {
                $message = '電子郵件或密碼錯誤';
            }
            
        } catch (PDOException $e) {
            $message = '資料庫連接失敗：' . $e->getMessage();
        }
    }
}

// 檢查是否已登入
$isLoggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入 - SignAttend</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 420px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }
        
        .login-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
            font-size: 1rem;
        }
        
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .message {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .demo-section {
            margin-top: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .demo-section h3 {
            margin-bottom: 0.75rem;
            color: #333;
            font-size: 1rem;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .demo-btn {
            padding: 0.4rem 0.8rem;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.85rem;
            cursor: pointer;
            border: none;
            transition: background 0.2s;
        }
        
        .demo-btn:hover {
            background: #5a6268;
        }
        
        .links-section {
            margin-top: 1.5rem;
            text-align: center;
        }
        
        .links-section a {
            display: inline-block;
            margin: 0.25rem 0.5rem;
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.2s;
        }
        
        .links-section a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .status {
            margin-top: 1rem;
            padding: 0.5rem;
            background: #e9ecef;
            border-radius: 8px;
            font-size: 0.85rem;
            color: #666;
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
            }
            
            .demo-buttons {
                flex-direction: column;
            }
            
            .demo-btn {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 SignAttend</h1>
            <p>員工出勤管理系統</p>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?= $success ? 'success' : 'error' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="email">📧 電子郵件</label>
                <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">🔒 密碼</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn">登入</button>
        </form>
        
        <div class="demo-section">
            <h3>🎯 快速演示登入</h3>
            <div class="demo-buttons">
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'aaaaaa')">🎯 演示帳號</button>
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'demo123')">🧪 測試帳號</button>
                <button class="demo-btn" onclick="fillDemo('<EMAIL>', 'test123')">👥 訪客帳號</button>
            </div>
        </div>
        
        <div class="links-section">
            <a href="#" onclick="alert('忘記密碼功能開發中')">忘記密碼？</a>
            <a href="#" onclick="alert('註冊功能開發中')">註冊帳號</a>
        </div>
        
        <div class="status">
            <strong>系統狀態：</strong><br>
            Session ID: <?= session_id() ?><br>
            登入狀態: <?= $isLoggedIn ? '✅ 已登入' : '❌ 未登入' ?><br>
            時間: <?= date('Y-m-d H:i:s') ?>
        </div>
    </div>
    
    <script>
        function fillDemo(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>
