<?php
/**
 * 登入頁面 - 使用有效的登入邏輯
 * 版本: 2.1.0 - 修復 AJAX 日誌寫入問題 (2025-06-26)
 */

// 防止瀏覽器緩存
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 記錄頁面訪問
$accessLogFile = LOG_PATH . '/page_access_' . date('Y-m-d') . '.log';
$accessMessage = sprintf(
    "[%s] NEW LOGIN PAGE ACCESS: Method=%s, Host=%s, IP=%s, UserAgent=%s\n",
    date('Y-m-d H:i:s'),
    $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    $_SERVER['HTTP_HOST'] ?? 'unknown',
    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100)
);
file_put_contents($accessLogFile, $accessMessage, FILE_APPEND | LOCK_EX);

// 記錄所有 POST 請求到調試日誌
$debugMessage = sprintf(
    "[%s] ALL POST DATA: %s\n",
    date('Y-m-d H:i:s'),
    json_encode($_POST, JSON_UNESCAPED_UNICODE)
);
file_put_contents($accessLogFile, $debugMessage, FILE_APPEND | LOCK_EX);

// 處理 AJAX 登入請求 - 使用調試版本的有效邏輯
if (isset($_POST['ajax_login']) && $_POST['ajax_login'] === '1') {
    header('Content-Type: application/json');

    // 記錄調試信息到專用文件
    $debugLogFile = LOG_PATH . '/pages_login_debug_' . date('Y-m-d') . '.log';

    function writeDebugLog($message) {
        global $debugLogFile;
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        $result = file_put_contents($debugLogFile, $logMessage, FILE_APPEND | LOCK_EX);
        // 強制刷新檔案系統緩存
        if ($result !== false) {
            clearstatcache(true, $debugLogFile);
        }
    }

    writeDebugLog("=== PAGES LOGIN AJAX 開始 ===");
    writeDebugLog("POST 數據: " . json_encode($_POST));

    $response = [
        'success' => false,
        'message' => '',
        'redirect' => '',
        'debug_info' => []
    ];

    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    writeDebugLog("Email: $email, Password: " . (!empty($password) ? '[有值]' : '[空]'));

    if (empty($email) || empty($password)) {
        $response['message'] = '請輸入電子郵件和密碼';
        writeDebugLog("驗證失敗: 空的 email 或密碼");
    } else {
        // 記錄登入前的資料庫記錄狀態
        require_once UTILS_PATH . '/DatabaseLogger.php';
        $dbLogger = new DatabaseLogger();
        $beforeCount = count($dbLogger->getRecentLogs(1, $email));

        writeDebugLog("登入前資料庫記錄數: $beforeCount");

        // 執行登入
        writeDebugLog("開始執行 auth->login()");
        $result = $auth->login($email, $password, $rememberMe);
        writeDebugLog("auth->login() 完成，結果: " . json_encode($result));

        // 記錄登入後的資料庫記錄狀態
        $afterCount = count($dbLogger->getRecentLogs(1, $email));
        writeDebugLog("登入後資料庫記錄數: $afterCount");

        if ($result['success']) {
            $response['success'] = true;
            $response['message'] = '登入成功！正在跳轉...';
            writeDebugLog("登入成功");

            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);

            // 簡單的 URL 構建
            if (strpos($redirectUrl, 'http') === 0) {
                $response['redirect'] = $redirectUrl;
            } else {
                $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') .
                          '://' . $_SERVER['HTTP_HOST'] .
                          dirname($_SERVER['REQUEST_URI']) . '/';
                $response['redirect'] = $baseUrl . $redirectUrl;
            }
        } else {
            $response['message'] = $result['message'] ?? '登入失敗';
            writeDebugLog("登入失敗: " . $response['message']);

            // 如果資料庫記錄沒有增長，記錄調試信息
            if ($afterCount <= $beforeCount) {
                writeDebugLog("資料庫記錄沒有增長，可能是記錄失敗");
            } else {
                writeDebugLog("資料庫記錄已增長: " . ($afterCount - $beforeCount) . " 筆");
            }
        }

        $response['debug_info'] = [
            'before_count' => $beforeCount,
            'after_count' => $afterCount,
            'record_change' => $afterCount - $beforeCount,
            'storage_type' => 'database',
            'table_name' => 'login_logs'
        ];
    }

    writeDebugLog("返回響應: " . json_encode($response));
    writeDebugLog("=== PAGES LOGIN AJAX 結束 ===");

    echo json_encode($response);
    exit;
}

// 處理 JavaScript 調試日誌
if (isset($_POST['debug_js_log'])) {
    $jsLogData = json_decode($_POST['debug_js_log'], true);
    if ($jsLogData) {
        $jsLogMessage = sprintf(
            "[%s] NEW LOGIN JS DEBUG: %s\n",
            date('Y-m-d H:i:s'),
            json_encode($jsLogData, JSON_UNESCAPED_UNICODE)
        );
        file_put_contents($accessLogFile, $jsLogMessage, FILE_APPEND | LOCK_EX);
    }
    // 如果只是調試日誌請求，直接返回
    if (!isset($_POST['email']) && !isset($_POST['password'])) {
        http_response_code(200);
        exit;
    }
}

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    $redirectMessage = sprintf(
        "[%s] NEW LOGIN ALREADY LOGGED IN: Redirecting to dashboard\n",
        date('Y-m-d H:i:s')
    );
    file_put_contents($accessLogFile, $redirectMessage, FILE_APPEND | LOCK_EX);
    
    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理登入表單提交 - 使用簡化測試頁面的有效邏輯
$result = null;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    $rememberMe = isset($_POST['remember_me']);
    
    $loginMessage = sprintf(
        "[%s] NEW LOGIN ATTEMPT: Email=%s, HasPassword=%s, RememberMe=%s\n",
        date('Y-m-d H:i:s'),
        $email,
        !empty($password) ? 'yes' : 'no',
        $rememberMe ? 'yes' : 'no'
    );
    file_put_contents($accessLogFile, $loginMessage, FILE_APPEND | LOCK_EX);
    
    if (empty($email) || empty($password)) {
        $error = '請輸入電子郵件和密碼';
        
        $errorMessage = sprintf(
            "[%s] NEW LOGIN VALIDATION ERROR: %s\n",
            date('Y-m-d H:i:s'),
            $error
        );
        file_put_contents($accessLogFile, $errorMessage, FILE_APPEND | LOCK_EX);
    } else {
        // 記錄登入前的認證日誌狀態
        $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
        $beforeSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        
        // 執行登入
        $result = $auth->login($email, $password, $rememberMe);
        
        // 記錄登入後的認證日誌狀態
        $afterSize = file_exists($authLogFile) ? filesize($authLogFile) : 0;
        
        $resultMessage = sprintf(
            "[%s] NEW LOGIN RESULT: Success=%s, Message=%s, AuthLogBefore=%d, AuthLogAfter=%d\n",
            date('Y-m-d H:i:s'),
            $result['success'] ? 'true' : 'false',
            $result['message'] ?? 'no message',
            $beforeSize,
            $afterSize
        );
        file_put_contents($accessLogFile, $resultMessage, FILE_APPEND | LOCK_EX);
        
        if ($result['success']) {
            $success = '登入成功！正在跳轉...';
            
            // 檢查是否有登入後重定向
            $redirectUrl = $_SESSION['redirect_after_login'] ?? 'pages/dashboard.php';
            unset($_SESSION['redirect_after_login']);

            // 使用 JavaScript 延遲跳轉以顯示成功訊息
            echo "<script>
                setTimeout(function() {
                    window.location.href = '" . page_url($redirectUrl) . "';
                }, 1500);
            </script>";
        } else {
            $error = $result['message'] ?? '登入失敗';
            
            // 如果認證日誌沒有增長，手動記錄（使用簡化測試頁面的邏輯）
            if ($afterSize <= $beforeSize) {
                $manualLogData = [
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_id' => null,
                    'user_email' => $email,
                    'action' => 'login_failed_manual_new_pages',
                    'description' => '新登入頁面登入失敗：' . $error,
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];
                
                $manualLogMessage = json_encode($manualLogData, JSON_UNESCAPED_UNICODE) . "\n";
                file_put_contents($authLogFile, $manualLogMessage, FILE_APPEND | LOCK_EX);
                
                $manualMessage = sprintf(
                    "[%s] NEW LOGIN MANUAL LOG: Written to auth log\n",
                    date('Y-m-d H:i:s')
                );
                file_put_contents($accessLogFile, $manualMessage, FILE_APPEND | LOCK_EX);
            }
        }
    }
}

// 在登入處理部分添加直接日誌記錄
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['ajax_login'])) {
    // 現有的登入處理代碼...
    
    // 添加直接日誌記錄
    $directLogFile = LOG_PATH . '/direct_login_' . date('Y-m-d') . '.log';
    $directLogMessage = date('Y-m-d H:i:s') . " - 登入嘗試: " . $email . " - " . ($result['success'] ? '成功' : '失敗') . "\n";
    
    // 嘗試直接寫入
    $directWriteResult = file_put_contents($directLogFile, $directLogMessage, FILE_APPEND);
    
    // 顯示調試信息
    echo '<div style="background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">';
    echo '<strong>日誌調試信息:</strong><br>';
    echo '外層日誌路徑: ' . dirname(dirname(__FILE__)) . '/logs/<br>';
    echo '直接日誌文件: ' . $directLogFile . '<br>';
    echo '寫入結果: ' . ($directWriteResult ? '成功' : '失敗') . '<br>';
    echo '寫入大小: ' . $directWriteResult . ' bytes<br>';
    echo '</div>';
}

// 頁面標題
$pageTitle = 'SignAttend - 登入';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?></title>
    <meta name="description" content="SignAttend 智慧簽到管理系統">
    <meta name="author" content="SignAttend System">

    <meta property="og:title" content="SignAttend - 智慧簽到系統">
    <meta property="og:description" content="SignAttend 智慧簽到管理系統">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 30px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #667eea;
            font-weight: bold;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-group label {
            display: flex;
            align-items: center;
        }

        .form-group input[type="email"],
        .form-group input[type="password"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input[type="email"]:focus,
        .form-group input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #666;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .btn-loading {
            display: none;
        }

        /* 演示按鈕樣式 */
        .demo-btn {
            transition: all 0.3s ease;
            border: none;
            border-radius: 8px;
            font-weight: 500;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .demo-btn:active {
            transform: translateY(0);
        }

        /* 脈衝動畫 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }



        .alert-danger {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .alert-success {
            background: #efe;
            color: #3c3;
            border: 1px solid #cfc;
        }

        .login-footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-top: 1px solid #e1e5e9;
        }

        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        .copyright {
            margin-top: 15px;
            font-size: 12px;
            color: #999;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 10px;
            }

            .login-header {
                padding: 30px 20px;
            }

            .login-form {
                padding: 30px 20px;
            }

            .login-footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">SA</div>
                <h1>SignAttend</h1>
                <p>員工簽到系統</p>
            </div>

            <!-- 動態訊息會通過 JavaScript 插入到這裡 -->

            <form id="loginForm" method="POST" action="" class="login-form">
                <?php generate_csrf_token_field(); ?>
                
                <div class="form-group">
                    <label for="email">
                        📧 電子郵件
                    </label>
                    <input type="email" id="email" name="email" required
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                           placeholder="請輸入您的電子郵件"
                           autocomplete="email">
                </div>

                <div class="form-group">
                    <label for="password">
                        🔒 密碼
                    </label>
                    <input type="password" id="password" name="password" required
                           placeholder="請輸入您的密碼"
                           autocomplete="current-password">
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me" id="remember_me">
                        <span class="checkmark"></span>
                        記住我
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                    <span class="btn-text">登入</span>
                    <span class="btn-loading" style="display: none;">
                        ⏳ 登入中...
                    </span>
                </button>

                <!-- 演示按鈕區域 -->
                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e1e5e9;">
                    <p style="text-align: center; color: #666; font-size: 14px; margin-bottom: 10px;">
                        🎯 快速演示登入
                    </p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                        <button type="button" class="btn demo-btn" data-email="<EMAIL>" data-password="demo123"
                                style="background: #28a745; color: white; font-size: 14px; padding: 10px;">
                            🎯 演示帳號
                        </button>
                        <button type="button" class="btn demo-btn" data-email="<EMAIL>" data-password="test123"
                                style="background: #dc3545; color: white; font-size: 14px; padding: 10px;">
                            🧪 測試帳號
                        </button>
                    </div>

                    <button type="button" class="btn demo-btn" data-email="<EMAIL>" data-password="guest123"
                            style="background: #17a2b8; color: white; font-size: 14px; padding: 8px; width: 100%;">
                        👥 訪客帳號
                    </button>
                </div>


            </form>

            <div class="login-footer">
                <p><a href="forgot-password.php">忘記密碼？</a></p>
                <p class="mt-2"><a href="register.php">註冊帳號</a></p>
                <p class="copyright">© 2024 SignAttend. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script>
        function setLoading(loading) {
            const btn = document.getElementById('loginBtn');
            const btnText = btn.querySelector('.btn-text');
            const btnLoading = btn.querySelector('.btn-loading');

            if (loading) {
                btn.disabled = true;
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                btnText.style.display = 'inline-block';
                btnLoading.style.display = 'none';
            }
        }

        function showMessage(message, type) {
            // 移除現有的訊息
            clearMessages();

            // 創建新的訊息元素
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-' + (type === 'error' ? 'danger' : 'success');
            alertDiv.innerHTML = (type === 'error' ? '❌ ' : '✅ ') + message;

            // 插入到表單前面
            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(alertDiv, form);

            // 平滑顯示動畫
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateY(-10px)';
            alertDiv.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                alertDiv.style.opacity = '1';
                alertDiv.style.transform = 'translateY(0)';
            }, 10);
        }

        function clearMessages() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            });
        }

        // AJAX 表單提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止默認表單提交

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember_me').checked;

            // 清除之前的錯誤訊息
            clearMessages();

            // 顯示載入狀態
            setLoading(true);

            // 準備表單數據
            const formData = new FormData();
            formData.append('ajax_login', '1');
            formData.append('email', email);
            formData.append('password', password);
            if (rememberMe) {
                formData.append('remember_me', '1');
            }

            // 添加 CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]');
            if (csrfToken) {
                formData.append('csrf_token', csrfToken.value);
            }

            console.log('AJAX 登入提交:', {
                email: email,
                hasPassword: password ? true : false,
                rememberMe: rememberMe
            });

            // 發送 AJAX 請求
            fetch('<?= $_SERVER['PHP_SELF'] ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('AJAX 響應狀態:', response.status);
                console.log('AJAX 響應頭:', response.headers.get('content-type'));
                return response.text().then(text => {
                    console.log('AJAX 原始響應:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON 解析錯誤:', e);
                        console.error('響應內容:', text);
                        throw new Error('服務器返回了無效的 JSON 響應');
                    }
                });
            })
            .then(data => {
                console.log('AJAX 解析後的數據:', data);
                setLoading(false);

                if (data.success) {
                    showMessage(data.message, 'success');
                    // 延遲跳轉
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                } else {
                    showMessage(data.message, 'error');
                    // 重新聚焦到密碼欄位
                    document.getElementById('password').focus();
                    document.getElementById('password').select();
                }
            })
            .catch(error => {
                setLoading(false);
                console.error('登入錯誤:', error);
                showMessage('登入請求失敗，請稍後再試', 'error');
            });
        });



        // 演示按鈕功能
        document.addEventListener('DOMContentLoaded', function() {
            const demoBtns = document.querySelectorAll('.demo-btn');

            demoBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const email = this.getAttribute('data-email');
                    const password = this.getAttribute('data-password');

                    // 填入表單
                    document.getElementById('email').value = email;
                    document.getElementById('password').value = password;

                    // 顯示提示訊息
                    showMessage(`已填入 ${email} 的登入資訊，點擊登入按鈕即可登入`, 'success');

                    // 高亮顯示登入按鈕
                    const loginBtn = document.getElementById('loginBtn');
                    loginBtn.style.animation = 'pulse 1s ease-in-out 3';

                    // 自動聚焦到登入按鈕
                    setTimeout(() => {
                        loginBtn.focus();
                        loginBtn.style.animation = '';
                    }, 3000);
                });
            });
        });

        // 頁面載入完成後重置載入狀態
        window.addEventListener('load', function() {
            setLoading(false);
        });
    </script>
</body>
</html>

