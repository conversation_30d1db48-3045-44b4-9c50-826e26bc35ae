<?php
/**
 * 檢查最近的登入日誌
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入配置
require_once __DIR__ . '/config/environment.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即時日誌監控</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .log-entry { background: #f5f5f5; margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #007cba; }
        .log-failed { border-left-color: #dc3545; }
        .log-success { border-left-color: #28a745; }
        .timestamp { font-weight: bold; color: #666; }
        .email { color: #007cba; font-weight: bold; }
        .description { margin: 5px 0; }
        .meta { font-size: 12px; color: #666; }
        .refresh-btn { background: #007cba; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>🔍 即時登入日誌監控</h1>
    
    <button class="refresh-btn" onclick="location.reload()">🔄 重新整理</button>
    
    <p><strong>日誌文件位置:</strong> <code><?= LOG_PATH ?>/auth_<?= date('Y-m-d') ?>.log</code></p>
    <p><strong>當前時間:</strong> <?= date('Y-m-d H:i:s') ?></p>
    <p><strong>當前環境:</strong> <?= defined('APP_ENVIRONMENT') ? APP_ENVIRONMENT : '未知' ?></p>
    
    <hr>
    
    <h2>📋 最近 10 筆認證記錄</h2>
    
    <?php
    $authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
    
    if (file_exists($authLogFile)) {
        $content = file_get_contents($authLogFile);
        $lines = explode("\n", trim($content));
        $lines = array_filter($lines); // 移除空行
        
        // 取最後 10 筆記錄
        $recentLines = array_slice($lines, -10);
        $recentLines = array_reverse($recentLines); // 最新的在上面
        
        if (empty($recentLines)) {
            echo "<p>今日尚無認證記錄</p>";
        } else {
            foreach ($recentLines as $line) {
                $data = json_decode($line, true);
                if ($data) {
                    $isSuccess = strpos($data['action'], 'success') !== false || strpos($data['action'], 'login') === 0;
                    $isFailed = strpos($data['action'], 'failed') !== false;
                    
                    $cssClass = 'log-entry';
                    if ($isFailed) $cssClass .= ' log-failed';
                    if ($isSuccess) $cssClass .= ' log-success';
                    
                    echo "<div class='{$cssClass}'>";
                    echo "<div class='timestamp'>⏰ " . htmlspecialchars($data['timestamp']) . "</div>";
                    echo "<div class='email'>👤 " . htmlspecialchars($data['user_email'] ?? '未知用戶') . "</div>";
                    echo "<div class='description'>📝 " . htmlspecialchars($data['description']) . "</div>";
                    echo "<div class='meta'>";
                    echo "🌐 IP: " . htmlspecialchars($data['ip_address']) . " | ";
                    echo "🔧 動作: " . htmlspecialchars($data['action']) . " | ";
                    echo "🖥️ 瀏覽器: " . htmlspecialchars(substr($data['user_agent'], 0, 50)) . "...";
                    echo "</div>";
                    echo "</div>";
                } else {
                    // 處理非 JSON 格式的舊記錄
                    echo "<div class='log-entry'>";
                    echo "<div class='description'>📝 " . htmlspecialchars($line) . "</div>";
                    echo "</div>";
                }
            }
        }
        
        echo "<p><strong>總記錄數:</strong> " . count($lines) . " 筆</p>";
        echo "<p><strong>文件大小:</strong> " . filesize($authLogFile) . " bytes</p>";
        echo "<p><strong>最後修改:</strong> " . date('Y-m-d H:i:s', filemtime($authLogFile)) . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ 今日認證日誌文件不存在</p>";
    }
    ?>
    
    <hr>
    
    <h3>🧪 測試說明</h3>
    <ol>
        <li>開啟新分頁訪問 <a href="/pages/login.php" target="_blank">登入頁面</a></li>
        <li>輸入任意 email 和錯誤密碼嘗試登入</li>
        <li>回到此頁面點擊「重新整理」按鈕</li>
        <li>檢查是否出現新的登入失敗記錄</li>
    </ol>
    
    <script>
        // 每 30 秒自動重新整理
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
