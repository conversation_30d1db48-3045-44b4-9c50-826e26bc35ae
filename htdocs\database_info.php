<?php
/**
 * 顯示資料庫連接信息和 login_logs 表位置
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫信息</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .info-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #dee2e6;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 資料庫連接信息</h1>
        
        <div class="info-box success">
            <h3>✅ 登入記錄資料表位置</h3>
            <p><strong>資料表名稱：</strong> <code>login_logs</code></p>
            <p><strong>資料庫名稱：</strong> <code><?= DB_NAME ?></code></p>
            <p><strong>主機：</strong> <code><?= DB_HOST ?></code></p>
            <p><strong>用戶：</strong> <code><?= DB_USER ?></code></p>
        </div>

        <?php
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            echo '<div class="info-box success">';
            echo '<h3>✅ 資料庫連接成功</h3>';
            
            // 檢查 login_logs 表
            $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
            if ($stmt->rowCount() > 0) {
                echo '<p><strong>login_logs 表狀態：</strong> ✅ 存在</p>';
                
                // 獲取記錄數量
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs");
                $count = $stmt->fetch()['count'];
                echo '<p><strong>記錄數量：</strong> ' . $count . ' 筆</p>';
                
                if ($count > 0) {
                    // 顯示最新記錄
                    $stmt = $pdo->query("
                        SELECT user_email, action, success, created_at 
                        FROM login_logs 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    ");
                    $logs = $stmt->fetchAll();
                    
                    echo '<p><strong>最新記錄：</strong></p>';
                    echo '<table>';
                    echo '<tr><th>郵箱</th><th>動作</th><th>結果</th><th>時間</th></tr>';
                    foreach ($logs as $log) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($log['user_email']) . '</td>';
                        echo '<td>' . htmlspecialchars($log['action']) . '</td>';
                        echo '<td>' . ($log['success'] ? '✅ 成功' : '❌ 失敗') . '</td>';
                        echo '<td>' . $log['created_at'] . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                }
            } else {
                echo '<p><strong>login_logs 表狀態：</strong> ❌ 不存在</p>';
            }
            echo '</div>';
            
        } catch (PDOException $e) {
            echo '<div class="info-box error">';
            echo '<h3>❌ 資料庫連接失敗</h3>';
            echo '<p>錯誤信息：' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>

        <div class="info-box">
            <h3>🔧 如何在資料庫管理工具中查看</h3>
            
            <h4>1. phpMyAdmin</h4>
            <p>如果您使用 phpMyAdmin：</p>
            <ol>
                <li>打開 phpMyAdmin (通常是 http://localhost/phpmyadmin)</li>
                <li>選擇資料庫：<code><?= DB_NAME ?></code></li>
                <li>在左側表列表中找到：<code>login_logs</code></li>
                <li>點擊表名即可查看記錄</li>
            </ol>
            
            <h4>2. MySQL Workbench</h4>
            <p>如果您使用 MySQL Workbench：</p>
            <ol>
                <li>連接到資料庫：<code><?= DB_HOST ?></code></li>
                <li>展開 Schemas → <code><?= DB_NAME ?></code> → Tables</li>
                <li>找到 <code>login_logs</code> 表</li>
                <li>右鍵選擇 "Select Rows" 查看記錄</li>
            </ol>
            
            <h4>3. 命令行 MySQL</h4>
            <div class="code">
mysql -u <?= DB_USER ?> -p<br>
USE <?= DB_NAME ?>;<br>
SELECT * FROM login_logs ORDER BY created_at DESC LIMIT 10;
            </div>
        </div>

        <div class="info-box warning">
            <h3>⚠️ 注意事項</h3>
            <ul>
                <li>確保您查看的是正確的資料庫：<strong><?= DB_NAME ?></strong></li>
                <li>表名是：<strong>login_logs</strong> (注意是複數形式)</li>
                <li>如果看不到表，請檢查資料庫連接配置</li>
                <li>如果需要重新創建表，請執行：<code>setup_login_logs_table.php</code></li>
            </ul>
        </div>

        <div class="info-box">
            <h3>🛠️ 管理工具</h3>
            <p>
                <a href="view_login_logs.php" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">📋 查看登入記錄</a>
                <a href="test_database_login_logs.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-left: 10px;">🧪 測試登入功能</a>
            </p>
        </div>
    </div>
</body>
</html>
