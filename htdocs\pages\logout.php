<?php
/**
 * SignAttend PHP Frontend - 登出頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入配置檔案
require_once __DIR__ . '/../config/config.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 執行登出
$auth->logout();

// 設定成功訊息
show_success('您已成功登出');

// 重定向到首頁
header('Location: ' . page_url('index.php'));
exit;
?>
