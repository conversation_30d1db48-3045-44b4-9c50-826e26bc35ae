<?php
// 測試登入日誌記錄
define('SIGNATTEND_INIT', true);

echo "=== 登入日誌記錄測試 ===\n";

// 載入配置和函數
require_once 'config/environment.php';
require_once 'includes/functions.php';

echo "LOG_PATH: " . LOG_PATH . "\n\n";

// 檢查現有的認證日誌文件
echo "=== 檢查現有認證日誌 ===\n";
$authLogPattern = LOG_PATH . '/auth_*.log';
$authLogFiles = glob($authLogPattern);

if (empty($authLogFiles)) {
    echo "沒有找到認證日誌文件\n";
} else {
    echo "找到 " . count($authLogFiles) . " 個認證日誌文件:\n";
    foreach ($authLogFiles as $file) {
        $filename = basename($file);
        $size = filesize($file);
        $mtime = date('Y-m-d H:i:s', filemtime($file));
        echo "  📄 " . $filename . " (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
    }
}

// 檢查錯誤日誌文件
echo "\n=== 檢查錯誤日誌 ===\n";
$errorLogPattern = LOG_PATH . '/error_*.log';
$errorLogFiles = glob($errorLogPattern);

if (empty($errorLogFiles)) {
    echo "沒有找到錯誤日誌文件\n";
} else {
    echo "找到 " . count($errorLogFiles) . " 個錯誤日誌文件:\n";
    foreach ($errorLogFiles as $file) {
        $filename = basename($file);
        $size = filesize($file);
        $mtime = date('Y-m-d H:i:s', filemtime($file));
        echo "  📄 " . $filename . " (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
    }
}

// 模擬登入失敗的日誌記錄
echo "\n=== 模擬登入失敗日誌記錄 ===\n";

// 模擬認證失敗日誌
$failedLoginData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_id' => null,
    'user_email' => '<EMAIL>',
    'action' => 'login_failed',
    'description' => '登入失敗：密碼錯誤',
    'ip_address' => get_client_ip(),
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Test Agent'
];

$authLogMessage = json_encode($failedLoginData, JSON_UNESCAPED_UNICODE) . "\n";
$authLogFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';

if (file_put_contents($authLogFile, $authLogMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 認證失敗日誌寫入成功: " . $authLogFile . "\n";
} else {
    echo "❌ 認證失敗日誌寫入失敗\n";
}

// 模擬錯誤日誌
$errorMessage = sprintf(
    "[%s] Login Error: Invalid password for user %s from IP %s\n",
    date('Y-m-d H:i:s'),
    '<EMAIL>',
    get_client_ip()
);

$errorLogFile = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
if (file_put_contents($errorLogFile, $errorMessage, FILE_APPEND | LOCK_EX)) {
    echo "✅ 錯誤日誌寫入成功: " . $errorLogFile . "\n";
} else {
    echo "❌ 錯誤日誌寫入失敗\n";
}

// 檢查最新的日誌內容
echo "\n=== 檢查最新日誌內容 ===\n";

// 檢查今天的認證日誌
$todayAuthLog = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';
if (file_exists($todayAuthLog)) {
    echo "今天的認證日誌內容 (最後 5 行):\n";
    $lines = file($todayAuthLog, FILE_IGNORE_NEW_LINES);
    $lastLines = array_slice($lines, -5);
    foreach ($lastLines as $line) {
        $data = json_decode($line, true);
        if ($data) {
            echo "  [" . $data['timestamp'] . "] " . $data['action'] . " - " . $data['description'] . " (IP: " . $data['ip_address'] . ")\n";
        } else {
            echo "  " . $line . "\n";
        }
    }
} else {
    echo "今天沒有認證日誌\n";
}

// 檢查今天的錯誤日誌
echo "\n";
$todayErrorLog = LOG_PATH . '/error_' . date('Y-m-d') . '.log';
if (file_exists($todayErrorLog)) {
    echo "今天的錯誤日誌內容 (最後 3 行):\n";
    $lines = file($todayErrorLog, FILE_IGNORE_NEW_LINES);
    $lastLines = array_slice($lines, -3);
    foreach ($lastLines as $line) {
        echo "  " . $line . "\n";
    }
} else {
    echo "今天沒有錯誤日誌\n";
}

echo "\n=== 總結 ===\n";
echo "登入密碼錯誤時，日誌會記錄在以下位置：\n";
echo "1. 📁 認證日誌: " . LOG_PATH . "/auth_YYYY-MM-DD.log\n";
echo "2. 📁 錯誤日誌: " . LOG_PATH . "/error_YYYY-MM-DD.log\n";
echo "3. 📁 API 日誌: " . LOG_PATH . "/api_YYYY-MM-DD.log (如果通過 API)\n";

echo "\n測試完成！\n";
?>
