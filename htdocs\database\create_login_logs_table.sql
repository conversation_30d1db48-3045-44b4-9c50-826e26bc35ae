-- 創建登入記錄資料表
-- 執行此 SQL 來添加登入記錄功能

USE signattend_prod;

-- 創建登入記錄表
CREATE TABLE IF NOT EXISTS login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(36) NULL,
    user_email VARCHAR(255) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    success BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_login_logs_user_id (user_id),
    INDEX idx_login_logs_user_email (user_email),
    INDEX idx_login_logs_action (action),
    INDEX idx_login_logs_success (success),
    INDEX idx_login_logs_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加一些範例數據（可選）
-- INSERT INTO login_logs (user_email, action, description, ip_address, success) 
-- VALUES ('<EMAIL>', 'login_failed', '測試登入失敗記錄', '127.0.0.1', FALSE);

-- 檢查表格是否創建成功
SHOW TABLES LIKE 'login_logs';
DESCRIBE login_logs;
