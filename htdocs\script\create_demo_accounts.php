<?php
/**
 * 創建演示帳號
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

echo "=== 創建演示帳號 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 演示帳號列表
    $demoAccounts = [
        [
            'email' => '<EMAIL>',
            'password' => 'demo123',
            'description' => '演示帳號'
        ],
        [
            'email' => '<EMAIL>', 
            'password' => 'test123',
            'description' => '測試帳號'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'guest123', 
            'description' => '訪客帳號'
        ]
    ];
    
    foreach ($demoAccounts as $account) {
        echo "=== 處理 " . $account['description'] . " ===\n";
        echo "郵箱: " . $account['email'] . "\n";
        echo "密碼: " . $account['password'] . "\n";
        
        // 檢查帳號是否已存在
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$account['email']]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            echo "⚠️  帳號已存在，更新密碼...\n";
            
            // 更新密碼
            $passwordHash = password_hash($account['password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE email = ?");
            $result = $stmt->execute([$passwordHash, $account['email']]);
            
            if ($result) {
                echo "✅ 密碼更新成功\n";
            } else {
                echo "❌ 密碼更新失敗\n";
            }
        } else {
            echo "📝 創建新帳號...\n";
            
            // 創建新帳號
            $userId = bin2hex(random_bytes(18)); // 生成 36 字符的 UUID
            $passwordHash = password_hash($account['password'], PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (id, email, password_hash, created_at, updated_at) 
                VALUES (?, ?, ?, NOW(), NOW())
            ");
            
            $result = $stmt->execute([$userId, $account['email'], $passwordHash]);
            
            if ($result) {
                echo "✅ 帳號創建成功\n";
            } else {
                echo "❌ 帳號創建失敗\n";
            }
        }
        
        echo "\n";
    }
    
    echo "=== 驗證創建的帳號 ===\n";
    
    // 載入 Auth 類別進行驗證
    require_once UTILS_PATH . '/Auth.php';
    $auth = new Auth();
    
    foreach ($demoAccounts as $account) {
        echo "測試登入: " . $account['email'] . "\n";
        
        $result = $auth->login($account['email'], $account['password'], false);
        
        if ($result['success']) {
            echo "✅ 登入成功\n";
            $auth->logout(); // 登出以便測試下一個
        } else {
            echo "❌ 登入失敗: " . ($result['message'] ?? '未知錯誤') . "\n";
        }
        
        echo "\n";
    }
    
    echo "=== 演示帳號信息 ===\n";
    echo "以下帳號可用於登入頁面演示:\n\n";
    
    foreach ($demoAccounts as $account) {
        echo "🎯 " . $account['description'] . "\n";
        echo "   郵箱: " . $account['email'] . "\n";
        echo "   密碼: " . $account['password'] . "\n\n";
    }
    
    echo "這些帳號已添加到登入頁面的演示按鈕中。\n";
    echo "用戶可以點擊演示按鈕快速填入登入信息。\n";
    
} catch (PDOException $e) {
    echo "❌ 資料庫錯誤: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 一般錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 創建完成 ===\n";
?>
