<?php
define('SIGNATTEND_INIT', true);

echo "開始測試...\n";

// 測試直接載入 config.php
echo "=== 直接載入 config.php ===\n";
require_once 'config/config.php';
echo "ROOT_PATH: " . ROOT_PATH . "\n";
echo "LOG_PATH: " . LOG_PATH . "\n";

// 檢查是否指向正確的外層目錄
$expectedPath = dirname(dirname(__FILE__)) . '/logs';
echo "預期路徑: " . $expectedPath . "\n";
echo "路徑是否正確: " . (LOG_PATH === $expectedPath ? '是' : '否') . "\n";
?>
