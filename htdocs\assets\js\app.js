/**
 * SignAttend 主要 JavaScript 邏輯
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// 全域變數
window.SignAttend = {
    version: '2.0.1',
    debug: false, // 正式環境設為 false
    apiBaseUrl: '/backend/api', // 指向後端 API
    
    // 工具函數
    utils: {},
    
    // 組件
    components: {},
    
    // 初始化
    init: function() {
        this.initBootstrap();
        this.initEventListeners();
        this.initTooltips();
        this.initFormValidation();
        this.checkApiStatus();
        
        if (this.debug) {
            console.log('SignAttend v' + this.version + ' initialized');
        }
    }
};

// 工具函數
SignAttend.utils = {
    /**
     * 顯示載入狀態
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.add('loading');
            const btn = element.querySelector('button[type="submit"]');
            if (btn) {
                btn.disabled = true;
                const originalText = btn.innerHTML;
                btn.setAttribute('data-original-text', originalText);
                btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>處理中...';
            }
        }
    },
    
    /**
     * 隱藏載入狀態
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.remove('loading');
            const btn = element.querySelector('button[type="submit"]');
            if (btn) {
                btn.disabled = false;
                const originalText = btn.getAttribute('data-original-text');
                if (originalText) {
                    btn.innerHTML = originalText;
                }
            }
        }
    },
    
    /**
     * 顯示通知訊息
     */
    showNotification: function(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 自動移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    },
    
    /**
     * 確認對話框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    /**
     * AJAX 請求
     */
    ajax: function(options) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = Object.assign({}, defaults, options);
        
        // 添加 CSRF Token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            config.headers['X-CSRF-Token'] = csrfToken.getAttribute('content');
        }
        
        return fetch(config.url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
    },
    
    /**
     * 格式化日期時間
     */
    formatDateTime: function(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 複製到剪貼簿
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification('已複製到剪貼簿', 'success', 2000);
            });
        } else {
            // 備用方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('已複製到剪貼簿', 'success', 2000);
        }
    }
};

// 初始化 Bootstrap 組件
SignAttend.initBootstrap = function() {
    // 初始化所有 tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化所有 popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
};

// 初始化事件監聽器
SignAttend.initEventListeners = function() {
    // 表單提交處理
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            SignAttend.handleAjaxForm(form);
        }
    });
    
    // 確認刪除
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('confirm-delete')) {
            e.preventDefault();
            const message = e.target.getAttribute('data-message') || '確定要刪除嗎？';
            SignAttend.utils.confirm(message, function() {
                window.location.href = e.target.href;
            });
        }
    });
    
    // 複製按鈕
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('copy-btn')) {
            e.preventDefault();
            const text = e.target.getAttribute('data-copy-text');
            if (text) {
                SignAttend.utils.copyToClipboard(text);
            }
        }
    });
    
    // 檔案上傳拖放
    const fileUploadAreas = document.querySelectorAll('.file-upload-area');
    fileUploadAreas.forEach(area => {
        area.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        area.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = this.querySelector('input[type="file"]');
                if (fileInput) {
                    fileInput.files = files;
                    // 觸發 change 事件
                    const event = new Event('change', { bubbles: true });
                    fileInput.dispatchEvent(event);
                }
            }
        });
    });
};

// 初始化工具提示
SignAttend.initTooltips = function() {
    // 為所有帶有 title 屬性的元素添加 tooltip
    const elementsWithTitle = document.querySelectorAll('[title]:not([data-bs-toggle])');
    elementsWithTitle.forEach(element => {
        element.setAttribute('data-bs-toggle', 'tooltip');
        new bootstrap.Tooltip(element);
    });
};

// 初始化表單驗證
SignAttend.initFormValidation = function() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
};

// 處理 AJAX 表單
SignAttend.handleAjaxForm = function(form) {
    const formData = new FormData(form);
    const url = form.action || window.location.href;
    const method = form.method || 'POST';
    
    SignAttend.utils.showLoading(form);
    
    const options = {
        method: method,
        body: formData
    };
    
    // 如果是 JSON 表單，轉換為 JSON
    if (form.classList.contains('json-form')) {
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value;
        });
        options.body = JSON.stringify(data);
        options.headers = {
            'Content-Type': 'application/json'
        };
    }
    
    fetch(url, options)
        .then(response => response.json())
        .then(data => {
            SignAttend.utils.hideLoading(form);
            
            if (data.success) {
                SignAttend.utils.showNotification(data.message || '操作成功', 'success');
                
                // 如果有重導向 URL
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
                
                // 如果需要重新載入頁面
                if (data.reload) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                SignAttend.utils.showNotification(data.message || '操作失敗', 'danger');
            }
        })
        .catch(error => {
            SignAttend.utils.hideLoading(form);
            SignAttend.utils.showNotification('發生錯誤：' + error.message, 'danger');
            console.error('Form submission error:', error);
        });
};

// 檢查 API 狀態
SignAttend.checkApiStatus = function() {
    SignAttend.utils.ajax({
        url: SignAttend.apiBaseUrl + '/test'
    })
    .then(data => {
        if (SignAttend.debug) {
            console.log('API Status:', data);
        }
        // API 連接成功，可以在這裡添加成功處理邏輯
    })
    .catch(error => {
        if (SignAttend.debug) {
            console.warn('API connection failed:', error);
        }
        // 正式環境不顯示 API 連線異常通知，因為這是正常的
        // SignAttend.utils.showNotification('API 連線異常', 'warning');
    });
};

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    SignAttend.init();
});

// 匯出到全域
window.SignAttend = SignAttend;
