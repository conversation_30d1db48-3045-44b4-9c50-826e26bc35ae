<?php
/**
 * SignAttend 簡單測試頁面 - 無重定向
 */

// 基本 PHP 資訊
$phpVersion = phpversion();
$currentTime = date('Y-m-d H:i:s');
$serverInfo = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$host = $_SERVER['HTTP_HOST'] ?? 'Unknown';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SignAttend 簡單測試</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f8ff; 
            line-height: 1.6;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
        }
        h1 { color: #2c3e50; text-align: center; }
        .info-box { 
            background: #e8f5e8; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid #27ae60;
        }
        .status { 
            display: inline-block; 
            padding: 5px 10px; 
            border-radius: 3px; 
            color: white; 
            background: #27ae60; 
            font-weight: bold;
        }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #3498db; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px;
        }
        .btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 SignAttend 測試成功！</h1>
        
        <div class="info-box">
            <h3>基本資訊</h3>
            <p><strong>狀態:</strong> <span class="status">正常運行</span></p>
            <p><strong>測試時間:</strong> <?= $currentTime ?></p>
            <p><strong>PHP 版本:</strong> <?= $phpVersion ?></p>
            <p><strong>伺服器:</strong> <?= $serverInfo ?></p>
            <p><strong>域名:</strong> <?= $host ?></p>
        </div>

        <div class="info-box">
            <h3>連接測試</h3>
            <p>✅ PHP 正常運行</p>
            <p>✅ 網頁可以正常訪問</p>
            <p>✅ 無重定向循環問題</p>
        </div>

        <div class="info-box">
            <h3>下一步測試</h3>
            <p>
                <a href="test_basic.php" class="btn">基本功能測試</a>
                <a href="deployment_check.php" class="btn">完整部署檢查</a>
                <a href="index.php" class="btn">前往首頁</a>
            </p>
        </div>

        <div class="info-box">
            <h3>伺服器環境</h3>
            <p><strong>文檔根目錄:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?? 'N/A' ?></p>
            <p><strong>腳本路徑:</strong> <?= __FILE__ ?></p>
            <p><strong>HTTPS 狀態:</strong> 
                <?php 
                if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                    echo '<span style="color: green;">✓ 啟用</span>';
                } elseif (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
                    echo '<span style="color: green;">✓ 啟用 (代理)</span>';
                } else {
                    echo '<span style="color: orange;">△ 未檢測到</span>';
                }
                ?>
            </p>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>SignAttend v2.0.0 - 部署測試頁面</p>
        </div>
    </div>
</body>
</html>
