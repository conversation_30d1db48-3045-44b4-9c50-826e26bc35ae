<?php
/**
 * SignAttend PHP Frontend - 儀表板頁面
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 要求使用者登入
$auth->requireAuth();

// 取得使用者資訊
$user = $auth->getCurrentUser();
$profile = $auth->getCurrentProfile();

// 從 API 獲取會議資料
$meetings = [];
$currentMeeting = null;
$stats = [
    'total_meetings' => 0,
    'total_attendees' => 4,
    'checked_in' => 0,
    'qr_codes' => 4
];

try {
    // 獲取用戶的會議列表
    $url = 'http://localhost/SignAttend/backend/simple_meeting_api.php?created_by=' . urlencode($user['id']);
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        $responseData = json_decode($response, true);
        if ($responseData && $responseData['status'] === 'success') {
            $meetings = $responseData['data'] ?? [];
            $stats['total_meetings'] = count($meetings);

            // 設定當前會議
            if (!empty($meetings)) {
                // 檢查是否有指定的會議 ID
                $selectedMeetingId = $_GET['meeting_id'] ?? null;
                if ($selectedMeetingId) {
                    // 尋找指定的會議
                    foreach ($meetings as $meeting) {
                        if ($meeting['id'] === $selectedMeetingId) {
                            $currentMeeting = $meeting;
                            break;
                        }
                    }
                }

                // 如果沒有找到指定會議或沒有指定，使用第一個會議
                if (!$currentMeeting) {
                    $currentMeeting = $meetings[0];
                }
            }
        }
    }
} catch (Exception $e) {
    debug_log('Dashboard API error: ' . $e->getMessage());
}

// 如果沒有會議，使用預設資料
if (empty($currentMeeting)) {
    $currentMeeting = [
        'id' => '',
        'name' => '尚未建立會議',
        'location' => '',
        'start_date' => ''
    ];
}

// 獲取當前會議的參與者資料
$attendees = [];
if ($currentMeeting && !empty($currentMeeting['id'])) {
    try {
        // 從 API 獲取真實的參與者資料
        $url = 'http://attendance.app.tn/backend/simple_attendee_api.php?meeting_id=' . urlencode($currentMeeting['id']);
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            $responseData = json_decode($response, true);
            if ($responseData && $responseData['status'] === 'success') {
                $attendees = $responseData['data'] ?? [];

                // 格式化日期顯示
                foreach ($attendees as &$attendee) {
                    if ($attendee['invitation_sent'] && $attendee['invitation_date']) {
                        $attendee['invitation_date'] = date('Y/m/d 上午H:i', strtotime($attendee['invitation_date']));
                    }
                    if ($attendee['checked_in'] && $attendee['check_in_time']) {
                        $attendee['check_in_time'] = date('Y/m/d 上午H:i', strtotime($attendee['check_in_time']));
                    }
                }
            }
        }

        // 真實資料已載入，無需模擬資料

    } catch (Exception $e) {
        debug_log('Attendee API error: ' . $e->getMessage());
    }

    // 更新統計資料
    $stats['total_attendees'] = count($attendees);
    $stats['checked_in'] = count(array_filter($attendees, function($a) { return $a['checked_in']; }));
    $stats['qr_codes'] = $stats['total_attendees'];
}

// 頁面標題
$pageTitle = '儀表板';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data: blob:; font-src 'self' data:;">
    <title><?= $pageTitle ?> - 簽易通</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gray-50">

<!-- Header -->
<header class="border-b bg-white shadow-sm">
    <div class="container mx-auto px-4 py-3 flex justify-between items-center max-w-7xl">
        <h1 class="text-xl font-semibold">簽易通</h1>
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2 text-sm text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span>歡迎，<?= htmlspecialchars($user['email'] ?? '<EMAIL>') ?></span>
            </div>
            <a href="<?= page_url('pages/logout.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16 17 21 12 16 7"></polyline>
                    <line x1="21" x2="9" y1="12" y2="12"></line>
                </svg>
                登出
            </a>
        </div>
    </div>
</header>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Title -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight">會議報到管理系統</h1>
        <p class="text-gray-600 mt-1">管理參與者、生成QR碼並追蹤報到狀況</p>
    </div>

    <!-- Meeting Selection -->
    <div class="flex items-center gap-4 mb-6 p-4 bg-white rounded-lg border">
        <div class="flex-1 relative">
            <label class="text-sm font-medium mb-2 block">選擇會議</label>
            <div class="relative">
                <button type="button" id="meeting-dropdown-button" class="flex h-10 items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full">
                    <span id="selected-meeting-name"><?= htmlspecialchars($currentMeeting['name']) ?></span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50 transition-transform" id="dropdown-arrow">
                        <path d="m6 9 6 6 6-6"></path>
                    </svg>
                </button>

                <!-- Dropdown Menu -->
                <div id="meeting-dropdown-menu" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden">
                    <div class="py-1">
                        <?php if (empty($meetings)): ?>
                            <div class="px-3 py-2 text-sm text-gray-500 text-center">
                                尚未建立任何會議
                            </div>
                        <?php else: ?>
                            <?php foreach ($meetings as $meeting): ?>
                                <button type="button" class="meeting-option w-full text-left px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                                        data-meeting-id="<?= htmlspecialchars($meeting['id']) ?>"
                                        data-meeting-name="<?= htmlspecialchars($meeting['name']) ?>">
                                    <div class="font-medium"><?= htmlspecialchars($meeting['name']) ?></div>
                                    <div class="text-xs text-gray-500">
                                        <?= htmlspecialchars($meeting['location'] ?? '未指定地點') ?> •
                                        <?= htmlspecialchars($meeting['start_date'] ? date('Y-m-d', strtotime($meeting['start_date'])) : '未指定日期') ?>
                                    </div>
                                </button>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex gap-2">
            <a href="<?= page_url('pages/meetings/index.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6"></path>
                </svg>
                會議管理
            </a>
            <a href="<?= page_url('pages/meetings/create.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg>
                新增會議
            </a>
            <?php if ($currentMeeting && !empty($currentMeeting['id'])): ?>
            <a href="<?= page_url('pages/meetings/settings.php?id=' . urlencode($currentMeeting['id'])) ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3 no-underline">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                </svg>
                會議設定
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- 總參與者 -->
        <div class="rounded-lg border bg-white text-gray-900 shadow-sm">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">總參與者</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-600">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold"><?= $stats['total_attendees'] ?></div>
                <p class="text-xs text-gray-600">已註冊參加此會議</p>
            </div>
        </div>

        <!-- 已報到 -->
        <div class="rounded-lg border bg-white text-gray-900 shadow-sm">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">已報到</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-600">
                    <path d="M20 6 9 17l-5-5"></path>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold"><?= $stats['checked_in'] ?></div>
                <p class="text-xs text-gray-600"><?= $stats['total_attendees'] > 0 ? round(($stats['checked_in'] / $stats['total_attendees']) * 100) : 0 ?>% 的總參與者</p>
            </div>
        </div>

        <!-- QR 碼 -->
        <div class="rounded-lg border bg-white text-gray-900 shadow-sm">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">QR 碼</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-600">
                    <rect width="5" height="5" x="3" y="3" rx="1"></rect>
                    <rect width="5" height="5" x="16" y="3" rx="1"></rect>
                    <rect width="5" height="5" x="3" y="16" rx="1"></rect>
                    <path d="M21 16h-3a2 2 0 0 0-2 2v3"></path>
                    <path d="M21 21v.01"></path>
                    <path d="M12 7v3a2 2 0 0 1-2 2H7"></path>
                    <path d="M3 12h.01"></path>
                    <path d="M12 3h.01"></path>
                    <path d="M12 16v.01"></path>
                    <path d="M16 12h1"></path>
                    <path d="M21 12v.01"></path>
                    <path d="M12 21v-1"></path>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold"><?= $stats['qr_codes'] ?></div>
                <p class="text-xs text-gray-600">已為所有參與者生成</p>
            </div>
        </div>

        <!-- 會議資訊 -->
        <div class="rounded-lg border bg-white text-gray-900 shadow-sm">
            <div class="p-6 flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 class="tracking-tight text-sm font-medium">會議資訊</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-600">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M10 9H8"></path>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                </svg>
            </div>
            <div class="p-6 pt-0">
                <div class="text-lg font-bold truncate"><?= htmlspecialchars($currentMeeting['name']) ?></div>
                <p class="text-xs text-gray-600"><?= htmlspecialchars($currentMeeting['location']) ?></p>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-8">
        <div class="inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-600 mb-6">
            <button type="button" id="tab-attendees" class="tab-button inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-white text-gray-900 shadow-sm" data-tab="attendees">
                參與者
            </button>
            <button type="button" id="tab-qrcodes" class="tab-button inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" data-tab="qrcodes">
                QR 碼
            </button>
            <button type="button" id="tab-checkin" class="tab-button inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" data-tab="checkin">
                報到
            </button>
            <button type="button" id="tab-signin-form" class="tab-button inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" data-tab="signin-form">
                簽到表設計
            </button>
        </div>

        <!-- Attendees Tab Content -->
        <div id="tab-content-attendees" class="tab-content rounded-lg border bg-white text-gray-900 shadow-sm w-full">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="font-semibold tracking-tight text-xl flex justify-between items-center">
                    <span>參與者管理</span>
                    <span class="text-sm font-normal">
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-gray-900 font-normal">
                            總計 <?= $stats['total_attendees'] ?> 位
                        </div>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-100 text-green-800 font-normal">
                            已報到 <?= $stats['checked_in'] ?> 位
                        </div>
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-blue-100 text-blue-800 font-normal">
                            已發送邀請 <?= $stats['total_attendees'] ?> 位
                        </div>
                    </span>
                </h3>
                <?php if ($currentMeeting && !empty($currentMeeting['id'])): ?>
                    <p class="text-sm text-gray-600">
                        當前會議：<span class="font-medium text-blue-600"><?= htmlspecialchars($currentMeeting['name']) ?></span>
                        <?php if (!empty($currentMeeting['location'])): ?>
                            • <?= htmlspecialchars($currentMeeting['location']) ?>
                        <?php endif; ?>
                        <?php if (!empty($currentMeeting['start_date'])): ?>
                            • <?= date('Y-m-d H:i', strtotime($currentMeeting['start_date'])) ?>
                        <?php endif; ?>
                    </p>
                <?php else: ?>
                    <p class="text-sm text-gray-600">請先選擇一個會議來查看參與者資料</p>
                <?php endif; ?>
            </div>
            <div class="p-6 pt-0">
                <!-- Search and Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 mb-4">
                    <div class="relative flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-2.5 top-2.5 h-4 w-4 text-gray-600">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.3-4.3"></path>
                        </svg>
                        <input id="attendee-search" class="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-900 placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm pl-8" placeholder="搜尋參與者..." value="">
                    </div>
                    <div class="flex gap-2">
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 px-4 py-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                <path d="M10 9H8"></path>
                                <path d="M16 13H8"></path>
                                <path d="M16 17H8"></path>
                            </svg>
                            匯出簽名表
                        </button>
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 px-4 py-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <rect width="5" height="5" x="3" y="3" rx="1"></rect>
                                <rect width="5" height="5" x="16" y="3" rx="1"></rect>
                                <rect width="5" height="5" x="3" y="16" rx="1"></rect>
                                <path d="M21 16h-3a2 2 0 0 0-2 2v3"></path>
                                <path d="M21 21v.01"></path>
                                <path d="M12 7v3a2 2 0 0 1-2 2H7"></path>
                                <path d="M3 12h.01"></path>
                                <path d="M12 3h.01"></path>
                                <path d="M12 16v.01"></path>
                                <path d="M16 12h1"></path>
                                <path d="M21 12v.01"></path>
                                <path d="M12 21v-1"></path>
                            </svg>
                            下載會場報到QR Code
                        </button>
                        <button id="import-attendees-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 px-4 py-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" x2="12" y1="3" y2="15"></line>
                            </svg>
                            匯入
                        </button>
                        <button id="add-attendee-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 px-4 py-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <line x1="19" x2="19" y1="8" y2="14"></line>
                                <line x1="22" x2="16" y1="11" y2="11"></line>
                            </svg>
                            新增
                        </button>
                    </div>
                </div>

                <!-- Batch Actions -->
                <div id="batch-actions" class="hidden mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            <span id="selected-count" class="text-sm font-medium text-blue-800">已選擇 0 位參與者</span>
                        </div>
                        <div class="flex gap-2">
                            <button id="batch-send-invitation" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-8 rounded-md px-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                </svg>
                                批次發送邀請
                            </button>
                            <button id="batch-delete" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-red-600 text-white hover:bg-red-700 h-8 rounded-md px-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                </svg>
                                批次刪除
                            </button>
                            <button id="clear-selection" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-8 rounded-md px-3">
                                取消選擇
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Attendees Table -->
                <?php if (empty($currentMeeting) || empty($currentMeeting['id'])): ?>
                    <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">請選擇會議</h3>
                        <p class="mt-1 text-sm text-gray-500">請先從上方下拉選單選擇一個會議，然後查看該會議的參與者資料。</p>
                        <div class="mt-6">
                            <a href="<?= page_url('pages/meetings/create.php') ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 rounded-md px-6 no-underline">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M5 12h14"></path>
                                    <path d="M12 5v14"></path>
                                </svg>
                                建立第一個會議
                            </a>
                        </div>
                    </div>
                <?php elseif (empty($attendees)): ?>
                    <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">此會議尚無參與者</h3>
                        <p class="mt-1 text-sm text-gray-500">開始新增參與者來管理此會議的簽到。</p>
                        <div class="mt-6">
                            <button id="add-attendee-empty-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 rounded-md px-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <line x1="19" x2="19" y1="8" y2="14"></line>
                                    <line x1="22" x2="16" y1="11" y2="11"></line>
                                </svg>
                                新增參與者
                            </button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="rounded-md border">
                        <div class="relative w-full overflow-auto">
                            <table class="w-full caption-bottom text-sm">
                                <thead class="border-b">
                                    <tr class="border-b transition-colors hover:bg-gray-50">
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 w-12">
                                            <input type="checkbox" id="select-all-attendees" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600">姓名</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 hidden md:table-cell">電子郵件</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600 hidden lg:table-cell">組織</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600">報到狀態</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600">邀請函狀態</th>
                                        <th class="h-12 px-4 text-left align-middle font-medium text-gray-600">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="attendees-table-body">
                                    <?php foreach ($attendees as $attendee): ?>
                                    <tr class="attendee-row border-b transition-colors hover:bg-gray-50"
                                        data-name="<?= htmlspecialchars(strtolower($attendee['name'])) ?>"
                                        data-email="<?= htmlspecialchars(strtolower($attendee['email'])) ?>"
                                        data-organization="<?= htmlspecialchars(strtolower($attendee['organization'])) ?>"
                                        data-attendee-id="<?= htmlspecialchars($attendee['id']) ?>">
                                        <td class="p-4 align-middle">
                                            <input type="checkbox" class="attendee-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="<?= htmlspecialchars($attendee['id']) ?>">
                                        </td>
                                        <td class="p-4 align-middle"><?= htmlspecialchars($attendee['name']) ?></td>
                                        <td class="p-4 align-middle hidden md:table-cell"><?= htmlspecialchars($attendee['email']) ?></td>
                                        <td class="p-4 align-middle hidden lg:table-cell"><?= htmlspecialchars($attendee['organization']) ?></td>
                                        <td class="p-4 align-middle">
                                            <div class="flex items-center gap-2">
                                                <?php if ($attendee['checked_in']): ?>
                                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-100 text-green-800">
                                                        <span class="flex items-center gap-1">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3">
                                                                <path d="M20 6 9 17l-5-5"></path>
                                                            </svg>
                                                            已報到
                                                        </span>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-amber-100 text-amber-800">
                                                        <span class="flex items-center gap-1">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3">
                                                                <circle cx="12" cy="12" r="10"></circle>
                                                                <path d="m15 9-6 6"></path>
                                                                <path d="m9 9 6 6"></path>
                                                            </svg>
                                                            待報到
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="p-4 align-middle">
                                            <div class="flex flex-col items-center gap-1">
                                                <?php if ($attendee['invitation_sent']): ?>
                                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-blue-100 text-blue-800">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3 mr-1">
                                                            <path d="M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8"></path>
                                                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                                            <path d="m16 19 2 2 4-4"></path>
                                                        </svg>
                                                        已發送
                                                    </div>
                                                    <span class="text-xs text-gray-600"><?= htmlspecialchars($attendee['invitation_date']) ?></span>
                                                <?php else: ?>
                                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-gray-100 text-gray-800">
                                                        未發送
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="p-4 align-middle">
                                            <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-gray-100 hover:text-gray-900 h-9 rounded-md px-3" <?= $attendee['invitation_sent'] ? 'disabled' : '' ?>>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Check-in Tab Content -->
        <div id="tab-content-checkin" class="tab-content rounded-lg border bg-white text-gray-900 shadow-sm w-full hidden">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="font-semibold tracking-tight text-xl">參與者報到</h3>
                <p class="text-sm text-gray-600">掃描 QR 碼或輸入代碼完成報到</p>
            </div>
            <div class="p-6 pt-0">
                <div class="flex flex-col items-center max-w-md mx-auto space-y-6">
                    <!-- QR掃描區域 -->
                    <div class="w-full">
                        <button id="qr-scan-btn" class="w-full h-12 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 rounded-md">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
                                <circle cx="12" cy="13" r="3"></circle>
                            </svg>
                            <span id="scan-btn-text">掃描 QR 碼</span>
                        </button>

                        <!-- QR掃描器容器 -->
                        <div id="qr-scanner-container" class="hidden mt-4">
                            <div id="qr-reader" class="w-full border rounded-lg overflow-hidden"></div>
                            <div class="mt-2 flex gap-2">
                                <button id="stop-scan-btn" class="flex-1 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3">
                                    停止掃描
                                </button>
                                <button id="switch-camera-btn" class="flex-1 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3">
                                    切換攝像頭
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="relative w-full">
                        <div class="absolute inset-0 flex items-center">
                            <span class="w-full border-t"></span>
                        </div>
                        <div class="relative flex justify-center text-xs uppercase">
                            <span class="bg-white px-2 text-gray-500">或</span>
                        </div>
                    </div>

                    <div class="w-full space-y-2">
                        <label for="checkin-code" class="block text-sm font-medium text-gray-700">輸入報到代碼</label>
                        <input type="text" id="checkin-code" class="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-900 placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm" placeholder="請輸入6位數代碼">
                        <button id="manual-checkin-btn" class="w-full inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 6 9 17l-5-5"></path>
                            </svg>
                            確認報到
                        </button>
                    </div>
                </div>

                <!-- 報到操作說明 -->
                <div class="mt-8 max-w-md mx-auto">
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-4">
                            <h4 class="text-lg font-medium mb-2">報到操作說明</h4>
                            <p class="text-sm text-gray-600 mb-4">如何使用報到系統</p>
                            <div class="space-y-4">
                                <div class="flex items-start gap-2">
                                    <div class="bg-blue-600 text-white rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 text-sm font-medium">1</div>
                                    <div>
                                        <p class="font-medium">掃描參與者QR碼</p>
                                        <p class="text-sm text-gray-600">使用QR掃描器快速為參與者完成報到</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-2">
                                    <div class="bg-blue-600 text-white rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 text-sm font-medium">2</div>
                                    <div>
                                        <p class="font-medium">手動輸入代碼</p>
                                        <p class="text-sm text-gray-600">參與者可提供6位數報到代碼進行報到</p>
                                    </div>
                                </div>
                                <div class="flex items-start gap-2">
                                    <div class="bg-blue-600 text-white rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 text-sm font-medium">3</div>
                                    <div>
                                        <p class="font-medium">即時更新狀態</p>
                                        <p class="text-sm text-gray-600">報到成功後系統會即時更新參與者狀態</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QR Codes Tab Content -->
        <div id="tab-content-qrcodes" class="tab-content rounded-lg border bg-white text-gray-900 shadow-sm w-full hidden">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="font-semibold tracking-tight text-xl">QR 碼管理</h3>
                <p class="text-sm text-gray-600">為參與者生成和管理 QR 碼</p>
            </div>
            <div class="p-6 pt-0">
                <?php if (empty($attendees)): ?>
                    <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <rect width="5" height="5" x="3" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="16" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="3" y="16" rx="1"></rect>
                            <path d="M21 16h-3a2 2 0 0 0-2 2v3"></path>
                            <path d="M21 21v.01"></path>
                            <path d="M12 7v3a2 2 0 0 0 2 2h3"></path>
                            <path d="M3 12h.01"></path>
                            <path d="M12 3h.01"></path>
                            <path d="M12 16v.01"></path>
                            <path d="M16 12h1"></path>
                            <path d="M21 12v.01"></path>
                            <path d="M12 21v-1"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">尚無參與者</h3>
                        <p class="mt-1 text-sm text-gray-500">請先在「參與者」分頁新增資料以產生QR碼。</p>
                    </div>
                <?php else: ?>
                    <div class="flex flex-col items-center max-w-2xl mx-auto space-y-6">
                        <!-- 參與者選擇 -->
                        <div class="w-full max-w-md">
                            <label for="attendee-select" class="block text-sm font-medium text-gray-700 mb-2">選擇參與者</label>
                            <select id="attendee-select" class="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm">
                                <?php foreach ($attendees as $attendee): ?>
                                    <option value="<?= htmlspecialchars($attendee['id']) ?>"><?= htmlspecialchars($attendee['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- QR 碼顯示區域 -->
                        <div class="p-4 bg-white rounded-lg shadow-sm border">
                            <div id="qr-code-container" class="flex justify-center">
                                <!-- QR 碼將通過 JavaScript 動態生成 -->
                            </div>
                        </div>

                        <!-- 參與者資訊 -->
                        <div id="attendee-info" class="w-full max-w-md bg-gray-50 rounded-lg p-4">
                            <!-- 參與者資訊將通過 JavaScript 動態更新 -->
                        </div>

                        <!-- 操作按鈕 -->
                        <div class="flex gap-4">
                            <button id="download-qr-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 rounded-md px-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7 10 12 15 17 10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                下載 QR 碼
                            </button>
                            <button id="regenerate-qr-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md px-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                                    <path d="M21 3v5h-5"></path>
                                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                                    <path d="M3 21v-5h5"></path>
                                </svg>
                                重新生成
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sign-in Form Tab Content -->
        <div id="tab-content-signin-form" class="tab-content rounded-lg border bg-white text-gray-900 shadow-sm w-full hidden">
            <div class="flex flex-col space-y-1.5 p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="font-semibold tracking-tight text-xl">簽到表設計精靈</h3>
                        <p class="text-sm text-gray-600">設計和生成自訂簽到表格</p>
                    </div>
                    <div class="flex gap-2">
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-3">
                            儲存草稿
                        </button>
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-9 rounded-md px-3">
                            幫助
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6 pt-0">
                <div class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">簽到表設計功能開發中</h3>
                    <p class="mt-1 text-sm text-gray-500">此功能正在開發中，敬請期待。</p>
                    <div class="mt-6">
                        <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md px-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                            </svg>
                            匯出簽名表
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增參與者彈出視窗 -->
<div id="add-attendee-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">新增參與者</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <form id="add-attendee-form">
                <div class="mb-4">
                    <label for="attendee-name" class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                    <input type="text" id="attendee-name" name="name" required
                           placeholder="例如：王小明"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-4">
                    <label for="attendee-email" class="block text-sm font-medium text-gray-700 mb-2">電子郵件 *</label>
                    <input type="email" id="attendee-email" name="email" required
                           placeholder="例如：<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-4">
                    <label for="attendee-phone" class="block text-sm font-medium text-gray-700 mb-2">電話</label>
                    <input type="tel" id="attendee-phone" name="phone"
                           placeholder="例如：0912-345-678"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-4">
                    <label for="attendee-organization" class="block text-sm font-medium text-gray-700 mb-2">組織/公司</label>
                    <input type="text" id="attendee-organization" name="organization"
                           placeholder="例如：台灣科技股份有限公司"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="mb-4">
                    <label for="attendee-position" class="block text-sm font-medium text-gray-700 mb-2">職位</label>
                    <input type="text" id="attendee-position" name="position"
                           placeholder="例如：產品經理"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="flex justify-end gap-3">
                    <button type="button" id="cancel-add" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-9 rounded-md px-4">
                        取消
                    </button>
                    <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-9 rounded-md px-4">
                        新增參與者
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 匯入參與者彈出視窗 -->
<div id="import-attendees-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-6 border w-full max-w-lg shadow-lg rounded-lg bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">匯入參與者名單</h3>
                <button id="close-import-modal" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <!-- 說明文字 -->
            <div class="mb-6">
                <p class="text-sm text-gray-600">上傳 Excel 檔案來批量匯入參與者資料，請使用指定的範本格式。</p>
            </div>

            <!-- 下載範本區域 -->
            <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">下載範本</h4>
                <div class="border-2 border-red-200 rounded-lg p-4 bg-red-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h5 class="text-sm font-medium text-gray-900">參與者名單範本</h5>
                                <p class="text-xs text-gray-500">包含必要欄位格式的範本檔案</p>
                            </div>
                        </div>
                        <button id="download-template-btn" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-300 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" x2="12" y1="15" y2="3"></line>
                            </svg>
                            下載
                        </button>
                    </div>
                </div>
            </div>

            <!-- 上傳檔案區域 -->
            <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">上傳檔案</h4>
                <div id="file-upload-area" class="mt-1 flex justify-center px-6 pt-8 pb-8 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors bg-gray-50">
                    <div id="file-upload-content" class="space-y-3 text-center">
                        <svg id="file-upload-icon" class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="text-sm text-gray-600">
                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500 px-2 py-1">
                                <span id="file-upload-text">拖放檔案至此處，或點擊選擇檔案</span>
                                <input id="file-upload" name="file-upload" type="file" accept=".csv,.xlsx,.xls" class="sr-only">
                            </label>
                        </div>
                        <p id="file-upload-hint" class="text-xs text-gray-500">支援格式：.xlsx, .xls, .csv</p>
                    </div>
                </div>
                </div>

                <!-- 檔案預覽區域 -->
                <div id="file-preview" class="hidden mb-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">檔案預覽</h4>
                    <div class="border border-gray-200 rounded-md overflow-hidden">
                        <div class="bg-gray-50 px-4 py-2 border-b border-gray-200">
                            <span id="file-name" class="text-sm font-medium text-gray-900"></span>
                            <span id="file-size" class="text-sm text-gray-500 ml-2"></span>
                        </div>
                        <div class="max-h-64 overflow-auto">
                            <table id="preview-table" class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr id="preview-header"></tr>
                                </thead>
                                <tbody id="preview-body" class="bg-white divide-y divide-gray-200"></tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 欄位對應設定 -->
                <div id="field-mapping" class="hidden mb-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">欄位對應設定</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">姓名欄位 *</label>
                            <select id="name-field" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">請選擇欄位</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">電子郵件欄位 *</label>
                            <select id="email-field" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">請選擇欄位</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">電話欄位</label>
                            <select id="phone-field" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">請選擇欄位</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">組織/公司欄位</label>
                            <select id="organization-field" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">請選擇欄位</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注意事項 -->
            <div class="mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mt-0.5">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 16v-4"></path>
                            <path d="M12 8h.01"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">注意事項：</h5>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• 請使用指定的範本格式</li>
                            <li>• 電子郵件欄位為必填且不可重複</li>
                            <li>• 姓名欄位為必填</li>
                            <li>• 檔案大小限制為 5MB</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 手動輸入區域 -->
            <div id="manual-input-section" class="tab-content hidden">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">批次輸入參與者資料</label>
                    <p class="text-sm text-gray-500 mb-3">每行一位參與者，格式：姓名,電子郵件,電話,組織（電話和組織可選）</p>
                    <textarea id="manual-input-text" rows="10" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="範例：&#10;張小明,<EMAIL>,0912-345-678,ABC公司&#10;李小華,<EMAIL>,,XYZ組織&#10;王大同,<EMAIL>"></textarea>
                </div>

                <div class="mb-4">
                    <div class="flex items-center">
                        <input id="skip-duplicates" type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                        <label for="skip-duplicates" class="ml-2 text-sm text-gray-700">跳過重複的電子郵件</label>
                    </div>
                </div>
            </div>

            <!-- 匯入結果預覽 -->
            <div id="import-preview" class="hidden mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">匯入預覽</h4>
                <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mr-2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                        <span id="import-summary" class="text-sm text-blue-800">準備匯入 0 位參與者</span>
                    </div>
                </div>
                <div class="max-h-48 overflow-auto border border-gray-200 rounded-md">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">電子郵件</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">電話</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">組織</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">狀態</th>
                            </tr>
                        </thead>
                        <tbody id="import-preview-body" class="bg-white divide-y divide-gray-200"></tbody>
                    </table>
                </div>
            </div>

            <!-- 操作按鈕 -->
            <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button type="button" id="cancel-import" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900 h-10 rounded-md px-6">
                    取消
                </button>
                <button type="button" id="start-import" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-purple-600 text-white hover:bg-purple-700 h-10 rounded-md px-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" x2="12" y1="3" y2="15"></line>
                    </svg>
                    開始匯入
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.tab-button.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}
.tab-button:not(.active) {
    color: #6b7280;
    border-bottom: 2px solid transparent;
}
.tab-content.hidden {
    display: none;
}

/* 統計更新動畫 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes highlight {
    0% { background-color: transparent; }
    50% { background-color: rgba(34, 197, 94, 0.1); }
    100% { background-color: transparent; }
}

.stats-update {
    animation: highlight 0.6s ease-in-out;
}

.number-update {
    transition: all 0.3s ease-in-out;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== 頁籤切換功能 =====
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    // 初始化頁籤狀態
    function initializeTabs() {
        // 預設顯示參與者頁籤
        showTab('attendees');
    }

    // 顯示指定頁籤
    function showTab(tabName) {
        // 隱藏所有頁籤內容
        tabContents.forEach(content => {
            content.classList.add('hidden');
        });

        // 移除所有按鈕的活動狀態
        tabButtons.forEach(button => {
            button.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            button.classList.add('text-gray-600');
        });

        // 顯示選中的頁籤內容
        const targetContent = document.getElementById(`tab-content-${tabName}`);
        if (targetContent) {
            targetContent.classList.remove('hidden');
        }

        // 設置選中按鈕的活動狀態
        const targetButton = document.getElementById(`tab-${tabName}`);
        if (targetButton) {
            targetButton.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
            targetButton.classList.remove('text-gray-600');
        }

        // 如果切換到QR碼頁籤，初始化QR碼功能
        if (tabName === 'qrcodes') {
            initializeQRCodeTab();
        }
    }

    // 為每個頁籤按鈕添加點擊事件
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            if (tabName) {
                showTab(tabName);
            }
        });
    });

    // 初始化頁籤
    initializeTabs();

    // ===== 會議下拉選單功能 =====
    const dropdownButton = document.getElementById('meeting-dropdown-button');
    const dropdownMenu = document.getElementById('meeting-dropdown-menu');
    const dropdownArrow = document.getElementById('dropdown-arrow');
    const selectedMeetingName = document.getElementById('selected-meeting-name');
    const meetingOptions = document.querySelectorAll('.meeting-option');

    if (dropdownButton && dropdownMenu) {
        // 切換下拉選單顯示/隱藏
        dropdownButton.addEventListener('click', function() {
            const isHidden = dropdownMenu.classList.contains('hidden');

            if (isHidden) {
                dropdownMenu.classList.remove('hidden');
                dropdownArrow.style.transform = 'rotate(180deg)';
            } else {
                dropdownMenu.classList.add('hidden');
                dropdownArrow.style.transform = 'rotate(0deg)';
            }
        });

        // 處理會議選項點擊（使用事件委託處理動態生成的選項）
        dropdownMenu.addEventListener('click', function(e) {
            const option = e.target.closest('.meeting-option');
            if (option) {
                const meetingId = option.getAttribute('data-meeting-id');
                const meetingName = option.getAttribute('data-meeting-name');

                // 更新顯示的會議名稱
                selectedMeetingName.textContent = meetingName;

                // 隱藏下拉選單
                dropdownMenu.classList.add('hidden');
                dropdownArrow.style.transform = 'rotate(0deg)';

                // 這裡可以添加切換會議的邏輯
                console.log('選擇會議:', meetingId, meetingName);

                // 可以發送 AJAX 請求來更新頁面數據
                updateMeetingData(meetingId);
            }
        });

        // 點擊外部區域關閉下拉選單
        document.addEventListener('click', function(event) {
            if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.classList.add('hidden');
                dropdownArrow.style.transform = 'rotate(0deg)';
            }
        });

        // 鍵盤導航支援
        dropdownButton.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                dropdownButton.click();
            }
        });

        // 鍵盤導航支援（使用事件委託）
        dropdownMenu.addEventListener('keydown', function(event) {
            const option = event.target.closest('.meeting-option');
            if (option && (event.key === 'Enter' || event.key === ' ')) {
                event.preventDefault();
                option.click();
            }
        });
    }

    // ===== 參與者搜尋功能 =====
    const searchInput = document.getElementById('attendee-search');
    const attendeeRows = document.querySelectorAll('.attendee-row');

    if (searchInput && attendeeRows.length > 0) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            let visibleCount = 0;

            attendeeRows.forEach(function(row) {
                const name = row.getAttribute('data-name') || '';
                const email = row.getAttribute('data-email') || '';
                const organization = row.getAttribute('data-organization') || '';

                // 檢查搜尋詞是否匹配姓名、電子郵件或組織
                const isMatch = name.includes(searchTerm) ||
                               email.includes(searchTerm) ||
                               organization.includes(searchTerm);

                if (isMatch || searchTerm === '') {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // 顯示搜尋結果統計
            updateSearchResults(visibleCount, attendeeRows.length, searchTerm);
        });
    }

function updateSearchResults(visibleCount, totalCount, searchTerm) {
    // 移除之前的搜尋結果提示
    const existingResult = document.getElementById('search-result-info');
    if (existingResult) {
        existingResult.remove();
    }

    // 如果有搜尋詞且結果不等於總數，顯示搜尋結果
    if (searchTerm && visibleCount !== totalCount) {
        const tableContainer = document.querySelector('#attendees-table-body');
        if (tableContainer) {
            const table = tableContainer.closest('table');
            if (table) {
                const resultInfo = document.createElement('div');
                resultInfo.id = 'search-result-info';
                resultInfo.className = 'px-4 py-2 bg-blue-50 border-b text-sm text-blue-800';

                if (visibleCount === 0) {
                    resultInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                            找不到符合「${searchTerm}」的參與者
                        </div>
                    `;
                } else {
                    resultInfo.innerHTML = `
                        <div class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                            找到 ${visibleCount} 位符合「${searchTerm}」的參與者（共 ${totalCount} 位）
                        </div>
                    `;
                }

                table.parentNode.insertBefore(resultInfo, table);
            }
        }
    }
}

    // ===== 批次操作功能 =====
    const selectAllCheckbox = document.getElementById('select-all-attendees');
    const attendeeCheckboxes = document.querySelectorAll('.attendee-checkbox');
    const batchActions = document.getElementById('batch-actions');
    const selectedCount = document.getElementById('selected-count');
    const batchDeleteBtn = document.getElementById('batch-delete');
    const batchSendInvitationBtn = document.getElementById('batch-send-invitation');
    const clearSelectionBtn = document.getElementById('clear-selection');

    if (selectAllCheckbox && attendeeCheckboxes.length > 0) {
        // 全選/取消全選功能
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            attendeeCheckboxes.forEach(function(checkbox) {
                // 只選擇可見的參與者
                const row = checkbox.closest('.attendee-row');
                if (row && row.style.display !== 'none') {
                    checkbox.checked = isChecked;
                }
            });
            updateBatchActions();
        });

        // 個別勾選功能
        attendeeCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateSelectAllState();
                updateBatchActions();
            });
        });

        // 批次刪除功能
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const selectedIds = getSelectedAttendeeIds();
                if (selectedIds.length === 0) {
                    alert('請先選擇要刪除的參與者');
                    return;
                }

                const confirmMessage = `確定要刪除選中的 ${selectedIds.length} 位參與者嗎？此操作無法復原。`;
                if (confirm(confirmMessage)) {
                    // 發送 AJAX 請求到後端刪除參與者
                    batchDeleteBtn.disabled = true;
                    batchDeleteBtn.textContent = '刪除中...';

                    fetch('http://localhost/SignAttend/backend/simple_attendee_api.php', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            attendee_ids: selectedIds
                        })
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        return response.text().then(text => {
                            console.log('Response text:', text);
                            try {
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('JSON parse error:', e);
                                throw new Error('Invalid JSON response: ' + text);
                            }
                        });
                    })
                    .then(data => {
                        console.log('Parsed data:', data);
                        if (data.status === 'success') {
                            // 從前端移除已刪除的參與者
                            selectedIds.forEach(function(id) {
                                const row = document.querySelector(`[data-attendee-id="${id}"]`);
                                if (row) {
                                    row.remove();
                                }
                            });

                            // 更新統計
                            updateAttendeeStats();
                            updateBatchActions();

                            alert(data.message || `已成功刪除 ${selectedIds.length} 位參與者`);
                        } else {
                            alert('刪除失敗：' + (data.message || '未知錯誤'));
                        }
                    })
                    .catch(error => {
                        console.error('刪除請求失敗:', error);
                        alert('刪除失敗：' + error.message);
                    })
                    .finally(() => {
                        batchDeleteBtn.disabled = false;
                        batchDeleteBtn.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                            批次刪除
                        `;
                    });
                }
            });
        }

        // 批次發送邀請功能
        if (batchSendInvitationBtn) {
            batchSendInvitationBtn.addEventListener('click', function() {
                const selectedIds = getSelectedAttendeeIds();
                if (selectedIds.length === 0) {
                    alert('請先選擇要發送邀請的參與者');
                    return;
                }

                const confirmMessage = `確定要向選中的 ${selectedIds.length} 位參與者發送邀請函嗎？`;
                if (confirm(confirmMessage)) {
                    // 這裡應該發送 AJAX 請求到後端發送邀請
                    // 目前先模擬發送操作
                    alert(`已向 ${selectedIds.length} 位參與者發送邀請函`);

                    // 清除選擇
                    clearSelection();
                }
            });
        }

        // 清除選擇功能
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', function() {
                clearSelection();
            });
        }
    }

    function updateSelectAllState() {
        const visibleCheckboxes = Array.from(attendeeCheckboxes).filter(function(checkbox) {
            const row = checkbox.closest('.attendee-row');
            return row && row.style.display !== 'none';
        });

        const checkedCount = visibleCheckboxes.filter(function(checkbox) {
            return checkbox.checked;
        }).length;

        if (selectAllCheckbox) {
            if (checkedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCount === visibleCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    function updateBatchActions() {
        const selectedIds = getSelectedAttendeeIds();
        const count = selectedIds.length;

        if (count > 0) {
            batchActions.classList.remove('hidden');
            selectedCount.textContent = `已選擇 ${count} 位參與者`;
        } else {
            batchActions.classList.add('hidden');
        }
    }

    function getSelectedAttendeeIds() {
        return Array.from(attendeeCheckboxes)
            .filter(function(checkbox) {
                const row = checkbox.closest('.attendee-row');
                return checkbox.checked && row && row.style.display !== 'none';
            })
            .map(function(checkbox) {
                return checkbox.value;
            });
    }

    function clearSelection() {
        attendeeCheckboxes.forEach(function(checkbox) {
            checkbox.checked = false;
        });
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
        updateBatchActions();
    }

    // ===== 新增參與者彈出視窗功能 =====
    const addAttendeeBtn = document.getElementById('add-attendee-btn');
    const addAttendeeEmptyBtn = document.getElementById('add-attendee-empty-btn');
    const addAttendeeModal = document.getElementById('add-attendee-modal');
    const closeModalBtn = document.getElementById('close-modal');
    const cancelAddBtn = document.getElementById('cancel-add');
    const addAttendeeForm = document.getElementById('add-attendee-form');

    // 開啟彈出視窗的通用函數
    function openAddAttendeeModal() {
        // 檢查是否有選擇會議
        const currentMeetingId = getCurrentMeetingId();
        if (!currentMeetingId) {
            alert('請先選擇一個會議');
            return;
        }

        addAttendeeModal.classList.remove('hidden');
        // 清空表單
        addAttendeeForm.reset();
        // 聚焦到姓名欄位
        document.getElementById('attendee-name').focus();
    }

    // 工具列的新增按鈕
    if (addAttendeeBtn) {
        addAttendeeBtn.addEventListener('click', openAddAttendeeModal);
    }

    // 空狀態的新增按鈕
    if (addAttendeeEmptyBtn) {
        addAttendeeEmptyBtn.addEventListener('click', openAddAttendeeModal);
    }

    // ===== 匯入參與者功能 =====
    const importAttendeesBtn = document.getElementById('import-attendees-btn');
    const importAttendeesModal = document.getElementById('import-attendees-modal');
    const closeImportModalBtn = document.getElementById('close-import-modal');
    const cancelImportBtn = document.getElementById('cancel-import');
    const downloadTemplateBtn = document.getElementById('download-template-btn');
    const startImportBtn = document.getElementById('start-import');

    // 檔案上傳相關
    const fileUpload = document.getElementById('file-upload');

    // 開啟匯入彈出視窗
    function openImportModal() {
        const currentMeetingId = getCurrentMeetingId();
        if (!currentMeetingId) {
            alert('請先選擇一個會議');
            return;
        }

        importAttendeesModal.classList.remove('hidden');
        resetImportModal();
    }

    // 重置匯入彈出視窗
    function resetImportModal() {
        // 重置檔案上傳
        if (fileUpload) {
            fileUpload.value = '';
        }

        // 重置上傳區域UI
        resetFileUploadArea();

        // 清除儲存的匯入資料
        window.importData = null;
        window.attendeesToImport = null;
    }

    // 關閉匯入彈出視窗
    function closeImportModal() {
        importAttendeesModal.classList.add('hidden');
        resetImportModal();
    }

    // 事件監聽器
    if (importAttendeesBtn) {
        importAttendeesBtn.addEventListener('click', openImportModal);
    }

    if (closeImportModalBtn) {
        closeImportModalBtn.addEventListener('click', closeImportModal);
    }

    if (cancelImportBtn) {
        cancelImportBtn.addEventListener('click', closeImportModal);
    }

    // 下載範本
    if (downloadTemplateBtn) {
        downloadTemplateBtn.addEventListener('click', downloadTemplate);
    }

    // 開始匯入按鈕
    if (startImportBtn) {
        startImportBtn.addEventListener('click', function() {
            if (!window.importData) {
                alert('請先選擇檔案');
                return;
            }
            executeSimpleImport();
        });
    }

    // 檔案上傳處理
    if (fileUpload) {
        fileUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });
    }

    // 拖拽上傳處理
    const dropZone = document.querySelector('#import-attendees-modal .border-dashed');
    if (dropZone) {
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropZone.classList.add('border-blue-400', 'bg-blue-50');
        });

        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-400', 'bg-blue-50');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-400', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });
    }



    // 點擊背景關閉彈出視窗
    importAttendeesModal.addEventListener('click', function(e) {
        if (e.target === importAttendeesModal) {
            closeImportModal();
        }
    });

    // ESC 鍵關閉彈出視窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !importAttendeesModal.classList.contains('hidden')) {
            closeImportModal();
        }
    });

    // 關閉彈出視窗
    function closeModal() {
        addAttendeeModal.classList.add('hidden');
        addAttendeeForm.reset();
    }

    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeModal);
    }

    if (cancelAddBtn) {
        cancelAddBtn.addEventListener('click', closeModal);
    }

    // 點擊背景關閉彈出視窗
    addAttendeeModal.addEventListener('click', function(e) {
        if (e.target === addAttendeeModal) {
            closeModal();
        }
    });

    // ESC 鍵關閉彈出視窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !addAttendeeModal.classList.contains('hidden')) {
            closeModal();
        }
    });

    // 處理表單提交
    if (addAttendeeForm) {
        addAttendeeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const currentMeetingId = getCurrentMeetingId();
            if (!currentMeetingId) {
                alert('請先選擇一個會議');
                return;
            }

            const formData = new FormData(addAttendeeForm);
            const attendeeData = {
                name: formData.get('name').trim(),
                email: formData.get('email').trim(),
                phone: formData.get('phone').trim(),
                organization: formData.get('organization').trim(),
                position: formData.get('position').trim()
            };

            // 驗證必填欄位
            if (!attendeeData.name || !attendeeData.email) {
                alert('請填寫姓名和電子郵件');
                return;
            }

            // 驗證電子郵件格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(attendeeData.email)) {
                alert('請輸入有效的電子郵件格式');
                return;
            }

            // 發送請求到後端
            const submitBtn = addAttendeeForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = '新增中...';

            fetch(`http://localhost/SignAttend/backend/api/meetings/${currentMeetingId}/attendees`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(attendeeData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    alert('參與者新增成功！');
                    closeModal();
                    // 動態添加新參與者到表格，而不是重新載入頁面
                    addAttendeeToTable(data.data);
                    // 更新統計
                    updateAttendeeStats();
                } else {
                    alert('新增失敗：' + (data.message || '未知錯誤'));
                }
            })
            .catch(error => {
                console.error('新增參與者失敗:', error);
                alert('新增失敗：' + error.message);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        });
    }

    function updateAttendeeStats() {
        // 重新計算參與者統計
        const visibleRows = document.querySelectorAll('.attendee-row[style=""], .attendee-row:not([style])');
        const totalCount = visibleRows.length;

        // 更新統計卡片（這裡可以根據實際需求更新）
        console.log(`更新統計：總參與者 ${totalCount} 位`);
    }
});

// ===== 獨立函數 =====

// 更新會議數據的函數
function updateMeetingData(meetingId) {
    if (!meetingId) {
        console.log('沒有選擇會議');
        return;
    }

    console.log('更新會議數據:', meetingId);

    // 重新載入頁面並傳遞選中的會議 ID
    const url = new URL(window.location);
    url.searchParams.set('meeting_id', meetingId);
    window.location.href = url.toString();
}

// 獲取當前選擇的會議 ID
function getCurrentMeetingId() {
    // 從 URL 參數獲取
    const urlParams = new URLSearchParams(window.location.search);
    const meetingId = urlParams.get('meeting_id');

    if (meetingId) {
        return meetingId;
    }

    // 從 PHP 變數獲取（如果有的話）
    <?php if ($currentMeeting && !empty($currentMeeting['id'])): ?>
    return '<?= addslashes($currentMeeting['id']) ?>';
    <?php endif; ?>

    return null;
}

// 動態添加新參與者到表格
function addAttendeeToTable(attendee) {
    const tableBody = document.getElementById('attendees-table-body');
    if (!tableBody) {
        // 如果沒有表格（空狀態），重新載入頁面
        window.location.reload();
        return;
    }

    // 檢查是否已經存在相同的參與者（避免重複）
    const existingRow = document.querySelector(`[data-attendee-id="${attendee.id}"]`);
    if (existingRow) {
        console.log('參與者已存在，跳過添加');
        return;
    }

    // 創建新的表格行
    const newRow = document.createElement('tr');
    newRow.className = 'attendee-row border-b transition-colors hover:bg-gray-50';
    newRow.setAttribute('data-name', attendee.name.toLowerCase());
    newRow.setAttribute('data-email', attendee.email.toLowerCase());
    newRow.setAttribute('data-organization', (attendee.organization || '').toLowerCase());
    newRow.setAttribute('data-attendee-id', attendee.id);

    // 構建行內容
    newRow.innerHTML = `
        <td class="p-4 align-middle">
            <input type="checkbox" class="attendee-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="${attendee.id}">
        </td>
        <td class="p-4 align-middle">${escapeHtml(attendee.name)}</td>
        <td class="p-4 align-middle hidden md:table-cell">${escapeHtml(attendee.email)}</td>
        <td class="p-4 align-middle hidden lg:table-cell">${escapeHtml(attendee.organization || '')}</td>
        <td class="p-4 align-middle">
            <div class="flex items-center gap-2">
                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-amber-100 text-amber-800">
                    <span class="flex items-center gap-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="m15 9-6 6"></path>
                            <path d="m9 9 6 6"></path>
                        </svg>
                        待報到
                    </span>
                </div>
            </div>
        </td>
        <td class="p-4 align-middle">
            <div class="flex flex-col items-center gap-1">
                <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-gray-100 text-gray-800">
                    未發送
                </div>
            </div>
        </td>
        <td class="p-4 align-middle">
            <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-gray-100 hover:text-gray-900 h-9 rounded-md px-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                </svg>
            </button>
        </td>
    `;

    // 添加到表格
    tableBody.appendChild(newRow);

    // 為新的複選框添加事件監聽器
    const newCheckbox = newRow.querySelector('.attendee-checkbox');
    if (newCheckbox) {
        newCheckbox.addEventListener('change', function() {
            // 重新初始化批次操作功能
            initializeBatchOperations();
        });
    }

    console.log('新參與者已添加到表格:', attendee.name);
}

// HTML 轉義函數
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// ===== 匯入功能相關函數 =====

// 下載範本檔案
function downloadTemplate() {
    // 創建 CSV 範本內容（確保使用正確的中文編碼）
    const templateContent = [
        '姓名,電子郵件,電話,組織',
        '張小明,<EMAIL>,0912-345-678,ABC科技公司',
        '李小華,<EMAIL>,0923-456-789,XYZ設計工作室',
        '王大同,<EMAIL>,0934-567-890,DEF顧問公司'
    ].join('\r\n'); // 使用 Windows 換行符

    // 創建 Blob 物件，使用 UTF-8 BOM 確保 Excel 正確識別中文
    const blob = new Blob(['\uFEFF' + templateContent], {
        type: 'text/csv;charset=utf-8;'
    });

    // 創建下載連結
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '參與者名單範本.csv');
    link.style.visibility = 'hidden';

    // 觸發下載
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理 URL 物件
    URL.revokeObjectURL(url);

    // 顯示使用提示
    setTimeout(() => {
        alert('範本下載完成！\n\n使用提示：\n1. 請使用 Excel 或記事本開啟檔案\n2. 填寫參與者資料後儲存\n3. 確保檔案編碼為 UTF-8\n4. 如果出現亂碼，請嘗試用記事本開啟並另存為 UTF-8 格式');
    }, 100);
}

// 處理檔案上傳
function handleFileUpload(file) {
    console.log('處理檔案上傳:', file.name);

    // 根據檔案類型處理
    if (file.name.endsWith('.csv')) {
        parseCSVFile(file);
    } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        alert('Excel 檔案支援需要額外的程式庫，目前請使用 CSV 格式');
    } else {
        alert('不支援的檔案格式，請使用 CSV 或 Excel 檔案');
    }
}

// 解析 CSV 檔案
function parseCSVFile(file) {
    // 嘗試多種編碼方式來解決中文亂碼問題
    tryParseWithEncoding(file, 'UTF-8')
        .then(result => {
            if (result.success) {
                processCSVData(result.data, file.name);
            } else {
                // UTF-8 失敗，嘗試 Big5
                return tryParseWithEncoding(file, 'Big5');
            }
        })
        .then(result => {
            if (result && result.success) {
                processCSVData(result.data, file.name);
            } else {
                // Big5 也失敗，嘗試 GBK
                return tryParseWithEncoding(file, 'GBK');
            }
        })
        .then(result => {
            if (result && result.success) {
                processCSVData(result.data, file.name);
            } else {
                alert('無法正確解析檔案編碼，請確保檔案為 UTF-8 格式');
            }
        })
        .catch(error => {
            console.error('檔案解析錯誤:', error);
            alert('檔案解析失敗，請檢查檔案格式');
        });
}

// 嘗試使用指定編碼解析檔案
function tryParseWithEncoding(file, encoding) {
    return new Promise((resolve) => {
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                let csv = e.target.result;

                // 移除 BOM（Byte Order Mark）如果存在
                if (csv.charCodeAt(0) === 0xFEFF) {
                    csv = csv.slice(1);
                }

                const lines = csv.split('\n').filter(line => line.trim());

                if (lines.length === 0) {
                    resolve({ success: false, error: '檔案內容為空' });
                    return;
                }

                // 解析 CSV
                const data = lines.map(line => {
                    // 簡單的 CSV 解析（不處理引號內的逗號）
                    return line.split(',').map(cell => cell.trim());
                });

                // 檢查是否有中文亂碼（簡單檢測）
                const firstRow = data[0];
                const hasGarbledText = firstRow.some(cell =>
                    cell.includes('�') || // 常見的亂碼字符
                    /[\x00-\x08\x0E-\x1F\x7F]/.test(cell) // 控制字符
                );

                if (hasGarbledText && encoding === 'UTF-8') {
                    resolve({ success: false, error: '可能存在編碼問題' });
                    return;
                }

                resolve({
                    success: true,
                    data: data,
                    encoding: encoding
                });

            } catch (error) {
                resolve({ success: false, error: error.message });
            }
        };

        reader.onerror = function() {
            resolve({ success: false, error: '檔案讀取失敗' });
        };

        reader.readAsText(file, encoding);
    });
}

// 處理解析成功的 CSV 資料
function processCSVData(data, fileName) {
    // 假設第一行是標題
    const headers = data[0];
    const rows = data.slice(1);

    // 儲存資料供後續使用
    window.importData = { headers, rows };

    console.log('CSV 檔案解析完成，共', rows.length, '行資料');
    console.log('標題行:', headers);
    console.log('資料預覽:', rows.slice(0, 2));

    // 檢查是否有必要的欄位
    const hasName = headers.some(h => h.includes('姓名') || h.toLowerCase().includes('name'));
    const hasEmail = headers.some(h => h.includes('郵件') || h.includes('信箱') || h.toLowerCase().includes('email'));

    if (!hasName || !hasEmail) {
        alert('警告：檔案中可能缺少必要的欄位（姓名、電子郵件）。請確認檔案格式正確。');
    }

    // 更新上傳區域UI為成功狀態
    updateFileUploadSuccess(fileName, rows.length);

    alert(`檔案上傳成功！共 ${rows.length} 行資料，點擊「開始匯入」執行匯入。`);
}

// 更新檔案上傳區域為成功狀態
function updateFileUploadSuccess(fileName, rowCount) {
    const uploadArea = document.getElementById('file-upload-area');
    const uploadIcon = document.getElementById('file-upload-icon');
    const uploadText = document.getElementById('file-upload-text');
    const uploadHint = document.getElementById('file-upload-hint');

    if (uploadArea && uploadIcon && uploadText && uploadHint) {
        // 更新容器樣式
        uploadArea.className = 'mt-1 flex justify-center px-6 pt-8 pb-8 border-2 border-green-300 border-dashed rounded-lg bg-green-50 transition-colors';

        // 更新圖示為打勾
        uploadIcon.className = 'mx-auto h-12 w-12 text-green-500';
        uploadIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        `;
        uploadIcon.setAttribute('fill', 'none');
        uploadIcon.setAttribute('viewBox', '0 0 24 24');
        uploadIcon.setAttribute('stroke', 'currentColor');

        // 更新文字
        uploadText.textContent = `檔案上傳成功：${fileName}`;
        uploadText.className = 'font-medium text-green-600';

        // 更新提示文字
        uploadHint.textContent = `已解析 ${rowCount} 行資料，準備匯入`;
        uploadHint.className = 'text-xs text-green-600';

        // 添加成功動畫
        uploadArea.style.animation = 'pulse 0.5s ease-in-out';
        setTimeout(() => {
            uploadArea.style.animation = '';
        }, 500);
    }
}

// 重置檔案上傳區域UI
function resetFileUploadArea() {
    const uploadArea = document.getElementById('file-upload-area');
    const uploadIcon = document.getElementById('file-upload-icon');
    const uploadText = document.getElementById('file-upload-text');
    const uploadHint = document.getElementById('file-upload-hint');

    if (uploadArea && uploadIcon && uploadText && uploadHint) {
        // 重置容器樣式
        uploadArea.className = 'mt-1 flex justify-center px-6 pt-8 pb-8 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors bg-gray-50';

        // 重置圖示為上傳圖示
        uploadIcon.className = 'mx-auto h-12 w-12 text-gray-400';
        uploadIcon.innerHTML = `
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        `;
        uploadIcon.setAttribute('fill', 'none');
        uploadIcon.setAttribute('viewBox', '0 0 48 48');
        uploadIcon.setAttribute('stroke', 'currentColor');

        // 重置文字
        uploadText.textContent = '拖放檔案至此處，或點擊選擇檔案';
        uploadText.className = '';

        // 重置提示文字
        uploadHint.textContent = '支援格式：.xlsx, .xls, .csv';
        uploadHint.className = 'text-xs text-gray-500';

        // 清除動畫
        uploadArea.style.animation = '';
    }
}



// 重新初始化批次操作功能
function initializeBatchOperations() {
    const selectAllCheckbox = document.getElementById('select-all-attendees');
    const attendeeCheckboxes = document.querySelectorAll('.attendee-checkbox');
    const batchActions = document.getElementById('batch-actions');
    const selectedCount = document.getElementById('selected-count');

    if (!selectAllCheckbox || !batchActions || !selectedCount) {
        return;
    }

    // 更新全選狀態
    const visibleCheckboxes = Array.from(attendeeCheckboxes).filter(function(checkbox) {
        const row = checkbox.closest('.attendee-row');
        return row && row.style.display !== 'none';
    });

    const checkedCount = visibleCheckboxes.filter(function(checkbox) {
        return checkbox.checked;
    }).length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === visibleCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }

    // 更新批次操作顯示
    const selectedIds = Array.from(attendeeCheckboxes)
        .filter(function(checkbox) {
            const row = checkbox.closest('.attendee-row');
            return checkbox.checked && row && row.style.display !== 'none';
        })
        .map(function(checkbox) {
            return checkbox.value;
        });

    const count = selectedIds.length;

    if (count > 0) {
        batchActions.classList.remove('hidden');
        selectedCount.textContent = `已選擇 ${count} 位參與者`;
    } else {
        batchActions.classList.add('hidden');
    }
}



// 簡化的匯入執行函數
async function executeSimpleImport() {
    if (!window.importData) {
        alert('請先選擇檔案');
        return;
    }

    const currentMeetingId = getCurrentMeetingId();
    if (!currentMeetingId) {
        alert('請先選擇一個會議');
        return;
    }

    const startImportBtn = document.getElementById('start-import');
    const originalText = startImportBtn.textContent;
    startImportBtn.disabled = true;
    startImportBtn.textContent = '匯入中...';

    try {
        const { headers, rows } = window.importData;

        console.log('開始匯入，標題行:', headers);
        console.log('資料行數:', rows.length);

        // 智能識別欄位位置
        const nameIndex = findColumnIndex(headers, ['姓名', 'name', '名字']);
        const emailIndex = findColumnIndex(headers, ['電子郵件', 'email', '郵件', '信箱']);
        const phoneIndex = findColumnIndex(headers, ['電話', 'phone', '手機']);
        const organizationIndex = findColumnIndex(headers, ['組織', 'organization', '公司', 'company']);

        console.log('欄位索引:', { nameIndex, emailIndex, phoneIndex, organizationIndex });

        if (nameIndex === -1 || emailIndex === -1) {
            alert('找不到必要的欄位（姓名、電子郵件）。\n請確認檔案格式正確。\n\n標題行應包含：姓名, 電子郵件, 電話, 組織');
            return;
        }

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];

            if (row.length < 2) {
                console.log(`跳過第 ${i + 2} 行：資料不足`);
                continue;
            }

            const name = (row[nameIndex] || '').trim();
            const email = (row[emailIndex] || '').trim();
            const phone = phoneIndex >= 0 ? (row[phoneIndex] || '').trim() : '';
            const organization = organizationIndex >= 0 ? (row[organizationIndex] || '').trim() : '';

            console.log(`處理第 ${i + 2} 行:`, { name, email, phone, organization });

            if (!name || !email) {
                console.log(`跳過第 ${i + 2} 行：姓名或電子郵件為空`);
                continue;
            }

            // 驗證電子郵件格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                errorCount++;
                errors.push(`第 ${i + 2} 行 ${name}: 無效的電子郵件格式`);
                continue;
            }

            try {
                const response = await fetch(`http://localhost/SignAttend/backend/api/meetings/${currentMeetingId}/attendees`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8',
                    },
                    body: JSON.stringify({
                        name: name,
                        email: email,
                        phone: phone,
                        organization: organization,
                        position: ''
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    successCount++;
                    console.log(`成功匯入: ${name}`);
                    // 動態添加到表格
                    addAttendeeToTable(data.data);
                } else {
                    errorCount++;
                    errors.push(`第 ${i + 2} 行 ${name}: ${data.message}`);
                    console.log(`匯入失敗: ${name} - ${data.message}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`第 ${i + 2} 行 ${name}: 網路錯誤`);
                console.log(`網路錯誤: ${name} - ${error.message}`);
            }
        }

        // 顯示結果
        let message = `匯入完成！\n成功: ${successCount} 位\n失敗: ${errorCount} 位`;
        if (errors.length > 0 && errors.length <= 5) {
            message += '\n\n失敗原因:\n' + errors.join('\n');
        } else if (errors.length > 5) {
            message += '\n\n部分失敗原因:\n' + errors.slice(0, 5).join('\n') + `\n... 還有 ${errors.length - 5} 個錯誤`;
        }

        alert(message);

        if (successCount > 0) {
            // 更新統計（重新計算參與者數量）
            updateAttendeeStatsAfterImport();
            // 關閉彈出視窗
            closeImportModal();
        }

    } catch (error) {
        console.error('匯入錯誤:', error);
        alert('匯入過程中發生錯誤: ' + error.message);
    } finally {
        startImportBtn.disabled = false;
        startImportBtn.textContent = originalText;
    }
}

// 智能尋找欄位索引
function findColumnIndex(headers, possibleNames) {
    for (let i = 0; i < headers.length; i++) {
        const header = headers[i].toLowerCase().trim();
        for (const name of possibleNames) {
            if (header.includes(name.toLowerCase())) {
                return i;
            }
        }
    }
    return -1;
}

// 匯入後更新參與者統計
function updateAttendeeStatsAfterImport() {
    // 重新計算參與者統計
    const visibleRows = document.querySelectorAll('.attendee-row');
    const totalCount = visibleRows.length;

    console.log(`匯入後統計更新：總參與者 ${totalCount} 位`);

    // 如果頁面有統計卡片，可以在這裡更新
    // 例如更新總人數、已簽到人數等

    // 重新初始化批次操作功能
    initializeBatchOperations();
}

// ===== QR碼頁籤功能 =====
function initializeQRCodeTab() {
    const attendeeSelect = document.getElementById('attendee-select');
    const qrCodeContainer = document.getElementById('qr-code-container');
    const attendeeInfo = document.getElementById('attendee-info');
    const downloadQRBtn = document.getElementById('download-qr-btn');
    const regenerateQRBtn = document.getElementById('regenerate-qr-btn');

    if (!attendeeSelect || !qrCodeContainer) return;

    // 生成QR碼
    function generateQRCode(attendeeId) {
        const selectedOption = attendeeSelect.querySelector(`option[value="${attendeeId}"]`);
        if (!selectedOption) return;

        const attendeeName = selectedOption.textContent;
        const meetingId = getCurrentMeetingId();

        // 構建QR碼數據
        const qrData = meetingId ? `${attendeeId}:${meetingId}:${attendeeName}` : `${attendeeId}:${attendeeName}`;
        const checkInCode = attendeeId.slice(-6).toUpperCase();

        // 清空容器
        qrCodeContainer.innerHTML = '';

        // 檢查QRCode庫是否已加載，如果沒有則顯示QR碼資訊
        if (typeof QRCode === 'undefined') {
            console.warn('QRCode庫未加載，顯示QR碼資訊');

            // 顯示QR碼資訊而不是實際的QR碼圖像
            qrCodeContainer.innerHTML = `
                <div class="w-64 h-64 bg-blue-50 border-2 border-blue-300 rounded-lg flex items-center justify-center">
                    <div class="text-center p-4">
                        <svg class="mx-auto h-16 w-16 text-blue-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <rect width="5" height="5" x="3" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="16" y="3" rx="1"></rect>
                            <rect width="5" height="5" x="3" y="16" rx="1"></rect>
                            <path d="M21 16h-3a2 2 0 0 0-2 2v3"></path>
                            <path d="M21 21v.01"></path>
                            <path d="M12 7v3a2 2 0 0 0 2 2h3"></path>
                            <path d="M3 12h.01"></path>
                            <path d="M12 3h.01"></path>
                            <path d="M12 16v.01"></path>
                            <path d="M16 12h1"></path>
                            <path d="M21 12v.01"></path>
                            <path d="M12 21v-1"></path>
                        </svg>
                        <p class="text-sm font-medium text-blue-700 mb-2">QR 碼資訊</p>
                        <p class="text-xs text-blue-600 break-all">${qrData}</p>
                        <p class="text-xs text-gray-500 mt-2">請使用QR碼掃描器掃描此資訊</p>
                    </div>
                </div>
            `;
            return;
        }

        // 使用QRCode.js生成QR碼
        try {
            // 創建canvas元素來顯示QR碼
            const canvas = document.createElement('canvas');
            canvas.className = 'mx-auto border rounded';
            qrCodeContainer.appendChild(canvas);

            // 使用QRCode.js生成QR碼
            QRCode.toCanvas(canvas, qrData, {
                width: 256,
                height: 256,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('QR碼生成失敗:', error);
                    qrCodeContainer.innerHTML = `
                        <div class="w-64 h-64 bg-red-50 border-2 border-dashed border-red-300 flex items-center justify-center">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p class="mt-2 text-sm text-red-500">QR碼生成失敗</p>
                                <p class="text-xs text-red-400">${error.message || '未知錯誤'}</p>
                            </div>
                        </div>
                    `;
                } else {
                    console.log('QR碼生成成功');
                }
            });

            // 更新參與者資訊
            if (attendeeInfo) {
                attendeeInfo.innerHTML = `
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700">姓名:</span>
                            <span class="text-sm text-gray-900">${attendeeName}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700">報到代碼:</span>
                            <span class="text-sm font-mono text-blue-600">${checkInCode}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-700">QR碼數據:</span>
                            <span class="text-xs text-gray-500 break-all">${qrData}</span>
                        </div>
                    </div>
                `;
            }

        } catch (error) {
            console.error('QR碼生成失敗:', error);
            qrCodeContainer.innerHTML = '<p class="text-red-500">QR碼生成失敗</p>';
        }
    }

    // 參與者選擇變更事件
    if (attendeeSelect) {
        attendeeSelect.addEventListener('change', function() {
            const selectedId = this.value;
            if (selectedId) {
                generateQRCode(selectedId);
            }
        });

        // 初始化時生成第一個參與者的QR碼
        if (attendeeSelect.options.length > 0) {
            generateQRCode(attendeeSelect.value);
        }
    }

    // 下載QR碼按鈕
    if (downloadQRBtn) {
        downloadQRBtn.addEventListener('click', function() {

            const selectedId = attendeeSelect.value;
            const selectedOption = attendeeSelect.querySelector(`option[value="${selectedId}"]`);

            if (!selectedOption) {
                alert('請先選擇參與者');
                return;
            }

            const attendeeName = selectedOption.textContent;
            const meetingId = getCurrentMeetingId();
            const qrData = meetingId ? `${selectedId}:${meetingId}:${attendeeName}` : `${selectedId}:${attendeeName}`;
            const checkInCode = selectedId.slice(-6).toUpperCase();

            // 創建一個更大的canvas來包含QR碼和文字資訊
            const downloadCanvas = document.createElement('canvas');
            const ctx = downloadCanvas.getContext('2d');

            // 設定canvas尺寸
            const qrSize = 300;
            const canvasWidth = qrSize + 40; // 左右各留20px
            const canvasHeight = qrSize + 120; // 下方留120px給文字
            downloadCanvas.width = canvasWidth;
            downloadCanvas.height = canvasHeight;

            // 設定背景為白色
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);

            // 檢查是否有QRCode庫
            if (typeof QRCode !== 'undefined') {
                // 使用QRCode.js生成
                QRCode.toCanvas(document.createElement('canvas'), qrData, {
                    width: qrSize,
                    height: qrSize,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error, canvas) {
                    if (error) {
                        alert('QR碼生成失敗：' + error.message);
                        return;
                    }

                    // 將QR碼繪製到下載canvas上
                    ctx.drawImage(canvas, 20, 10);

                    // 添加文字資訊並下載
                    addTextAndDownload();
                });
            } else {
                // QRCode庫不可用，下載QR碼資訊文字檔
                const qrInfo = `參與者QR碼資訊

參與者姓名: ${attendeeName}
報到代碼: ${checkInCode}
QR碼數據: ${qrData}
會議ID: ${meetingId || '無'}
生成時間: ${new Date().toLocaleString()}

使用說明:
1. 請使用QR碼生成器將「QR碼數據」轉換為QR碼
2. 或者直接使用「報到代碼」進行手動報到
`;

                const blob = new Blob([qrInfo], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.download = `qr-info-${attendeeName}.txt`;
                link.href = url;
                link.click();
                URL.revokeObjectURL(url);

                alert(`${attendeeName} 的 QR 碼資訊已下載為文字檔！`);
                return;
            }

            // 添加文字資訊並下載的函數
            function addTextAndDownload() {
                // 添加文字資訊
                ctx.fillStyle = '#000000';
                ctx.textAlign = 'center';

                // 參與者姓名
                ctx.font = 'bold 18px Arial';
                ctx.fillText(attendeeName, canvasWidth / 2, qrSize + 35);

                // 報到碼
                ctx.font = 'bold 16px Arial';
                ctx.fillText(`報到碼: ${checkInCode}`, canvasWidth / 2, qrSize + 60);

                // 會議資訊（如果有）
                if (meetingId) {
                    ctx.font = '12px Arial';
                    ctx.fillText(`會議ID: ${meetingId}`, canvasWidth / 2, qrSize + 80);
                }

                // 下載圖片
                downloadCanvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.download = `qr-${attendeeName}.png`;
                    link.href = url;
                    link.click();
                    URL.revokeObjectURL(url);

                    alert(`${attendeeName} 的 QR 碼已下載！`);
                });
            }
        });
    }

    // 重新生成QR碼按鈕
    if (regenerateQRBtn) {
        regenerateQRBtn.addEventListener('click', function() {
            const selectedId = attendeeSelect.value;
            if (selectedId) {
                generateQRCode(selectedId);
                alert('QR碼已重新生成');
            }
        });
    }
}

// ===== 報到頁籤功能 =====
document.addEventListener('DOMContentLoaded', function() {
    const qrScanBtn = document.getElementById('qr-scan-btn');
    const scanBtnText = document.getElementById('scan-btn-text');
    const qrScannerContainer = document.getElementById('qr-scanner-container');
    const stopScanBtn = document.getElementById('stop-scan-btn');
    const switchCameraBtn = document.getElementById('switch-camera-btn');
    const checkinCodeInput = document.getElementById('checkin-code');
    const manualCheckinBtn = document.getElementById('manual-checkin-btn');

    let html5QrCode = null;
    let isScanning = false;
    let cameras = [];
    let currentCameraIndex = 0;

    // QR掃描按鈕
    if (qrScanBtn) {
        qrScanBtn.addEventListener('click', function() {
            if (isScanning) {
                stopQRScanning();
            } else {
                startQRScanning();
            }
        });
    }

    // 停止掃描按鈕
    if (stopScanBtn) {
        stopScanBtn.addEventListener('click', stopQRScanning);
    }

    // 切換攝像頭按鈕
    if (switchCameraBtn) {
        switchCameraBtn.addEventListener('click', switchCamera);
    }

    // 開始QR掃描
    function startQRScanning() {
        // 檢查Html5Qrcode庫是否已加載
        if (typeof Html5Qrcode === 'undefined') {
            // 顯示加載提示
            scanBtnText.textContent = '庫加載中...';
            qrScanBtn.disabled = true;

            // 延遲重試
            setTimeout(() => {
                if (typeof Html5Qrcode !== 'undefined') {
                    console.log('Html5Qrcode庫延遲加載成功');
                    scanBtnText.textContent = '掃描 QR 碼';
                    qrScanBtn.disabled = false;
                    startQRScanning();
                } else {
                    console.log('Html5Qrcode庫加載失敗，使用簡化版本');
                    scanBtnText.textContent = '掃描 QR 碼';
                    qrScanBtn.disabled = false;
                    // 提供簡化的掃描方案
                    startSimpleQRScanning();
                }
            }, 2000);
            return;
        }

        if (isScanning) return;

        // 初始化QR掃描器
        html5QrCode = new Html5Qrcode("qr-reader");

        // 獲取攝像頭列表
        Html5Qrcode.getCameras().then(devices => {
            if (devices && devices.length) {
                cameras = devices;
                currentCameraIndex = 0;

                // 開始掃描
                startScanningWithCamera(cameras[currentCameraIndex].id);
            } else {
                alert('未找到可用的攝像頭');
            }
        }).catch(err => {
            console.error('獲取攝像頭失敗:', err);
            alert('無法訪問攝像頭，請確保已授權攝像頭權限');
        });
    }

    // 使用指定攝像頭開始掃描
    function startScanningWithCamera(cameraId) {
        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0
        };

        html5QrCode.start(
            cameraId,
            config,
            (decodedText, decodedResult) => {
                // QR碼掃描成功
                console.log('QR碼掃描成功:', decodedText);
                handleQRCodeScanned(decodedText);
            },
            (errorMessage) => {
                // 掃描錯誤（通常是沒有找到QR碼，可以忽略）
                // console.log('掃描中...', errorMessage);
            }
        ).then(() => {
            // 掃描開始成功
            isScanning = true;
            qrScannerContainer.classList.remove('hidden');
            scanBtnText.textContent = '停止掃描';
            qrScanBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            qrScanBtn.classList.add('bg-red-600', 'hover:bg-red-700');

            // 更新切換攝像頭按鈕狀態
            if (cameras.length > 1) {
                switchCameraBtn.disabled = false;
                switchCameraBtn.textContent = `攝像頭 ${currentCameraIndex + 1}/${cameras.length}`;
            } else {
                switchCameraBtn.disabled = true;
                switchCameraBtn.textContent = '僅一個攝像頭';
            }
        }).catch(err => {
            console.error('開始掃描失敗:', err);
            alert('開始掃描失敗：' + err.message);
        });
    }

    // 停止QR掃描
    function stopQRScanning() {
        if (!isScanning || !html5QrCode) return;

        html5QrCode.stop().then(() => {
            // 掃描停止成功
            isScanning = false;
            qrScannerContainer.classList.add('hidden');
            scanBtnText.textContent = '掃描 QR 碼';
            qrScanBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
            qrScanBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');

            // 清理
            html5QrCode = null;
        }).catch(err => {
            console.error('停止掃描失敗:', err);
        });
    }

    // 切換攝像頭
    function switchCamera() {
        if (!isScanning || cameras.length <= 1) return;

        // 停止當前掃描
        html5QrCode.stop().then(() => {
            // 切換到下一個攝像頭
            currentCameraIndex = (currentCameraIndex + 1) % cameras.length;

            // 使用新攝像頭開始掃描
            startScanningWithCamera(cameras[currentCameraIndex].id);
        }).catch(err => {
            console.error('切換攝像頭失敗:', err);
        });
    }

    // 處理掃描到的QR碼
    function handleQRCodeScanned(qrData) {
        // 停止掃描
        stopQRScanning();

        // 解析QR碼數據
        // 預期格式: attendeeId:meetingId:attendeeName 或 attendeeId:attendeeName
        const parts = qrData.split(':');

        if (parts.length < 2) {
            alert('無效的QR碼格式');
            return;
        }

        const attendeeId = parts[0];
        const meetingId = parts.length >= 3 ? parts[1] : null;
        const attendeeName = parts.length >= 3 ? parts[2] : parts[1];

        // 確認報到
        const confirmMessage = `確認為以下參與者報到？\n\n姓名: ${attendeeName}\n參與者ID: ${attendeeId}${meetingId ? '\n會議ID: ' + meetingId : ''}`;

        if (confirm(confirmMessage)) {
            // 執行報到操作
            performCheckIn(attendeeId, attendeeName, 'qr');
        }
    }

    // 手動報到按鈕
    if (manualCheckinBtn && checkinCodeInput) {
        manualCheckinBtn.addEventListener('click', function() {
            const code = checkinCodeInput.value.trim().toUpperCase();

            if (!code) {
                alert('請輸入報到代碼');
                return;
            }

            if (code.length !== 6) {
                alert('報到代碼必須是6位數');
                return;
            }

            // 執行報到操作
            performCheckInByCode(code);
        });
    }

    // 執行報到操作（通用函數）
    function performCheckIn(attendeeId, attendeeName, method) {
        const currentMeetingId = getCurrentMeetingId();

        if (!currentMeetingId) {
            alert('請先選擇一個會議');
            return;
        }

        // 發送報到請求到後端 - 使用正確的API端點
        const requestData = {
            signature_data: null, // 可以後續添加簽名功能
            method: method // 'qr' 或 'manual'
        };

        fetch(`http://localhost/SignAttend/backend/api/attendees/${attendeeId}/checkin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert(`報到成功！\n\n參與者: ${attendeeName}\n時間: ${new Date().toLocaleString()}`);

                // 清空輸入框
                if (checkinCodeInput) {
                    checkinCodeInput.value = '';
                }

                // 如果在參與者頁籤，更新該參與者的狀態
                updateAttendeeStatusInTable(attendeeId, true);

                // 更新統計數據
                updateStatisticsAfterCheckIn();

            } else {
                alert('報到失敗：' + (data.message || '未知錯誤'));
            }
        })
        .catch(error => {
            console.error('報到請求失敗:', error);
            alert('報到失敗：網路錯誤');
        });
    }

    // 通過代碼執行報到
    function performCheckInByCode(code) {
        const currentMeetingId = getCurrentMeetingId();

        if (!currentMeetingId) {
            alert('請先選擇一個會議');
            return;
        }

        // 發送請求查找對應的參與者
        fetch(`http://localhost/SignAttend/backend/simple_attendee_api.php?meeting_id=${currentMeetingId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data && data.data.length > 0) {
                // 在前端查找匹配的參與者（通過ID的後6位）
                const matchingAttendee = data.data.find(attendee => {
                    const attendeeCode = attendee.id.toString().slice(-6).toUpperCase();
                    return attendeeCode === code;
                });

                if (matchingAttendee) {
                    // 確認報到
                    const confirmMessage = `找到參與者，確認報到？\n\n姓名: ${matchingAttendee.name}\n電子郵件: ${matchingAttendee.email}\n報到代碼: ${code}`;

                    if (confirm(confirmMessage)) {
                        performCheckIn(matchingAttendee.id, matchingAttendee.name, 'manual');
                    }
                } else {
                    alert('找不到對應的參與者，請檢查報到代碼是否正確');
                }
            } else {
                alert('無法獲取參與者列表或會議中沒有參與者');
            }
        })
        .catch(error => {
            console.error('查找參與者失敗:', error);
            alert('查找參與者失敗：網路錯誤');
        });
    }

    // 更新參與者表格中的狀態
    function updateAttendeeStatusInTable(attendeeId, checkedIn) {
        const row = document.querySelector(`[data-attendee-id="${attendeeId}"]`);
        if (row) {
            const statusCell = row.querySelector('td:nth-child(5)'); // 報到狀態列
            if (statusCell && checkedIn) {
                statusCell.innerHTML = `
                    <div class="flex items-center gap-2">
                        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-100 text-green-800">
                            <span class="flex items-center gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3">
                                    <path d="M20 6 9 17l-5-5"></path>
                                </svg>
                                已報到
                            </span>
                        </div>
                    </div>
                `;
            }
        }
    }

    // 報到成功後更新統計數據
    function updateStatisticsAfterCheckIn() {
        const currentMeetingId = getCurrentMeetingId();

        if (!currentMeetingId) {
            console.log('沒有選擇會議，跳過統計更新');
            return;
        }

        // 發送請求獲取最新的統計數據
        fetch(`http://localhost/SignAttend/backend/simple_attendee_api.php?meeting_id=${currentMeetingId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data) {
                const attendees = data.data;
                const totalAttendees = attendees.length;
                const checkedInAttendees = attendees.filter(attendee => attendee.checked_in).length;
                const checkedInPercentage = totalAttendees > 0 ? Math.round((checkedInAttendees / totalAttendees) * 100) : 0;

                // 更新統計卡片
                updateStatisticsCards(totalAttendees, checkedInAttendees, checkedInPercentage);

                // 更新頁面標題中的徽章
                updateAttendeesBadges(totalAttendees, checkedInAttendees);

                console.log(`統計更新完成：總參與者 ${totalAttendees} 位，已報到 ${checkedInAttendees} 位 (${checkedInPercentage}%)`);
            }
        })
        .catch(error => {
            console.error('更新統計數據失敗:', error);
        });
    }

    // 更新統計卡片
    function updateStatisticsCards(totalAttendees, checkedInAttendees, checkedInPercentage) {
        // 查找統計卡片容器
        const statsCards = document.querySelectorAll('.rounded-lg.border.bg-white.text-gray-900.shadow-sm');

        // 更新總參與者卡片（第一個卡片）
        if (statsCards.length >= 1) {
            const totalCard = statsCards[0];
            const totalElement = totalCard.querySelector('.text-2xl.font-bold');
            if (totalElement && totalElement.textContent !== totalAttendees.toString()) {
                totalElement.textContent = totalAttendees;
                totalCard.classList.add('stats-update');
                setTimeout(() => totalCard.classList.remove('stats-update'), 600);
            }
        }

        // 更新已報到卡片（第二個卡片）
        if (statsCards.length >= 2) {
            const checkedInCard = statsCards[1];
            const checkedInElement = checkedInCard.querySelector('.text-2xl.font-bold');
            const percentageElement = checkedInCard.querySelector('.text-xs.text-gray-600');

            if (checkedInElement && checkedInElement.textContent !== checkedInAttendees.toString()) {
                // 更新數字
                checkedInElement.textContent = checkedInAttendees;
                checkedInElement.classList.add('number-update');

                // 更新百分比
                if (percentageElement) {
                    percentageElement.textContent = `${checkedInPercentage}% 的總參與者`;
                }

                // 添加高亮動畫
                checkedInCard.classList.add('stats-update');
                checkedInElement.style.color = '#16a34a'; // 綠色

                setTimeout(() => {
                    checkedInCard.classList.remove('stats-update');
                    checkedInElement.classList.remove('number-update');
                    checkedInElement.style.color = '';
                }, 600);
            }
        }

        // 更新QR碼卡片（第三個卡片）- 通常QR碼數量等於總參與者數量
        if (statsCards.length >= 3) {
            const qrCard = statsCards[2];
            const qrElement = qrCard.querySelector('.text-2xl.font-bold');
            if (qrElement && qrElement.textContent !== totalAttendees.toString()) {
                qrElement.textContent = totalAttendees;
            }
        }
    }

    // 更新參與者頁籤中的徽章
    function updateAttendeesBadges(totalAttendees, checkedInAttendees) {
        // 更新總計徽章
        const totalBadge = document.querySelector('.text-gray-900.font-normal');
        if (totalBadge && totalBadge.textContent.includes('總計')) {
            totalBadge.textContent = `總計 ${totalAttendees} 位`;
        }

        // 更新已報到徽章
        const checkedInBadge = document.querySelector('.bg-green-100.text-green-800.font-normal');
        if (checkedInBadge && checkedInBadge.textContent.includes('已報到')) {
            checkedInBadge.textContent = `已報到 ${checkedInAttendees} 位`;
            // 添加閃爍效果
            checkedInBadge.style.animation = 'pulse 0.5s ease-in-out';
            setTimeout(() => {
                checkedInBadge.style.animation = '';
            }, 500);
        }
    }

    // 報到代碼輸入框回車事件
    if (checkinCodeInput) {
        checkinCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                manualCheckinBtn.click();
            }
        });

        // 限制輸入為6位數字母
        checkinCodeInput.addEventListener('input', function() {
            let value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
            if (value.length > 6) {
                value = value.substring(0, 6);
            }
            this.value = value;
        });
    }
});
</script>

<!-- 使用不同的CDN來加載庫 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html5-qrcode/2.3.8/html5-qrcode.min.js"></script>

<!-- 備用CDN -->
<script>
// 檢查庫加載狀態並提供備用方案
window.addEventListener('load', function() {
    console.log('QRCode庫狀態:', typeof QRCode !== 'undefined' ? '已加載' : '未加載');
    console.log('Html5Qrcode庫狀態:', typeof Html5Qrcode !== 'undefined' ? '已加載' : '未加載');

    // 如果主要庫沒有加載，嘗試備用CDN
    if (typeof QRCode === 'undefined') {
        console.log('嘗試加載QRCode備用CDN...');
        const script1 = document.createElement('script');
        script1.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js';
        script1.onload = function() {
            console.log('QRCode備用CDN加載成功');
        };
        script1.onerror = function() {
            console.log('QRCode備用CDN加載失敗');
        };
        document.head.appendChild(script1);
    }

    if (typeof Html5Qrcode === 'undefined') {
        console.log('嘗試加載Html5Qrcode備用CDN...');
        const script2 = document.createElement('script');
        script2.src = 'https://cdn.jsdelivr.net/npm/html5-qrcode@2.3.8/minified/html5-qrcode.min.js';
        script2.onload = function() {
            console.log('Html5Qrcode備用CDN加載成功');
        };
        script2.onerror = function() {
            console.log('Html5Qrcode備用CDN加載失敗');
        };
        document.head.appendChild(script2);
    }

    // 簡化的QR掃描方案（文件上傳）
    window.startSimpleQRScanning = function() {
        // 創建文件輸入元素
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.capture = 'environment'; // 優先使用後置攝像頭

        fileInput.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                // 顯示處理中狀態
                const scanBtnText = document.getElementById('scan-btn-text');
                const qrScanBtn = document.getElementById('qr-scan-btn');

                if (scanBtnText) scanBtnText.textContent = '處理中...';
                if (qrScanBtn) qrScanBtn.disabled = true;

                // 創建FileReader來讀取圖片
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 創建圖片元素
                    const img = new Image();
                    img.onload = function() {
                        // 提供手動輸入的選項
                        const qrData = prompt('請輸入QR碼中的文字內容（或6位報到代碼）：\n\n如果是QR碼，格式通常為：參與者ID:會議ID:姓名\n如果是報到代碼，請輸入6位代碼');

                        if (scanBtnText) scanBtnText.textContent = '掃描 QR 碼';
                        if (qrScanBtn) qrScanBtn.disabled = false;

                        if (qrData && qrData.trim()) {
                            const trimmedData = qrData.trim();
                            if (trimmedData.length === 6 && /^[A-Z0-9]+$/i.test(trimmedData)) {
                                // 看起來像報到代碼
                                if (typeof performCheckInByCode === 'function') {
                                    performCheckInByCode(trimmedData.toUpperCase());
                                } else {
                                    alert('報到功能暫時不可用');
                                }
                            } else {
                                // 看起來像QR碼數據
                                if (typeof handleQRCodeScanned === 'function') {
                                    handleQRCodeScanned(trimmedData);
                                } else {
                                    alert('QR碼處理功能暫時不可用');
                                }
                            }
                        }
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        };

        // 觸發文件選擇
        fileInput.click();
    };
});
</script>

</body>
</html>