<?php
// 完全獨立的測試文件 - 不載入任何配置
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>直接測試</title>
    <style>
        body { font-family: Arial; margin: 40px; background: #f0f0f0; }
        .box { background: white; padding: 30px; border-radius: 10px; max-width: 600px; margin: 0 auto; }
        .success { color: #28a745; font-size: 24px; font-weight: bold; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="box">
        <div class="success">✅ 測試成功！</div>
        <p>如果您看到這個頁面，表示：</p>
        <ul>
            <li>PHP 正常運行</li>
            <li>網站可以正常訪問</li>
            <li>沒有重定向循環問題</li>
        </ul>
        
        <div class="info">
            <strong>當前狀態：</strong><br>
            時間: <?= date('Y-m-d H:i:s') ?><br>
            PHP: <?= phpversion() ?><br>
            伺服器: <?= $_SERVER['HTTP_HOST'] ?? 'Unknown' ?><br>
            協議: <?= isset($_SERVER['HTTPS']) ? 'HTTPS' : 'HTTP' ?>
        </div>
        
        <p><strong>下一步：</strong></p>
        <ol>
            <li>按照上面的方法修復重定向問題</li>
            <li>修復後測試其他頁面</li>
            <li>確認資料庫連接正常</li>
        </ol>
        
        <p style="color: #666; font-size: 14px;">
            這是獨立測試文件，不會觸發任何重定向。
        </p>
    </div>
</body>
</html>
