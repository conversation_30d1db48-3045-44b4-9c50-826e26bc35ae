<?php
// 測試日誌寫入權限
echo "=== 日誌寫入權限測試 ===\n";

// 1. 確定外層 logs 目錄路徑
$outerLogsDir = dirname(dirname(__FILE__)) . '/logs';
echo "外層 logs 目錄: " . $outerLogsDir . "\n";

// 2. 檢查目錄是否存在
if (is_dir($outerLogsDir)) {
    echo "✅ 外層 logs 目錄存在\n";
} else {
    echo "❌ 外層 logs 目錄不存在\n";
    echo "嘗試創建目錄...\n";
    if (mkdir($outerLogsDir, 0755, true)) {
        echo "✅ 外層 logs 目錄創建成功\n";
    } else {
        echo "❌ 外層 logs 目錄創建失敗\n";
    }
}

// 3. 檢查目錄權限
if (is_writable($outerLogsDir)) {
    echo "✅ 外層 logs 目錄可寫\n";
} else {
    echo "❌ 外層 logs 目錄不可寫\n";
    echo "目錄權限: " . substr(sprintf('%o', fileperms($outerLogsDir)), -4) . "\n";
}

// 4. 嘗試寫入測試文件
$testFile = $outerLogsDir . '/test_permissions_' . date('Y-m-d_H-i-s') . '.log';
$content = date('Y-m-d H:i:s') . " - 權限測試\n";

echo "嘗試寫入測試文件: " . $testFile . "\n";
if (file_put_contents($testFile, $content)) {
    echo "✅ 測試文件寫入成功\n";
    echo "文件大小: " . filesize($testFile) . " bytes\n";
} else {
    echo "❌ 測試文件寫入失敗\n";
}

// 5. 檢查 PHP 進程用戶
echo "PHP 進程用戶: " . exec('whoami') . "\n";
echo "PHP 進程 ID: " . getmypid() . "\n";

// 6. 檢查 PHP 錯誤日誌配置
echo "PHP 錯誤日誌路徑: " . ini_get('error_log') . "\n";
echo "PHP 顯示錯誤: " . (ini_get('display_errors') ? '開啟' : '關閉') . "\n";
echo "PHP 錯誤報告級別: " . ini_get('error_reporting') . "\n";

// 7. 嘗試使用 error_log 函數
error_log("測試 PHP error_log 函數", 3, $outerLogsDir . '/php_error_test.log');
echo "已嘗試使用 error_log 函數寫入\n";

// 8. 列出目錄內容
echo "\n外層 logs 目錄內容:\n";
$files = glob($outerLogsDir . '/*');
if (empty($files)) {
    echo "目錄為空\n";
} else {
    foreach ($files as $file) {
        $filename = basename($file);
        $size = filesize($file);
        $mtime = date('Y-m-d H:i:s', filemtime($file));
        echo "- " . $filename . " (大小: " . $size . " bytes, 修改: " . $mtime . ")\n";
    }
}

echo "\n測試完成！\n";
?>