[2025-06-26 19:35:37] === DIRECT TEST AJAX 開始 ===
[2025-06-26 19:35:43] POST 數據: {"ajax_login":"1","email":"<EMAIL>","password":"test123","remember_me":"0"}
[2025-06-26 19:35:43] Email: <EMAIL>, Password: [有值]
[2025-06-26 19:35:43] 認證日誌文件: V:\attendance.app.tn/logs/auth_2025-06-26.log
[2025-06-26 19:35:43] 登入前大小: 7595 bytes
[2025-06-26 19:35:43] 開始執行 auth->login()
[2025-06-26 19:35:48] auth->login() 完成，結果: {"success":false,"message":"\u96fb\u5b50\u90f5\u4ef6\u6216\u5bc6\u78bc\u932f\u8aa4"}
[2025-06-26 19:35:51] 登入後大小: 7842 bytes
[2025-06-26 19:35:54] 登入失敗: 電子郵件或密碼錯誤
[2025-06-26 19:35:56] 返回響應: {"success":false,"message":"\u96fb\u5b50\u90f5\u4ef6\u6216\u5bc6\u78bc\u932f\u8aa4","redirect":"","debug_info":{"before_size":7595,"after_size":7842,"auth_log_file":"V:\\attendance.app.tn\/logs\/auth_2025-06-26.log","log_path":"V:\\attendance.app.tn\/logs"}}
[2025-06-26 19:35:59] === DIRECT TEST AJAX 結束 ===
