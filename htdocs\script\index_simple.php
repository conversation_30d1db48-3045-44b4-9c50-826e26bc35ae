<?php
/**
 * SignAttend PHP Frontend 簡化版主入口點
 */

// 啟動 Session
session_start();

// 基本配置
define('APP_NAME', 'SignAttend');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://localhost/SignAttend/frontend');

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= APP_NAME ?> - 智慧簽到系統</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <!-- 導航列 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-check-circle-fill me-2"></i>
                <?= APP_NAME ?>
            </a>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <!-- Hero Section -->
                <div class="jumbotron bg-light p-5 rounded-3 mb-4">
                    <div class="container-fluid py-5">
                        <h1 class="display-5 fw-bold">歡迎使用 <?= APP_NAME ?></h1>
                        <p class="col-md-8 fs-4">智慧簽到系統，讓會議管理更簡單、更有效率。</p>
                        
                        <div class="mt-4">
                            <a class="btn btn-primary btn-lg me-3" href="pages/register.php" role="button">
                                <i class="bi bi-person-plus me-2"></i>立即註冊
                            </a>
                            <a class="btn btn-outline-secondary btn-lg" href="pages/login.php" role="button">
                                <i class="bi bi-box-arrow-in-right me-2"></i>會員登入
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 功能特色 -->
                <div class="row g-4 mb-5">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-qr-code display-4 text-primary mb-3"></i>
                                <h5 class="card-title">QR Code 簽到</h5>
                                <p class="card-text">快速掃描 QR Code 完成簽到，支援批量生成個人專屬簽到碼。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-pen display-4 text-success mb-3"></i>
                                <h5 class="card-title">電子簽名</h5>
                                <p class="card-text">支援手寫電子簽名，確保簽到的真實性和法律效力。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up display-4 text-info mb-3"></i>
                                <h5 class="card-title">即時統計</h5>
                                <p class="card-text">即時查看簽到狀況，匯出參與者名單和簽到報表。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系統狀態 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle me-2"></i>系統狀態
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>系統版本：</strong><?= APP_VERSION ?></p>
                                        <p><strong>PHP 版本：</strong><?= phpversion() ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>當前時間：</strong><?= date('Y-m-d H:i:s') ?></p>
                                        <p><strong>狀態：</strong><span class="badge bg-success">正常運行</span></p>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <h6>測試連結：</h6>
                                    <ul>
                                        <li><a href="test.php">PHP 功能測試</a></li>
                                        <li><a href="index.php">完整版首頁</a></li>
                                        <li><a href="../backend/api/test">後端 API 測試</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 頁尾 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= APP_NAME ?></h5>
                    <p class="mb-0">智慧簽到系統 v<?= APP_VERSION ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; <?= date('Y') ?> SignAttend Team. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
